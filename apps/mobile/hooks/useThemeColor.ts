/**
 * Learn more about light and dark modes:
 * https://docs.expo.dev/guides/color-schemes/
 */

import { Colors } from '@/constants/Colors';

export type ColorName = keyof typeof Colors.light;
export type ThemeProps = {
  light: string;
  dark?: string; // Make dark optional since we'll only use light
};

export function useThemeColor(
  props: ThemeProps,
  colorName: string
): string {
  // Always use light theme regardless of system setting
  const theme = 'light';
  const colorFromProps = props[theme];

  if (colorFromProps) {
    return colorFromProps;
  } else {
    return Colors[theme][colorName as ColorName];
  }
}
