# Refactoring to a PNPM Workspace: A Detailed Plan

This document outlines a new, step-by-step plan to migrate your existing Expo React Native project into a scalable monorepo using **PNPM Workspaces** and **Vite** for web applications.

## 1. Why PNPM Workspaces? The Benefits

Using PNPM workspaces is a powerful yet lightweight approach to building a monorepo.

-   **Simplicity**: Less configuration and abstraction compared to systems like Nx.
-   **Performance**: PNPM's unique non-flat `node_modules` structure is extremely efficient and fast.
-   **Excellent Dependency Management**: Avoids phantom dependencies and ensures each package has explicit access to its dependencies.
-   **Flexibility**: You bring your own tools (like Vite, TypeScript, ESLint) and configure them as you see fit.

## 2. The Migration Strategy

Our strategy is to build the monorepo from the ground up, ensuring a clean and logical structure.

1.  **Initialize a PNPM Workspace**: Create a root `package.json` and a `pnpm-workspace.yaml` file.
2.  **Migrate the Expo App**: Move the existing Expo application into an `apps` directory.
3.  **Centralize Dependencies**: Consolidate all dependencies into the root `package.json`.
4.  **Create Shared Packages**: Establish shared libraries for code that will be used across multiple applications.
5.  **Add New Vite & NestJS Apps**: Scaffold the new applications within the workspace.

---

## 3. Step-by-Step Implementation Plan

### Step 1: Initialize the PNPM Workspace

First, we'll set up the monorepo structure.

1.  **Create a new root directory** for your monorepo and navigate into it:
    ```bash
    mkdir invoice-go-workspace
    cd invoice-go-workspace
    ```
2.  **Create a root `package.json`**:
    ```bash
    pnpm init
    ```
    You can edit the details later. Add `"private": true` to this file to prevent accidental publishing of the workspace root.

3.  **Create `pnpm-workspace.yaml`**: This file tells PNPM where to find the projects (packages) in your workspace.
    ```yaml
    # pnpm-workspace.yaml
    packages:
      - 'apps/*'
      - 'packages/*'
    ```
4.  **Create the directory structure**:
    ```bash
    mkdir apps packages
    ```

### Step 2: Migrate the Existing Expo App

Now, let's move your Expo project into the new workspace.

1.  **Create a directory** for your Expo app within the `apps` folder:
    ```bash
    mkdir apps/expo-app
    ```
2.  **Copy your project files**: Move all files and folders from your original `invoice-go-expo` project into `apps/expo-app`.
    -   **DO NOT COPY**: `node_modules`, `package.json`, or any lock files (`pnpm-lock.yaml`).
3.  **Create a `package.json` for the Expo app** inside `apps/expo-app`:
    ```json
    // apps/expo-app/package.json
    {
      "name": "expo-app",
      "version": "1.0.0",
      "main": "expo-router/entry",
      "scripts": {
        "start": "expo start",
        "android": "expo run:android",
        "ios": "expo run:ios",
        "web": "expo start --web"
      }
    }
    ```
    This file is lean because all dependencies will be managed from the root.

### Step 3: Centralize and Install Dependencies

1.  **Merge Dependencies**: Manually copy all `dependencies` and `devDependencies` from your old `package.json` into the root `package.json` of the new workspace.
2.  **Install all dependencies** from the root of the workspace:
    ```bash
    pnpm install
    ```
    PNPM will install all dependencies in the root `node_modules` and create symbolic links for each workspace package.

### Step 4: Create Shared Packages

This is where the power of the monorepo shines.

1.  **Create package directories**:
    ```bash
    mkdir packages/shared-types packages/shared-ui packages/shared-utils
    ```
2.  **Initialize each package**: For each new directory (e.g., `packages/shared-types`), create a `package.json` and a `tsconfig.json`.

    **Example for `shared-types`**:
    ```json
    // packages/shared-types/package.json
    {
      "name": "@invoice-go/shared-types",
      "version": "1.0.0",
      "main": "src/index.ts",
      "types": "src/index.ts"
    }
    ```
    ```typescript
    // packages/shared-types/src/index.ts
    // Export your Zod schemas, TypeScript types, etc. here
    export type User = {
      id: string;
      name: string;
    };
    ```

3.  **Configure TypeScript for Monorepo**:
    -   Create a root `tsconfig.base.json` to define shared compiler options.
    -   In each app and package, create a `tsconfig.json` that extends the base configuration and adds path aliases for workspace packages.

    ```json
    // tsconfig.base.json (at the root)
    {
      "compilerOptions": {
        "paths": {
          "@invoice-go/*": ["./packages/*/src"]
        }
      }
    }
    ```
    Your Expo app and other apps will need to extend this to resolve shared packages.

### Step 5: Add New Applications with Vite

1.  **Add React Web Apps with Vite**:
    -   Navigate to the `apps` directory: `cd apps`.
    -   Run the Vite scaffolding command for each app:
        ```bash
        pnpm create vite web-portal --template react-ts
        pnpm create vite invoice-builder --template react-ts
        ```
    -   Each new Vite app will have its own `package.json`. You'll need to update it to use workspace dependencies. For example:
        ```json
        // apps/invoice-builder/package.json
        "dependencies": {
          "react": "^18.2.0",
          "@invoice-go/shared-types": "workspace:*"
        }
        ```

2.  **Add the NestJS Server**:
    -   Navigate to the `apps` directory: `cd apps`.
    -   Use the NestJS CLI to create the server:
        ```bash
        npx @nestjs/cli new api
        ```
    -   This will create an `apps/api` directory with a standard NestJS project.

### Step 6: Define Workspace Scripts

In your **root `package.json`**, add scripts to manage the entire workspace.

```json
// package.json (at the root)
"scripts": {
  "dev:expo": "pnpm --filter expo-app start",
  "dev:web-portal": "pnpm --filter web-portal dev",
  "dev:invoice-builder": "pnpm --filter invoice-builder dev",
  "dev:api": "pnpm --filter api start:dev",
  "build": "pnpm -r build",
  "lint": "pnpm -r lint"
}
```

## 4. Final Workspace Structure

Your final project structure will be clean and organized:

```
/invoice-go-workspace
├── apps/
│   ├── api/                # NestJS server
│   ├── expo-app/           # Your migrated Expo app
│   ├── invoice-builder/    # Vite + React app
│   └── web-portal/         # Vite + React app
├── packages/
│   ├── shared-types/
│   ├── shared-ui/
│   └── shared-utils/
├── node_modules/
├── package.json
├── pnpm-lock.yaml
├── pnpm-workspace.yaml
└── tsconfig.base.json
```

## 5. Next Steps

-   **Verify Everything Works**: Run each app using the root scripts (e.g., `pnpm dev:expo`) to ensure they function correctly.
-   **Refactor to Use Packages**: Start moving shared logic, types, and UI components from your applications into the `packages/*` libraries.
-   **Configure CI/CD**: Your CI pipeline can now be much faster by using PNPM's filtering capabilities (`pnpm --filter <package-name> ...`) to run commands only on affected packages.

This PNPM-based approach gives you a flexible and high-performance monorepo, perfectly suited for a multi-app project using Vite. 