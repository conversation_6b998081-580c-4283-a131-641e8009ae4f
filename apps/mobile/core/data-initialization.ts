/**
 * Data initialization system for the mobile app
 * Loads initial data from the mock provider into stores
 */

import { initializeOrganizationStore, initializeUserStore } from '@repo/stores';
import { getApiProvider } from '@repo/providers';

// Create provider instance using factory
const provider = getApiProvider();

/**
 * Initialize all stores with data from the mock provider
 * This should be called once when the app starts
 */
export const initializeAppData = async () => {
  try {
    console.log('🚀 Initializing app data...');

    // Load organizations from mock provider
    const organizations = await provider.getOrganizations();
    console.log(`📊 Loaded ${organizations.length} organizations:`, organizations.map(o => o.name));

    // Initialize organization store
    initializeOrganizationStore({
      organizations,
      activeOrganization: organizations.length > 0 ? organizations[0] : null,
    });

    const user = await provider.getUserProfile('user_001');
    if (!user) {
      return {
        success: false,
        error: 'User not found',
      };
    }
    
    initializeUserStore(user, true);

    // Load additional data that the app expects
    console.log('📋 Loading subscription plans...');
    const subscriptionPlans = await provider.getSubscriptionPlans();
    console.log(`💳 Loaded ${subscriptionPlans.length} subscription plans`);

    console.log('📋 Loading user subscription...');
    const userSubscription = await provider.getUserSubscription(user.id);
    console.log(`📊 User subscription: ${userSubscription?.status || 'none'}`);

    console.log('⚙️ Loading app defaults...');
    const appDefaults = await provider.getAppDefaults(organizations[0]?.id || '1');
    console.log(`🔧 App defaults loaded for organization: ${appDefaults?.organizationId}`);

    console.log('✅ App data initialization complete');
    console.log(`👤 User: ${user.firstName} ${user.lastName} (${user.email})`);
    console.log(`🏢 Organizations: ${organizations.length}`);
    console.log(`💳 Subscription Plans: ${subscriptionPlans.length}`);

    return {
      success: true,
      organizationsCount: organizations.length,
      subscriptionPlansCount: subscriptionPlans.length,
      user: user,
      userSubscription,
      appDefaults,
    };
  } catch (error) {
    console.error('❌ Failed to initialize app data:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
};

/**
 * Get the mock provider instance for use in services
 */
export const getProvider = () => provider;
