// Mobile app-specific selectors for shared stores
// These are convenience selectors that are specific to mobile app usage patterns

import { 
  useOrganizationStore, 
  useUserStore, 
  useInvoiceStore 
} from '@repo/stores';

// Organization selectors
export const useActiveOrganizationId = () =>
  useOrganizationStore((state) => state.activeOrganization?.id);

export const useHasActiveOrganization = () =>
  useOrganizationStore((state) => !!state.activeOrganization);

export const useActiveOrganization = () =>
  useOrganizationStore((state) => state.activeOrganization);

export const useOrganizations = () =>
  useOrganizationStore((state) => state.organizations);

export const useOrganizationIsLoading = () =>
  useOrganizationStore((state) => state.isLoading);

export const useOrganizationById = (id: string) =>
  useOrganizationStore((state) => state.organizations.find(org => org.id === id));

export const useDashboardData = (organizationId?: string) => {
  return useOrganizationStore((state) => {
    const id = organizationId || state.activeOrganization?.id;
    return id ? state.getDashboardData(id) : null;
  });
};

export const useActiveDashboardData = () => {
  return useOrganizationStore((state) => {
    const activeId = state.activeOrganization?.id;
    return activeId ? state.getDashboardData(activeId) : null;
  });
};

// User selectors
export const useCurrentUserId = () =>
  useUserStore((state) => state.currentUser?.id);

export const useIsAuthenticated = () =>
  useUserStore((state) => state.isAuthenticated);

export const useCurrentUser = () =>
  useUserStore((state) => state.currentUser);

export const useUserIsLoading = () =>
  useUserStore((state) => state.isLoading);

export const useUserFullName = () => {
  return useUserStore((state) => {
    const user = state.currentUser;
    return user ? `${user.firstName} ${user.lastName}` : '';
  });
};

export const useUserInitials = () => {
  return useUserStore((state) => {
    const user = state.currentUser;
    return user ? `${user.firstName[0]}${user.lastName[0]}`.toUpperCase() : '';
  });
};

// Invoice selectors (mobile-specific convenience selectors)
export const useInvoiceFormValid = () =>
  useInvoiceStore((state) => state.isFormValid());

export const useInvoiceTotal = () =>
  useInvoiceStore((state) => state.getTotal());

export const useInvoiceSubtotal = () =>
  useInvoiceStore((state) => state.getSubtotal());

export const useInvoiceTaxAmount = () =>
  useInvoiceStore((state) => state.getTaxAmount());

export const useSelectedClient = () =>
  useInvoiceStore((state) => state.selectedClientId);

export const useInvoiceLineItems = () =>
  useInvoiceStore((state) => state.lineItems);

export const useInvoiceAttachments = () =>
  useInvoiceStore((state) => state.attachments);

export const useInvoicePaymentMethods = () =>
  useInvoiceStore((state) => state.paymentMethods);

export const useTotalSelectedPaymentMethods = () =>
  useInvoiceStore((state) => state.getTotalSelectedPaymentMethods());
