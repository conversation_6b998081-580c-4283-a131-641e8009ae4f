// Export all stores and their types
export { TaxMethod, useInvoiceStore, type LineItem, type TaxConfiguration } from './invoiceStore';
export {
  generateNickname,
  useOrganizationStore,
  toStoreOrganization,
  type Organization as StoreOrganization
} from './organization';
export {
    formatCurrency,
    formatCurrencyInput, getCurrencyCode, getCurrencySymbol, useSettingsStore, type UIPreferences
} from './settingsStore';

// Re-export consolidated types from defs
export { type Currency } from '@/defs/common';
export { type Organization } from '@/defs/organization';

// Export organization selectors for clean usage
export { useActiveOrganization, useActiveOrganizationId, useHasActiveOrganization } from './organization-selectors';

// Export user selectors for clean usage
export { useCurrentUser, useCurrentUserId, useIsAuthenticated } from './user-selectors';

