import { INVOICES_KEY, INVOICE_KEY } from '@/constants/query-keys';
import { getApiProvider } from '@/core/providers/provider-factory';
import { queryClient } from '@/core/query-client';
import { Invoice, InvoiceActivity, UpdateInvoiceDto } from '@/defs/invoice';
import { useActiveOrganizationId } from '@/stores';
import { useQuery } from '@tanstack/react-query';
import { debounce } from 'lodash';

const provider = getApiProvider();

// Service functions
export const fetchInvoices = async (organizationId: string): Promise<Invoice[]> => {
  const response = await provider.getInvoices(organizationId);
  return response;
};

export const fetchInvoice = async (organizationId: string, invoiceId: string): Promise<Invoice> => {
  const response = await provider.getInvoice(organizationId, invoiceId);
  return response;
};

export const updateInvoice = async (data: UpdateInvoiceDto): Promise<Invoice> => {
  const response = await provider.updateInvoice(data.organizationId, data.id, data);
  
  // Immediate cache updates
  queryClient.setQueryData(INVOICE_KEY(response.id, data.organizationId), response);
  queryClient.setQueryData(INVOICES_KEY(data.organizationId), (cache: Invoice[]) => {
    if (!cache) return [response];
    return cache.map((invoice) => invoice.id === response.id ? response : invoice);
  });
  
  return response;
};

export const fetchInvoiceActivities = async (organizationId: string, invoiceId: string): Promise<InvoiceActivity[]> => {
  const response = await provider.getInvoiceActivities(organizationId, invoiceId);
  return response;
};

// Debounced version for real-time editing
export const debouncedUpdateInvoice = debounce(updateInvoice, 1000);

// Clean hooks
export const useInvoices = () => {
  const organizationId = useActiveOrganizationId();
  
  const {
    error,
    isPending: loading,
    data: invoices,
  } = useQuery({
    queryKey: INVOICES_KEY(organizationId!),
    queryFn: () => fetchInvoices(organizationId!),
    enabled: !!organizationId,
  });

  return { invoices, loading, error };
};

export const useInvoice = (invoiceId: string) => {
  const organizationId = useActiveOrganizationId();
  
  const {
    error,
    isPending: loading,
    data: invoice,
  } = useQuery({
    queryKey: INVOICE_KEY(invoiceId, organizationId!),
    queryFn: () => fetchInvoice(organizationId!, invoiceId),
    enabled: !!organizationId && !!invoiceId,
  });

  return { invoice, loading, error };
};

export const useInvoiceActivities = (invoiceId: string) => {
  const organizationId = useActiveOrganizationId();
  
  const {
    error,
    isPending: loading,
    data: activities,
  } = useQuery({
    queryKey: ['invoiceActivities', organizationId, invoiceId],
    queryFn: () => fetchInvoiceActivities(organizationId!, invoiceId),
    enabled: !!organizationId && !!invoiceId,
  });

  return { activities, loading, error };
}; 