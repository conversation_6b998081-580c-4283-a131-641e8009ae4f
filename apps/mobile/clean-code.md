# Clean Code Guidelines 🎯

Clean and refactor this TypeScript/React Native code to follow the highest standards of clarity and maintainability:

## 🏗️ Architecture

Use `getApiProvider()` for all data access — auto-selects Mock (dev) or API (prod) provider.

All data operations go through provider interface.

## 📊 State Management

**React Query**: Server data, API caching, mutations, optimistic updates.

**Zustand**: UI preferences, cross-component state, local data transformations.

**useState**: Component-only ephemeral state (modals, inputs, toggles).

## 📝 Code Style

Functional components only.

Prefer declarative and functional patterns using `.map`, `.filter`, `.reduce`.

Keep functions focused and composable; avoid deeply nested logic.

Eliminate `...spread` syntax when possible — build objects and arrays explicitly.

Use descriptive variable names, consistent structure, and clean typing.

Favor immutability and avoid side effects inside state logic.

Make updates elegant like Swift-style code: structured, expressive, and readable.

## 🎯 TypeScript

Strict types, no `any`.

Union types for status/enums.

Generic types for reusable patterns.

Interface for all object shapes.

## ⚡ Performance

`React.memo` for expensive renders.

`useMemo` for heavy computations.

`useCallback` for stable function references.

Avoid unnecessary re-renders.

## 🔄 React Query Decision

Use when data comes from API.

Use when need caching/background updates.

Use when multiple components need same data.

Use when need loading/error states.

Query keys with hierarchical structure.

Optimistic updates for mutations.

Proper error handling and rollback.

## 🏪 Zustand Decision

Use when UI state crosses components.

Use for app-wide preferences.

Use for local data that doesn't need server sync.

Use for complex form state spanning multiple components.

Focused stores by domain.

Immutable updates only.

Actions grouped logically.

Computed values with `get()`.

## 📋 Checklist

No TypeScript errors or `any` types.

Functional patterns throughout.

Correct state management choice.

Error/loading states handled.

Performance optimized.

Clean naming conventions.

Provider pattern for data access.

Proper state separation.

Immutable updates.

Optimistic UX.

Composable components.

Always use pnpm.

---

**Rule**: Server state → React Query. Client state → Zustand. Component state → useState. 