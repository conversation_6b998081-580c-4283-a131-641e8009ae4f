# React Native Keyboard Controller Guide

This guide explains how to use `react-native-keyboard-controller` for better keyboard handling throughout the app.

## Why We Switched

The previous keyboard libraries (`react-native-keyboard-aware-scroll-view`) had issues with:
- Inconsistent behavior between iOS and Android
- Poor animation performance
- Limited customization options
- Lack of maintenance

`react-native-keyboard-controller` provides:
- ✅ Consistent cross-platform behavior
- ✅ Smooth animations with Reanimated integration
- ✅ Better performance
- ✅ Rich keyboard metadata
- ✅ Interactive keyboard dismissal
- ✅ Active maintenance and support

## Basic Components

### KeyboardAwareScrollView

Replace old `KeyboardAwareScrollView` imports with:

```tsx
import { KeyboardAwareScrollView } from 'react-native-keyboard-controller';

// Basic usage
<KeyboardAwareScrollView
  bottomOffset={20}
  extraKeyboardSpace={0}
  style={styles.container}
  contentContainerStyle={styles.content}
>
  <TextInput placeholder="Enter text..." />
  <TextInput placeholder="Enter more text..." />
</KeyboardAwareScrollView>
```

**Props:**
- `bottomOffset`: Distance between keyboard and focused input (default: 0)
- `extraKeyboardSpace`: Additional space below content (default: 0)
- `enabled`: Enable/disable keyboard handling (default: true)
- `disableScrollOnKeyboardHide`: Prevent auto-scroll when keyboard hides

### KeyboardAvoidingView

For non-scrollable content:

```tsx
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';

<KeyboardAvoidingView 
  behavior="padding"
  keyboardVerticalOffset={headerHeight}
>
  <TextInput placeholder="Enter text..." />
</KeyboardAvoidingView>
```

**Behaviors:**
- `padding` - Most common, adds padding to push content up
- `height` - Shrinks the view height
- `translate-with-padding` - Best performance for chat-like apps

## Migration Examples

### Before (Old Way)
```tsx
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';

<KeyboardAwareScrollView
  enableOnAndroid={true}
  extraScrollHeight={20}
  keyboardShouldPersistTaps="handled"
>
  <TextInput />
</KeyboardAwareScrollView>
```

### After (New Way)
```tsx
import { KeyboardAwareScrollView } from 'react-native-keyboard-controller';

<KeyboardAwareScrollView
  bottomOffset={20}
  keyboardShouldPersistTaps="handled"
>
  <TextInput />
</KeyboardAwareScrollView>
```

## Form Patterns

### Multi-Input Forms
```tsx
import { KeyboardAwareScrollView } from 'react-native-keyboard-controller';

function FormScreen() {
  return (
    <KeyboardAwareScrollView
      bottomOffset={30}
      style={styles.container}
      contentContainerStyle={styles.content}
    >
      <TextInput placeholder="Name" />
      <TextInput placeholder="Email" />
      <TextInput placeholder="Phone" />
      <TextInput placeholder="Address" multiline />
    </KeyboardAwareScrollView>
  );
}
```

### Modal Forms
```tsx
import { KeyboardAwareScrollView } from 'react-native-keyboard-controller';

function ModalForm() {
  return (
    <Modal visible={visible}>
      <View style={styles.modalContainer}>
        <KeyboardAwareScrollView
          bottomOffset={20}
          extraKeyboardSpace={10}
        >
          <TextInput placeholder="Enter details..." />
        </KeyboardAwareScrollView>
      </View>
    </Modal>
  );
}
```

## Advanced Usage

### Custom Keyboard Animations
```tsx
import { useKeyboardHandler } from 'react-native-keyboard-controller';
import Animated, { useAnimatedStyle, useSharedValue } from 'react-native-reanimated';

function ChatScreen() {
  const height = useSharedValue(0);

  useKeyboardHandler({
    onMove: (event) => {
      'worklet';
      height.value = Math.max(event.height, 0);
    },
  });

  const animatedStyle = useAnimatedStyle(() => ({
    height: height.value,
  }));

  return (
    <View style={{ flex: 1 }}>
      <FlatList data={messages} />
      <TextInput placeholder="Type message..." />
      <Animated.View style={animatedStyle} />
    </View>
  );
}
```

### Navigation Header Support
```tsx
import { useHeaderHeight } from '@react-navigation/elements';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';

function Screen() {
  const headerHeight = useHeaderHeight();

  return (
    <KeyboardAvoidingView
      behavior="padding"
      keyboardVerticalOffset={headerHeight}
    >
      <TextInput placeholder="Type here..." />
    </KeyboardAvoidingView>
  );
}
```

## Common Issues and Solutions

### 1. TextInput Not Scrolling Into View
**Problem**: Input field gets covered by keyboard.
**Solution**: Increase `bottomOffset` prop.

```tsx
<KeyboardAwareScrollView bottomOffset={50}>
  <TextInput />
</KeyboardAwareScrollView>
```

### 2. Extra Space Below Content
**Problem**: Too much space below keyboard.
**Solution**: Use negative `extraKeyboardSpace`.

```tsx
<KeyboardAwareScrollView extraKeyboardSpace={-20}>
  <TextInput />
</KeyboardAwareScrollView>
```

### 3. Modal Keyboard Issues
**Problem**: Keyboard behavior inconsistent in modals.
**Solution**: Use appropriate `keyboardVerticalOffset`.

```tsx
<KeyboardAvoidingView keyboardVerticalOffset={100}>
  <TextInput />
</KeyboardAvoidingView>
```

## Best Practices

1. **Use KeyboardAwareScrollView for forms** with multiple inputs
2. **Use KeyboardAvoidingView for single inputs** or non-scrollable content
3. **Set appropriate bottomOffset** (usually 20-50px) for comfortable spacing
4. **Consider header height** when using navigation
5. **Test on both platforms** as behavior can vary
6. **Use `keyboardShouldPersistTaps="handled"`** for better UX

## Component Replacement Guide

| Old Component | New Component | Notes |
|---------------|---------------|-------|
| `KeyboardAwareScrollView` (old lib) | `KeyboardAwareScrollView` (new lib) | Props slightly different |
| `KeyboardAvoidingView` (RN) | `KeyboardAvoidingView` (new lib) | Better cross-platform behavior |
| Manual keyboard handling | `useKeyboardHandler` hook | More powerful and consistent |

## Installation Complete ✅

The project is now set up with:
- ✅ `react-native-keyboard-controller` installed
- ✅ `KeyboardProvider` added to app root
- ✅ Expo build properties configured for Kotlin
- ✅ Old keyboard libraries removed
- ✅ Example components updated

You can now start using the new keyboard components throughout your app for better keyboard handling! 