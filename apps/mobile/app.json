{"expo": {"name": "InvoiceGo", "slug": "invoice-go", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "invoicegoexpo", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.homestylerai.invoicegoexpo"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.homestylerai.invoicegoexpo"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}]], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "6102fd57-b9bd-4a91-9e8b-e7d781ce0f08"}}}}