# Invoice Go - Expo React Native App 👋

This is a production-ready [Expo](https://expo.dev) invoice management application built with TypeScript, React Native, and a clean, scalable architecture.

## 🏗️ Architecture Overview

This app follows clean architecture principles with a clear separation of concerns:

### **Provider Pattern**
- **Development**: Uses `MockProvider` with simulated API responses and delays
- **Production**: Configurable to use real API provider via `ProviderFactory`
- **Interface**: All providers implement `IApiProvider` for consistency

### **State Management**
- **React Query (@tanstack/react-query)**: Server state management with caching, mutations, and optimistic updates
- **Zustand**: Client-side state management for UI preferences and local data
- **Custom Hooks**: Encapsulated data fetching with proper error handling and loading states

### **Key Features**
- Multi-organization support with organization switching
- Comprehensive invoice management (CRUD operations)
- Client and service management
- Tax configuration and calculations
- Real-time data updates with optimistic UI
- Offline-capable with proper cache management

## 🚀 Quick Start

1. **Install dependencies**
   ```bash
   pnpm install
   ```

2. **Start development server**
   ```bash
   pnpm start
   ```

3. **Run on platforms**
   - Press `i` for iOS simulator
   - Press `a` for Android emulator  
   - Press `w` for web browser
   - Scan QR code with Expo Go app

## 🧰 Development Setup

### **Provider Configuration**
The app automatically uses the appropriate data provider:

```typescript
// Development: MockProvider with realistic delays
// Production: Real API provider (when implemented)
const provider = getApiProvider();
```

### **React Query Setup**
Pre-configured with:
- 5-minute stale time for efficient caching
- Smart retry logic (no retry on 4xx errors)
- Optimistic updates for better UX
- Automatic background refetching

### **Available Scripts**
```bash
pnpm start          # Start Expo development server
pnpm android        # Run on Android emulator
pnpm ios            # Run on iOS simulator  
pnpm web            # Run in web browser
pnpm lint           # Run ESLint
pnpm reset-project  # Reset to blank project
```

## 📁 Project Structure

```
core/
├── providers/           # Data provider layer
│   ├── api-provider-interface.ts  # Provider contract
│   ├── provider-factory.ts       # Environment-based provider selection
│   └── mock-provider.ts          # Development mock implementation
└── hooks/              # React Query hooks
    └── use-invoices.ts # Invoice data hooks with mutations

stores/                 # Zustand state management
├── invoiceStore.ts     # Invoice form state
├── clientStore.ts      # Client management
├── organizationStore.ts # Organization switching
└── settingsStore.ts    # App preferences

components/
├── ui/                 # Reusable UI components
└── ...                # Feature-specific components

app/                    # File-based routing (Expo Router)
├── (tabs)/            # Tab navigation screens
├── _layout.tsx        # Root layout with providers
└── ...               # Other screens
```

## 🎯 Data Management Patterns

### **Server State (React Query)**
```typescript
// Queries - for fetching data
const { data: invoices, isLoading } = useInvoices(organizationId);

// Mutations - for creating/updating/deleting  
const createInvoice = useCreateInvoice();
const updateInvoice = useUpdateInvoice();
const deleteInvoice = useDeleteInvoice();
```

### **Client State (Zustand)**
```typescript
// UI preferences and local state
const { setActiveOrganization } = useOrganizationStore();
const { selectedCurrency, formatCurrency } = useSettingsStore();
```

## 🔧 Environment Configuration

The app automatically detects the environment:
- **Development**: Uses `MockProvider` with simulated network delays
- **Production**: Ready for real API integration

To implement production API:
1. Create `ApiProvider` class implementing `IApiProvider`
2. Update `provider-factory.ts` to use it in production
3. Configure API endpoints and authentication

## 📚 Learn More

- [Clean Code Guidelines](./clean-code.md) - Detailed coding standards and patterns
- [Expo Documentation](https://docs.expo.dev/) - Expo framework guides
- [React Query Guide](https://tanstack.com/query/latest) - Server state management
- [Zustand Documentation](https://zustand-demo.pmnd.rs/) - Client state management

## 🤝 Contributing

1. Follow the [Clean Code Guidelines](./clean-code.md)
2. Use TypeScript strictly with proper type definitions
3. Implement features with provider pattern
4. Add React Query hooks for server state
5. Write tests for business logic
6. Use conventional commits

## 📱 Platform Support

- ✅ iOS (React Native)
- ✅ Android (React Native)  
- ✅ Web (React Native Web)
- 🔄 Desktop (Expo Dev Build)

---

Built with ❤️ using Expo, TypeScript, React Query, and clean architecture principles.
