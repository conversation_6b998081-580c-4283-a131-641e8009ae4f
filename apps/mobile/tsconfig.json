{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "allowJs": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "isolatedModules": true, "jsx": "react-jsx", "lib": ["es2017"], "moduleResolution": "bundler", "noEmit": true, "target": "esnext", "resolveJsonModule": true, "skipLibCheck": true, "paths": {"@/*": ["./*"], "@/defs/*": ["./defs/*"]}}, "include": ["**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts"]}