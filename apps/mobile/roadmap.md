# Migration Roadmap: Current Architecture → cursor_rules.md Architecture

## 🎯 **GOAL**
Migrate from current mixed architecture to **organization-centric** architecture as defined in `cursor_rules.md`:
- **Organization Store**: Core business context with active organization management
- **React Query**: All server state (organizations, invoices, clients, services)  
- **Zustand**: Pure client state (UI, preferences, form state)
- **Temporal/Immer**: Advanced state management for complex documents

---

## 📊 **CURRENT vs TARGET ARCHITECTURE**

### **🔍 Current State Analysis:**
```
Current Architecture:
├── organizationStore.ts (151 lines) - Mixed: UI state + static dashboard data
├── invoiceStore.ts (387 lines) - Complex form state, no temporal
├── clientStore.ts (17 lines) - Minimal UI state only ✅
├── serviceStore.ts (16 lines) - Minimal UI state only ✅  
├── taxStore.ts (61 lines) - Mixed: UI state + static data
├── settingsStore.ts (233 lines) - User preferences
├── invoiceDataStore.ts (81 lines) - Data duplication
└── React Query hooks - ❌ Need cursor_rules.md alignment
```

### **🎯 Target Architecture (cursor_rules.md):**
```
Target Architecture:
├── organization.ts - Organization selection + cache management
├── invoice.ts - Document state with temporal/immer
├── preferences.ts - User preferences and settings  
├── dialog.ts - Modal/dialog state
├── React Query hooks - Organization-scoped, exact cursor_rules.md patterns
└── Provider integration - Clean separation
```

---

## ❌ **CRITICAL MISALIGNMENTS WITH cursor_rules.md**

### **1. React Query Hook Patterns**

#### **❌ Current Pattern (WRONG):**
```typescript
// Takes optional organizationId parameter
export function useInvoices(organizationId?: string) {
  const activeOrganizationId = useOrganizationStore(state => state.activeOrganizationId);
  const finalOrganizationId = organizationId || activeOrganizationId;
  
  return useQuery({
    queryKey: invoiceKeys.list(finalOrganizationId),
    queryFn: () => provider.getInvoices(finalOrganizationId),
    enabled: !!finalOrganizationId,
  });
}
```

#### **✅ cursor_rules.md Pattern (TARGET):**
```typescript
// No parameters - hook manages own dependencies
export const useInvoices = () => {
  const organizationId = useActiveOrganizationId();
  
  const {
    error,
    isPending: loading,
    data: invoices,
  } = useQuery({
    queryKey: ['invoices', { organizationId }],
    queryFn: () => fetchInvoices(organizationId!),
    enabled: !!organizationId,
  });

  return { invoices, loading, error };
};
```

### **2. Query Keys Structure**

#### **❌ Current Pattern (WRONG):**
```typescript
export const invoiceKeys = {
  all: ['invoices'] as const,
  lists: () => [...invoiceKeys.all, 'list'] as const,
  list: (organizationId: string) => [...invoiceKeys.lists(), organizationId] as const,
};
```

#### **✅ cursor_rules.md Pattern (TARGET):**
```typescript
export const ORGANIZATIONS_KEY = ['organizations'];
export const INVOICES_KEY = (organizationId: string) => ['invoices', { organizationId }];
export const INVOICE_KEY = (id: string, organizationId: string) => ['invoice', { id, organizationId }];
export const CLIENTS_KEY = (organizationId: string) => ['clients', { organizationId }];
export const SERVICES_KEY = (organizationId: string) => ['services', { organizationId }];
```

### **3. Mutation Patterns** 

#### **❌ Current Pattern (WRONG):**
```typescript
export function useCreateInvoice() {
  return useMutation({
    mutationFn: ({ organizationId, invoice }: { organizationId: string; invoice: CreateInvoiceInput }) =>
      provider.createInvoice(organizationId, invoice),
    // Manual parameter passing
  });
}
```

#### **✅ cursor_rules.md Pattern (TARGET):**
```typescript
export const useCreateInvoice = () => {
  const organizationId = useActiveOrganizationId();
  
  const {
    error,
    isPending: loading,
    mutateAsync: createInvoiceFn,
  } = useMutation({
    mutationFn: (data: CreateInvoiceDto) => createInvoice({ ...data, organizationId: organizationId! }),
    onSuccess: (data) => {
      queryClient.setQueryData<InvoiceDto>(
        ["invoice", { id: data.id, organizationId }], 
        data
      );
      queryClient.setQueryData<InvoiceDto[]>(
        ["invoices", { organizationId }], 
        (cache) => {
          if (!cache) return [data];
          return [...cache, data];
        }
      );
    },
  });

  return { createInvoice: createInvoiceFn, loading, error };
};
```

### **4. Organization Store Pattern**

#### **❌ Current Pattern (WRONG):**
```typescript
// Mixed responsibilities, no cache management
export const useOrganizationStore = create<OrganizationStore>((set, get) => ({
  organizations: dashboardData.user.companies, // Static data
  activeOrganizationId: dashboardData.user.activeCompanyId,
  dashboardData: dashboardData.companyData, // Should be React Query
  
  setActiveOrganization: (organizationId: string) => {
    set({ activeOrganizationId: organizationId }); // No cache invalidation
  },
}));
```

#### **✅ cursor_rules.md Pattern (TARGET):**
```typescript
export const useOrganizationStore = create<OrganizationState & OrganizationActions>()(
  persist(
    (set, get) => ({
      activeOrganization: null,
      organizations: [],
      isLoading: false,
      
      setActiveOrganization: (organization) => {
        set({ activeOrganization: organization });
        // Clear cache when switching organizations
        queryClient.removeQueries({ queryKey: ['invoices'] });
        queryClient.removeQueries({ queryKey: ['clients'] });
        queryClient.removeQueries({ queryKey: ['services'] });
      },
      
      // ... full implementation from cursor_rules.md
    }),
    { 
      name: "organization",
      partialize: (state) => ({ 
        activeOrganization: state.activeOrganization,
        organizations: state.organizations 
      })
    },
  ),
);

// Computed selectors
export const useActiveOrganizationId = () => 
  useOrganizationStore((state) => state.activeOrganization?.id);

export const useHasActiveOrganization = () => 
  useOrganizationStore((state) => !!state.activeOrganization);
```

---

## 🏗️ **EXACT MIGRATION PHASES**

## ✅ **Phase 1: FOUNDATION - COMPLETED**
- [x] Provider pattern established
- [x] Mock database setup  
- [x] React Query hooks created
- [x] Basic CRUD operations working

## 🔄 **Phase 2: ORGANIZATION STORE TRANSFORMATION**

### **🎯 Priority 1: Replace organizationStore.ts with cursor_rules.md Pattern**

#### **Migration Tasks:**
- [ ] **Create `stores/organization.ts`** following cursor_rules.md exactly:
  - Persistence with `activeOrganization` object (not just ID)
  - Cache invalidation on organization switch
  - Computed selectors: `useActiveOrganizationId`, `useHasActiveOrganization`
  - Full CRUD operations with proper state management

- [ ] **Create computed selectors** in separate exports:
  ```typescript
  export const useActiveOrganizationId = () => 
    useOrganizationStore((state) => state.activeOrganization?.id);
  
  export const useHasActiveOrganization = () => 
    useOrganizationStore((state) => !!state.activeOrganization);
  ```

## 🔄 **Phase 3: REACT QUERY HOOK COMPLETE REWRITE**

### **🎯 Priority 2: Rewrite ALL Hooks to cursor_rules.md Patterns**

#### **Required Changes for EVERY Hook:**

1. **Remove optional parameters** - hooks manage own dependencies
2. **Use computed selectors** - `useActiveOrganizationId()`  
3. **Standardize return pattern** - `{ data, loading, error }`
4. **Fix query keys** - Use object structure: `{ organizationId }`
5. **Auto-manage organization context** - No manual passing

#### **File-by-File Rewrites:**

##### **`core/hooks/use-invoices.ts` - COMPLETE REWRITE**
```typescript
// ✅ TARGET PATTERN
export const useInvoices = () => {
  const organizationId = useActiveOrganizationId();
  
  const {
    error,
    isPending: loading,
    data: invoices,
  } = useQuery({
    queryKey: ['invoices', { organizationId }],
    queryFn: () => provider.getInvoices(organizationId!),
    enabled: !!organizationId,
  });

  return { invoices, loading, error };
};

export const useInvoice = (invoiceId: string) => {
  const organizationId = useActiveOrganizationId();
  
  const {
    error,
    isPending: loading,
    data: invoice,
  } = useQuery({
    queryKey: ['invoice', { id: invoiceId, organizationId }],
    queryFn: () => provider.getInvoice(organizationId!, invoiceId),
    enabled: !!organizationId && !!invoiceId,
  });

  return { invoice, loading, error };
};

export const useCreateInvoice = () => {
  const organizationId = useActiveOrganizationId();
  
  const {
    error,
    isPending: loading,
    mutateAsync: createInvoiceFn,
  } = useMutation({
    mutationFn: (data: CreateInvoiceInput) => 
      provider.createInvoice(organizationId!, data),
    onSuccess: (data) => {
      queryClient.setQueryData(['invoice', { id: data.id, organizationId }], data);
      queryClient.setQueryData(['invoices', { organizationId }], (cache: Invoice[]) => {
        if (!cache) return [data];
        return [...cache, data];
      });
    },
  });

  return { createInvoice: createInvoiceFn, loading, error };
};
```

##### **`core/hooks/use-clients.ts` - COMPLETE REWRITE**  
##### **`core/hooks/use-services.ts` - COMPLETE REWRITE**
##### **`core/hooks/use-organizations.ts` - REWRITE**

#### **Migration Tasks:**
- [ ] Rewrite `use-invoices.ts` to cursor_rules.md pattern
- [ ] Rewrite `use-clients.ts` to cursor_rules.md pattern  
- [ ] Rewrite `use-services.ts` to cursor_rules.md pattern
- [ ] Create new query keys constants file
- [ ] Update all mutations to auto-manage organization context

## 🔄 **Phase 4: INVOICE STORE ENHANCEMENT**

### **🎯 Priority 3: Add Temporal/Immer to Invoice Store**

#### **Current Issues:**
❌ No undo/redo functionality  
❌ No immer for immutable updates  
❌ Manual total calculations  
❌ No auto-save mechanism  

#### **Migration Tasks:**
- [ ] Install `zundo` and `immer` dependencies
- [ ] Implement temporal wrapper with undo/redo
- [ ] Add immer for immutable nested updates
- [ ] Implement auto-calculation for totals
- [ ] Add debounced auto-save functionality

## 🔄 **Phase 5: STORE CLEANUP & CONSOLIDATION**

### **🎯 Priority 4: Align Store Structure with cursor_rules.md**

#### **Target Store Structure:**
```
stores/
├── organization.ts      # ✅ NEW: Organization selection + cache management
├── invoice.ts          # ✅ ENHANCED: Temporal + immer document state  
├── preferences.ts      # 🔄 RENAMED: User preferences (from settingsStore.ts)
├── dialog.ts           # ✅ NEW: Modal/dialog state
├── index.ts            # 🔄 UPDATED: Clean exports
└── [REMOVED]           # Delete: organizationStore.ts, invoiceDataStore.ts, taxStore.ts
```

#### **Migration Tasks:**
- [ ] Create `stores/dialog.ts` for modal state
- [ ] Rename `settingsStore.ts` → `stores/preferences.ts`
- [ ] Move tax UI state from `taxStore.ts` to invoice store
- [ ] Delete duplicate stores: `invoiceDataStore.ts`, `taxStore.ts`
- [ ] Update all imports across codebase

## 🔄 **Phase 6: COMPONENT UPDATES**

### **🎯 Priority 5: Update Components to New Architecture**

#### **Components Needing Updates:**
- [ ] `app/(tabs)/index.tsx` - Use React Query for dashboard data
- [ ] `components/OrganizationSelector.tsx` - Use new organization store
- [ ] `app/create-organization.tsx` - Use new organization store  
- [ ] All invoice forms - Use new temporal invoice store
- [ ] All components using removed stores

#### **Migration Tasks:**
- [ ] Replace `useOrganizationStore` with new computed selectors
- [ ] Add `useHasActiveOrganization` guards where needed
- [ ] Implement organization selection prompts
- [ ] Update all static data usage to React Query

---

## 📋 **MIGRATION CHECKLIST**

### **Phase 2: Organization Store (IMMEDIATE NEXT)**
- [ ] Create `stores/organization.ts` following cursor_rules.md pattern exactly
- [ ] Add persistence for organization selection with full object
- [ ] Create computed selectors (`useActiveOrganizationId`, `useHasActiveOrganization`)  
- [ ] Add cache invalidation on organization switch
- [ ] Test organization switching with cache clearing

### **Phase 3: React Query Rewrite (CRITICAL)**
- [ ] Rewrite `use-invoices.ts` - Remove parameters, use selectors, fix return pattern
- [ ] Rewrite `use-clients.ts` - Same pattern alignment
- [ ] Rewrite `use-services.ts` - Same pattern alignment  
- [ ] Create `constants/query-keys.ts` with cursor_rules.md structure
- [ ] Update all mutations to auto-manage organization context

### **Phase 4: Invoice Store Enhancement**
- [ ] Install `zundo` and `immer` dependencies
- [ ] Implement temporal wrapper with undo/redo
- [ ] Add immer for immutable nested updates
- [ ] Implement auto-calculation for totals
- [ ] Add debounced auto-save functionality

### **Phase 5: Store Cleanup**
- [ ] Create `stores/dialog.ts` for modal state
- [ ] Rename `settingsStore.ts` → `stores/preferences.ts`
- [ ] Delete `invoiceDataStore.ts`, `taxStore.ts`, `organizationStore.ts`
- [ ] Update all imports across codebase

### **Phase 6: Component Migration**
- [ ] Update OrganizationSelector to use new organization store
- [ ] Add organization selection guards in main screens
- [ ] Update dashboard to use React Query data
- [ ] Migrate all invoice forms to temporal store

---

**🚨 IMMEDIATE PRIORITY:** The React Query hooks are NOT following cursor_rules.md patterns. They need complete rewrite to:
1. Remove optional parameters  
2. Use computed selectors
3. Return `{ data, loading, error }` pattern
4. Use correct query key structure
5. Auto-manage organization context

**🎯 NEXT STEP:** Create `stores/organization.ts` and rewrite `use-invoices.ts` to cursor_rules.md pattern.

## 🏷️ **NAMING CONVENTIONS**

**Follow these naming patterns for all future code. Clean names = clean code.**

### ✅ **Good Variable Patterns:**
```typescript
// Clear, descriptive, purposeful
const currentClient = clients.find(...)
const activeOrganization = organizations.find(...)
const selectedInvoice = invoices.find(...)
const availableServices = services.filter(...)
const pendingInvoices = invoices.filter(...)
const draftInvoice = invoices.find(...)
```

### ❌ **Avoid These Anti-Patterns:**
```typescript
// Vague suffixes - adds no value
const selectedClientObj = clients.find(...)
const invoiceData = invoice
const serviceInfo = service

// Unclear prefixes - confusing intent  
const finalOrganizationId = organizationId || activeId
const tempClient = client
const newService = service

// Non-descriptive names
const item = invoice
const data = clients
const obj = service
```

### 📝 **Naming Guidelines:**
- **`current*`** - Item being viewed/edited right now
- **`selected*`** - Item chosen from picker/dropdown
- **`active*`** - Item that's currently enabled/in-use
- **`available*`** - Items that can be chosen/used
- **`filtered*`** - Items after search/filtering
- **`pending*`** - Items awaiting action
- **`draft*`** - Items in draft state

### 🎯 **Context-Specific Examples:**
```typescript
// Invoice context
const draftInvoice, pendingInvoice, paidInvoice, overdueInvoice
const invoiceClient, invoiceLineItems, invoiceTotal, invoiceTaxes

// Client context
const primaryClient, billingClient, currentClient  
const clientInvoices, clientContact, clientAddress

// Organization context
const activeOrganization, currentOrganization (NOT finalOrganizationId)
const organizationClients, organizationInvoices, organizationSettings
```

**Remember:** If you're adding "Obj", "Data", "Info", or "final" to a variable name, there's probably a better, more descriptive name available. 