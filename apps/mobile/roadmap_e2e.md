# 🚀 E2E Subscription Limits Roadmap

**Current Status: Phase 0 - Planning Phase**
- 🔄 Phase 1: Limits Infrastructure & Types - READY
- 🔄 Phase 2: Usage Tracking System - READY
- 🔄 Phase 3: Enforcement Layer - READY
- 🔄 Phase 4: UI/UX Limit Handling - READY
- 🔄 Phase 5: E2E Testing Scenarios - READY
- 🔄 Phase 6: Edge Cases & Polish - READY

## 🎯 **GOAL**
Implement robust subscription limits system that:
- **Prevents limit violations** before backend implementation
- **Tracks usage accurately** across all resources
- **Provides clear UX** for limit-related scenarios
- **Handles edge cases** gracefully
- **Tests end-to-end** all subscription scenarios

---

## 📊 **Subscription Limits Analysis**

### **Limit Types to Implement:**
1. **Organizations** - Max number of organizations user can create
2. **Invoices** - Max invoices per organization (or unlimited)
3. **Clients** - Max clients per organization
4. **Storage** - Total file storage across all organizations (MB)
5. **Branding** - Custom branding vs app branding

### **Subscription Tiers (from roadmap_settings.md):**
```typescript
// Starter Plan - $9.99/month
limits: {
  organizations: 2,
  invoices: -1,        // unlimited
  clients: 100,
  storage: 1024,       // 1GB
  customBranding: false,
}

// Professional Plan - $19.99/month  
limits: {
  organizations: 10,
  invoices: -1,        // unlimited
  clients: 1000,
  storage: 5120,       // 5GB
  customBranding: true,
}

// Free Plan (implied)
limits: {
  organizations: 1,
  invoices: 50,
  clients: 25,
  storage: 100,        // 100MB
  customBranding: false,
}
```

---

## 🏗️ **Architecture Requirements**

### **1. Usage Tracking Layer**
```
defs/
├── subscription-usage.ts    # Usage tracking schemas
└── subscription-limits.ts   # Limits validation schemas

services/
├── limits/
│   ├── usage-tracker.ts     # Track resource usage
│   ├── limit-checker.ts     # Check against limits
│   └── enforcement.ts       # Enforce limit violations
└── subscription/
    └── limits.ts           # Subscription limit queries

stores/
├── usage.ts                # Real-time usage tracking
└── limits.ts              # Limit enforcement state

components/
├── limits/
│   ├── LimitWarning.tsx    # Near-limit warnings
│   ├── LimitExceeded.tsx   # Limit exceeded modal
│   └── UpgradePrompt.tsx   # Subscription upgrade
```

### **2. Enforcement Points**
- **Organization Creation** - Check org limit before creation
- **Invoice Creation** - Check invoice limit per org
- **Client Creation** - Check client limit per org
- **File Upload** - Check storage limit before upload
- **Branding Access** - Check custom branding permission

---

## 🔄 **IMPLEMENTATION PHASES**

## **Phase 1: Limits Infrastructure & Types** 🔄

### **1.1 Create Usage Tracking Types**

#### **defs/subscription-usage.ts**
```typescript
import { z } from 'zod';

export const ResourceUsageSchema = z.object({
  userId: z.string(),
  organizations: z.number().min(0),
  invoices: z.record(z.string(), z.number().min(0)), // orgId -> count
  clients: z.record(z.string(), z.number().min(0)),  // orgId -> count
  storage: z.number().min(0), // total MB used
  lastUpdated: z.date(),
});

export const UsageMetricsSchema = z.object({
  organizationId: z.string(),
  invoiceCount: z.number().min(0),
  clientCount: z.number().min(0),
  storageUsed: z.number().min(0), // MB for this org
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type ResourceUsage = z.infer<typeof ResourceUsageSchema>;
export type UsageMetrics = z.infer<typeof UsageMetricsSchema>;
```

#### **defs/subscription-limits.ts**
```typescript
import { z } from 'zod';

export const SubscriptionLimitsSchema = z.object({
  organizations: z.number().min(0),
  invoices: z.number().min(-1), // -1 = unlimited
  clients: z.number().min(0),
  storage: z.number().min(0),   // MB
  customBranding: z.boolean(),
});

export const LimitCheckResultSchema = z.object({
  canProceed: z.boolean(),
  limitType: z.enum(['organizations', 'invoices', 'clients', 'storage', 'branding']).optional(),
  currentUsage: z.number().optional(),
  limit: z.number().optional(),
  remainingQuota: z.number().optional(),
  upgradeRequired: z.boolean(),
});

export type SubscriptionLimits = z.infer<typeof SubscriptionLimitsSchema>;
export type LimitCheckResult = z.infer<typeof LimitCheckResultSchema>;
```

### **1.2 Extend Mock Database with Usage Tracking**

#### **Update data/mock-database.ts**
```typescript
// Add to MockDatabase class
static resourceUsage: ResourceUsage[] = [
  {
    userId: 'user_001',
    organizations: 2, // Current usage
    invoices: {
      '1': 3,  // Organization 1 has 3 invoices
      '2': 1,  // Organization 2 has 1 invoice
    },
    clients: {
      '1': 6,  // Organization 1 has 6 clients
      '2': 2,  // Organization 2 has 2 clients
    },
    storage: 45.5, // 45.5MB total used
    lastUpdated: new Date(),
  }
];

static subscriptionPlans: SubscriptionPlan[] = [
  {
    id: 'plan_free',
    name: 'Free',
    description: 'Perfect for getting started',
    price: 0,
    currency: 'USD',
    interval: 'month',
    features: [
      '1 organization',
      '50 invoices per month',
      'Up to 25 clients',
      '100MB storage',
      'Basic templates',
    ],
    limits: {
      organizations: 1,
      invoices: 50,
      clients: 25,
      storage: 100,
      customBranding: false,
    },
    isPopular: false,
    isActive: true,
  },
  // ... existing plans with limits added
];
```

### **1.3 Create Usage Tracking Store**

#### **stores/usage.ts**
```typescript
import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';

type UsageState = {
  currentUsage: ResourceUsage | null;
  isLoading: boolean;
  lastSync: Date | null;
};

type UsageActions = {
  setUsage: (usage: ResourceUsage) => void;
  incrementOrganizations: () => void;
  incrementInvoices: (organizationId: string) => void;
  incrementClients: (organizationId: string) => void;
  addStorage: (bytes: number) => void;
  decrementInvoices: (organizationId: string) => void;
  decrementClients: (organizationId: string) => void;
  removeStorage: (bytes: number) => void;
  syncUsage: () => Promise<void>;
};

export const useUsageStore = create<UsageState & UsageActions>()(
  subscribeWithSelector((set, get) => ({
    currentUsage: null,
    isLoading: false,
    lastSync: null,
    
    setUsage: (usage) => set({ currentUsage: usage, lastSync: new Date() }),
    
    incrementOrganizations: () => {
      set((state) => {
        if (!state.currentUsage) return state;
        return {
          currentUsage: {
            ...state.currentUsage,
            organizations: state.currentUsage.organizations + 1,
            lastUpdated: new Date(),
          }
        };
      });
    },
    
    incrementInvoices: (organizationId) => {
      set((state) => {
        if (!state.currentUsage) return state;
        const currentCount = state.currentUsage.invoices[organizationId] || 0;
        return {
          currentUsage: {
            ...state.currentUsage,
            invoices: {
              ...state.currentUsage.invoices,
              [organizationId]: currentCount + 1,
            },
            lastUpdated: new Date(),
          }
        };
      });
    },
    
    incrementClients: (organizationId) => {
      set((state) => {
        if (!state.currentUsage) return state;
        const currentCount = state.currentUsage.clients[organizationId] || 0;
        return {
          currentUsage: {
            ...state.currentUsage,
            clients: {
              ...state.currentUsage.clients,
              [organizationId]: currentCount + 1,
            },
            lastUpdated: new Date(),
          }
        };
      });
    },
    
    addStorage: (bytes) => {
      set((state) => {
        if (!state.currentUsage) return state;
        const mbToAdd = bytes / (1024 * 1024); // Convert bytes to MB
        return {
          currentUsage: {
            ...state.currentUsage,
            storage: state.currentUsage.storage + mbToAdd,
            lastUpdated: new Date(),
          }
        };
      });
    },
    
    syncUsage: async () => {
      set({ isLoading: true });
      try {
        // Sync with backend/mock provider
        const usage = await fetchCurrentUsage();
        set({ currentUsage: usage, lastSync: new Date() });
      } finally {
        set({ isLoading: false });
      }
    },
  }))
);

// Computed selectors
export const useCurrentUsage = () => 
  useUsageStore((state) => state.currentUsage);

export const useOrganizationUsage = () =>
  useUsageStore((state) => state.currentUsage?.organizations || 0);

export const useInvoiceUsage = (organizationId: string) =>
  useUsageStore((state) => state.currentUsage?.invoices[organizationId] || 0);

export const useClientUsage = (organizationId: string) =>
  useUsageStore((state) => state.currentUsage?.clients[organizationId] || 0);

export const useStorageUsage = () =>
  useUsageStore((state) => state.currentUsage?.storage || 0);
```

---

## **Phase 2: Usage Tracking System** 🔄

### **2.1 Usage Tracking Services**

#### **services/limits/usage-tracker.ts**
```typescript
import { getApiProvider } from '@/core/providers/provider-factory';
import { useQuery } from '@tanstack/react-query';

const provider = getApiProvider();

export const fetchCurrentUsage = async (userId: string) => {
  return await provider.getCurrentUsage(userId);
};

export const updateUsage = async (userId: string, updates: Partial<ResourceUsage>) => {
  return await provider.updateUsage(userId, updates);
};

export const useCurrentUsage = () => {
  const userId = 'user_001'; // From auth context
  
  const {
    error,
    isPending: loading,
    data: usage,
  } = useQuery({
    queryKey: ['current-usage', { userId }],
    queryFn: () => fetchCurrentUsage(userId),
    enabled: !!userId,
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
  });

  return { usage, loading, error };
};
```

#### **services/limits/limit-checker.ts**
```typescript
export const checkLimits = async (
  action: 'create_organization' | 'create_invoice' | 'create_client' | 'upload_file',
  context: { organizationId?: string; fileSize?: number }
): Promise<LimitCheckResult> => {
  return await provider.checkLimits(action, context);
};

export const useCheckLimits = () => {
  const {
    error,
    isPending: loading,
    mutateAsync: checkLimitsFn,
  } = useMutation({
    mutationFn: ({ action, context }: { 
      action: string; 
      context: { organizationId?: string; fileSize?: number }
    }) => checkLimits(action, context),
  });

  return { checkLimits: checkLimitsFn, loading, error };
};
```

### **2.2 Extend Mock Provider with Limits**

#### **Update core/providers/mock-provider.ts**
```typescript
// Add to MockProvider class

async getCurrentUsage(userId: string): Promise<ResourceUsage> {
  await this.delay();
  const usage = MockDatabase.resourceUsage.find(u => u.userId === userId);
  if (!usage) {
    // Create default usage if not found
    const defaultUsage: ResourceUsage = {
      userId,
      organizations: 0,
      invoices: {},
      clients: {},
      storage: 0,
      lastUpdated: new Date(),
    };
    MockDatabase.resourceUsage.push(defaultUsage);
    return defaultUsage;
  }
  return usage;
}

async updateUsage(userId: string, updates: Partial<ResourceUsage>): Promise<ResourceUsage> {
  await this.delay(100);
  const index = MockDatabase.resourceUsage.findIndex(u => u.userId === userId);
  
  if (index === -1) {
    throw new Error('Usage record not found');
  }
  
  MockDatabase.resourceUsage[index] = {
    ...MockDatabase.resourceUsage[index],
    ...updates,
    lastUpdated: new Date(),
  };
  
  return MockDatabase.resourceUsage[index];
}

async checkLimits(
  action: string,
  context: { organizationId?: string; fileSize?: number }
): Promise<LimitCheckResult> {
  await this.delay(50);
  
  const userId = 'user_001'; // Get from auth context
  const usage = await this.getCurrentUsage(userId);
  const subscription = await this.getUserSubscription(userId);
  
  if (!subscription) {
    return { canProceed: false, upgradeRequired: true };
  }
  
  const plan = MockDatabase.subscriptionPlans.find(p => p.id === subscription.planId);
  if (!plan) {
    return { canProceed: false, upgradeRequired: true };
  }
  
  const limits = plan.limits;
  
  switch (action) {
    case 'create_organization':
      const canCreateOrg = usage.organizations < limits.organizations;
      return {
        canProceed: canCreateOrg,
        limitType: 'organizations',
        currentUsage: usage.organizations,
        limit: limits.organizations,
        remainingQuota: limits.organizations - usage.organizations,
        upgradeRequired: !canCreateOrg,
      };
      
    case 'create_invoice':
      if (limits.invoices === -1) return { canProceed: true, upgradeRequired: false };
      const currentInvoices = usage.invoices[context.organizationId!] || 0;
      const canCreateInvoice = currentInvoices < limits.invoices;
      return {
        canProceed: canCreateInvoice,
        limitType: 'invoices',
        currentUsage: currentInvoices,
        limit: limits.invoices,
        remainingQuota: limits.invoices - currentInvoices,
        upgradeRequired: !canCreateInvoice,
      };
      
    case 'create_client':
      const currentClients = usage.clients[context.organizationId!] || 0;
      const canCreateClient = currentClients < limits.clients;
      return {
        canProceed: canCreateClient,
        limitType: 'clients',
        currentUsage: currentClients,
        limit: limits.clients,
        remainingQuota: limits.clients - currentClients,
        upgradeRequired: !canCreateClient,
      };
      
    case 'upload_file':
      const fileSizeMB = (context.fileSize || 0) / (1024 * 1024);
      const canUpload = (usage.storage + fileSizeMB) <= limits.storage;
      return {
        canProceed: canUpload,
        limitType: 'storage',
        currentUsage: usage.storage,
        limit: limits.storage,
        remainingQuota: limits.storage - usage.storage,
        upgradeRequired: !canUpload,
      };
      
    default:
      return { canProceed: true, upgradeRequired: false };
  }
}
```

---

## **Phase 3: Enforcement Layer** 🔄

### **3.1 Create Enforcement Hooks**

#### **services/limits/enforcement.ts**
```typescript
import { useCheckLimits } from './limit-checker';
import { useUsageStore } from '@/stores/usage';

export const useEnforceOrganizationLimit = () => {
  const { checkLimits } = useCheckLimits();
  const incrementOrganizations = useUsageStore((state) => state.incrementOrganizations);
  
  const enforceLimit = async (): Promise<boolean> => {
    const result = await checkLimits({
      action: 'create_organization',
      context: {}
    });
    
    if (!result.canProceed) {
      // Show upgrade modal or warning
      return false;
    }
    
    // Update local usage optimistically
    incrementOrganizations();
    return true;
  };
  
  return { enforceLimit };
};

export const useEnforceInvoiceLimit = () => {
  const { checkLimits } = useCheckLimits();
  const incrementInvoices = useUsageStore((state) => state.incrementInvoices);
  const organizationId = useActiveOrganizationId();
  
  const enforceLimit = async (): Promise<boolean> => {
    if (!organizationId) return false;
    
    const result = await checkLimits({
      action: 'create_invoice',
      context: { organizationId }
    });
    
    if (!result.canProceed) {
      return false;
    }
    
    incrementInvoices(organizationId);
    return true;
  };
  
  return { enforceLimit };
};

export const useEnforceClientLimit = () => {
  const { checkLimits } = useCheckLimits();
  const incrementClients = useUsageStore((state) => state.incrementClients);
  const organizationId = useActiveOrganizationId();
  
  const enforceLimit = async (): Promise<boolean> => {
    if (!organizationId) return false;
    
    const result = await checkLimits({
      action: 'create_client',
      context: { organizationId }
    });
    
    if (!result.canProceed) {
      return false;
    }
    
    incrementClients(organizationId);
    return true;
  };
  
  return { enforceLimit };
};

export const useEnforceStorageLimit = () => {
  const { checkLimits } = useCheckLimits();
  const addStorage = useUsageStore((state) => state.addStorage);
  
  const enforceLimit = async (fileSize: number): Promise<boolean> => {
    const result = await checkLimits({
      action: 'upload_file',
      context: { fileSize }
    });
    
    if (!result.canProceed) {
      return false;
    }
    
    addStorage(fileSize);
    return true;
  };
  
  return { enforceLimit };
};
```

### **3.2 Update Creation Services with Enforcement**

#### **Update services/organization/create.ts**
```typescript
export const useCreateOrganization = () => {
  const { enforceLimit } = useEnforceOrganizationLimit();
  
  const {
    error,
    isPending: loading,
    mutateAsync: createOrganizationFn,
  } = useMutation({
    mutationFn: async (data: CreateOrganizationInput) => {
      // Check limits before creation
      const canProceed = await enforceLimit();
      if (!canProceed) {
        throw new Error('Organization limit exceeded');
      }
      
      return createOrganization(data);
    },
  });

  return { createOrganization: createOrganizationFn, loading, error };
};
```

#### **Update services/invoice/create.ts**
```typescript
export const useCreateInvoice = () => {
  const { enforceLimit } = useEnforceInvoiceLimit();
  const organizationId = useActiveOrganizationId();
  
  const {
    error,
    isPending: loading,
    mutateAsync: createInvoiceFn,
  } = useMutation({
    mutationFn: async (data: CreateInvoiceInput) => {
      // Check limits before creation
      const canProceed = await enforceLimit();
      if (!canProceed) {
        throw new Error('Invoice limit exceeded');
      }
      
      return createInvoice({ ...data, organizationId: organizationId! });
    },
  });

  return { createInvoice: createInvoiceFn, loading, error };
};
```

---

## **Phase 4: UI/UX Limit Handling** 🔄

### **4.1 Limit Warning Components**

#### **components/limits/LimitWarning.tsx**
```typescript
import { Alert, AlertDescription } from '@/components/ui/Alert';
import { Button } from '@/components/ui/Button';

interface LimitWarningProps {
  limitType: 'organizations' | 'invoices' | 'clients' | 'storage';
  currentUsage: number;
  limit: number;
  onUpgrade: () => void;
}

export function LimitWarning({ limitType, currentUsage, limit, onUpgrade }: LimitWarningProps) {
  const remaining = limit - currentUsage;
  const percentage = (currentUsage / limit) * 100;
  
  if (percentage < 80) return null; // Only show when approaching limit
  
  const getWarningText = () => {
    switch (limitType) {
      case 'organizations':
        return `You've used ${currentUsage} of ${limit} organizations. ${remaining} remaining.`;
      case 'invoices':
        return `You've used ${currentUsage} of ${limit} invoices. ${remaining} remaining.`;
      case 'clients':
        return `You've used ${currentUsage} of ${limit} clients. ${remaining} remaining.`;
      case 'storage':
        return `You've used ${currentUsage.toFixed(1)}MB of ${limit}MB storage. ${(limit - currentUsage).toFixed(1)}MB remaining.`;
    }
  };
  
  return (
    <Alert variant={percentage >= 95 ? 'destructive' : 'warning'}>
      <AlertDescription>
        {getWarningText()}
        <Button
          variant="link"
          size="sm"
          onPress={onUpgrade}
          style={{ marginLeft: 8 }}
        >
          Upgrade Plan
        </Button>
      </AlertDescription>
    </Alert>
  );
}
```

#### **components/limits/LimitExceeded.tsx**
```typescript
import { Modal } from '@/components/ui/Modal';
import { Button } from '@/components/ui/Button';
import { ThemedText } from '@/components/ThemedText';

interface LimitExceededProps {
  visible: boolean;
  onClose: () => void;
  limitType: string;
  currentPlan: string;
  onUpgrade: () => void;
}

export function LimitExceeded({ visible, onClose, limitType, currentPlan, onUpgrade }: LimitExceededProps) {
  const getLimitMessage = () => {
    switch (limitType) {
      case 'organizations':
        return `You've reached your organization limit on the ${currentPlan} plan.`;
      case 'invoices':
        return `You've reached your invoice limit on the ${currentPlan} plan.`;
      case 'clients':
        return `You've reached your client limit on the ${currentPlan} plan.`;
      case 'storage':
        return `You've reached your storage limit on the ${currentPlan} plan.`;
      default:
        return `You've reached a limit on the ${currentPlan} plan.`;
    }
  };
  
  return (
    <Modal visible={visible} onClose={onClose}>
      <View style={{ padding: 24 }}>
        <ThemedText style={{ fontSize: 20, fontWeight: 'bold', marginBottom: 16 }}>
          Limit Reached
        </ThemedText>
        
        <ThemedText style={{ marginBottom: 24 }}>
          {getLimitMessage()} Upgrade your plan to continue.
        </ThemedText>
        
        <View style={{ flexDirection: 'row', gap: 12 }}>
          <Button
            title="Maybe Later"
            variant="outline"
            onPress={onClose}
            style={{ flex: 1 }}
          />
          <Button
            title="Upgrade Now"
            onPress={onUpgrade}
            style={{ flex: 1 }}
          />
        </View>
      </View>
    </Modal>
  );
}
```

### **4.2 Usage Dashboard Component**

#### **components/limits/UsageDashboard.tsx**
```typescript
import { View, StyleSheet } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ProgressBar } from '@/components/ui/ProgressBar';
import { useCurrentUsage } from '@/stores/usage';
import { useUserSubscription } from '@/services/subscription/subscription';

export function UsageDashboard() {
  const usage = useCurrentUsage();
  const { subscription } = useUserSubscription();
  
  if (!usage || !subscription) return null;
  
  const plan = subscription.plan; // Assuming plan is populated
  
  return (
    <View style={styles.container}>
      <ThemedText style={styles.title}>Usage Overview</ThemedText>
      
      {/* Organizations */}
      <UsageItem
        label="Organizations"
        current={usage.organizations}
        limit={plan.limits.organizations}
      />
      
      {/* Storage */}
      <UsageItem
        label="Storage"
        current={usage.storage}
        limit={plan.limits.storage}
        unit="MB"
      />
      
      {/* Invoices (per org) */}
      {Object.entries(usage.invoices).map(([orgId, count]) => (
        <UsageItem
          key={orgId}
          label={`Invoices (Org ${orgId})`}
          current={count}
          limit={plan.limits.invoices}
          unlimited={plan.limits.invoices === -1}
        />
      ))}
    </View>
  );
}

function UsageItem({ label, current, limit, unit = '', unlimited = false }) {
  const percentage = unlimited ? 0 : (current / limit) * 100;
  
  return (
    <View style={styles.usageItem}>
      <View style={styles.usageHeader}>
        <ThemedText>{label}</ThemedText>
        <ThemedText>
          {unlimited ? `${current}${unit}` : `${current}${unit} / ${limit}${unit}`}
        </ThemedText>
      </View>
      {!unlimited && (
        <ProgressBar 
          progress={percentage}
          color={percentage > 90 ? 'red' : percentage > 75 ? 'orange' : 'green'}
        />
      )}
    </View>
  );
}
```

---

## **Phase 5: E2E Testing Scenarios** 🔄

### **5.1 Test Scenarios Matrix**

#### **Organization Limits Testing**
```typescript
// Test scenarios for organization limits
const organizationLimitTests = [
  {
    name: 'Free plan - 1 organization limit',
    plan: 'free',
    currentUsage: 0,
    action: 'create_organization',
    expectedResult: 'success'
  },
  {
    name: 'Free plan - exceed 1 organization limit',
    plan: 'free',
    currentUsage: 1,
    action: 'create_organization',
    expectedResult: 'limit_exceeded'
  },
  {
    name: 'Starter plan - within 2 organization limit',
    plan: 'starter',
    currentUsage: 1,
    action: 'create_organization',
    expectedResult: 'success'
  },
  {
    name: 'Starter plan - exceed 2 organization limit',
    plan: 'starter',
    currentUsage: 2,
    action: 'create_organization',
    expectedResult: 'limit_exceeded'
  }
];
```

#### **Invoice Limits Testing**
```typescript
const invoiceLimitTests = [
  {
    name: 'Free plan - within 50 invoice limit',
    plan: 'free',
    currentUsage: 49,
    action: 'create_invoice',
    expectedResult: 'success'
  },
  {
    name: 'Free plan - exceed 50 invoice limit',
    plan: 'free',
    currentUsage: 50,
    action: 'create_invoice',
    expectedResult: 'limit_exceeded'
  },
  {
    name: 'Professional plan - unlimited invoices',
    plan: 'professional',
    currentUsage: 1000,
    action: 'create_invoice',
    expectedResult: 'success'
  }
];
```

### **5.2 E2E Test Implementation**

#### **__tests__/e2e/subscription-limits.test.ts**
```typescript
import { renderHook, act } from '@testing-library/react-native';
import { useCreateOrganization } from '@/services/organization/create';
import { useCreateInvoice } from '@/services/invoice/create';
import { MockDatabase } from '@/data/mock-database';

describe('Subscription Limits E2E', () => {
  beforeEach(() => {
    // Reset mock database to known state
    MockDatabase.resourceUsage = [{
      userId: 'user_001',
      organizations: 0,
      invoices: {},
      clients: {},
      storage: 0,
      lastUpdated: new Date(),
    }];
  });
  
  describe('Organization Limits', () => {
    it('should allow organization creation within limits', async () => {
      const { result } = renderHook(() => useCreateOrganization());
      
      await act(async () => {
        const success = await result.current.createOrganization({
          name: 'Test Org',
          nickname: 'TEST'
        });
        expect(success).toBeTruthy();
      });
    });
    
    it('should prevent organization creation when limit exceeded', async () => {
      // Set usage to limit
      MockDatabase.resourceUsage[0].organizations = 1; // Free plan limit
      
      const { result } = renderHook(() => useCreateOrganization());
      
      await act(async () => {
        try {
          await result.current.createOrganization({
            name: 'Test Org 2',
            nickname: 'TEST2'
          });
          fail('Should have thrown limit exceeded error');
        } catch (error) {
          expect(error.message).toContain('limit exceeded');
        }
      });
    });
  });
  
  describe('Invoice Limits', () => {
    it('should allow invoice creation within limits', async () => {
      const { result } = renderHook(() => useCreateInvoice());
      
      await act(async () => {
        const success = await result.current.createInvoice({
          invoiceNumber: 'INV-001',
          clientId: 'client_001'
        });
        expect(success).toBeTruthy();
      });
    });
  });
  
  describe('Storage Limits', () => {
    it('should prevent file upload when storage limit exceeded', async () => {
      // Set storage near limit
      MockDatabase.resourceUsage[0].storage = 99; // Free plan: 100MB
      
      const { result } = renderHook(() => useEnforceStorageLimit());
      
      await act(async () => {
        const fileSize = 2 * 1024 * 1024; // 2MB file
        const canUpload = await result.current.enforceLimit(fileSize);
        expect(canUpload).toBeFalsy();
      });
    });
  });
});
```

---

## **Phase 6: Edge Cases & Polish** 🔄

### **6.1 Edge Cases to Handle**

#### **Race Conditions**
- Multiple users creating resources simultaneously
- Network delays causing stale usage data
- Optimistic updates failing

#### **Data Consistency**
- Usage tracking getting out of sync
- Deleted resources not updating usage
- Import operations bypassing limits

#### **User Experience**
- Graceful degradation when limits reached
- Clear messaging about limit implications
- Smooth upgrade flow

### **6.2 Error Handling Strategy**

#### **services/limits/error-handler.ts**
```typescript
export class LimitExceededError extends Error {
  constructor(
    public limitType: string,
    public currentUsage: number,
    public limit: number
  ) {
    super(`${limitType} limit exceeded: ${currentUsage}/${limit}`);
    this.name = 'LimitExceededError';
  }
}

export const handleLimitError = (error: Error) => {
  if (error instanceof LimitExceededError) {
    // Show limit exceeded modal
    showLimitExceededModal(error.limitType);
    return;
  }
  
  // Handle other errors
  throw error;
};
```

---

## ✅ **IMPLEMENTATION CHECKLIST**

### **📋 Phase 1: Limits Infrastructure** 🔄
- [ ] Create `defs/subscription-usage.ts` with Zod schemas
- [ ] Create `defs/subscription-limits.ts` with validation schemas
- [ ] Extend `MockDatabase` with usage tracking data
- [ ] Add Free plan to subscription plans with limits
- [ ] Create `stores/usage.ts` with real-time tracking
- [ ] Test basic usage tracking functionality

### **📋 Phase 2: Usage Tracking System** 🔄
- [ ] Create `services/limits/usage-tracker.ts`
- [ ] Create `services/limits/limit-checker.ts`
- [ ] Extend `MockProvider` with usage and limit checking methods
- [ ] Implement real-time usage synchronization
- [ ] Test usage tracking accuracy

### **📋 Phase 3: Enforcement Layer** 🔄
- [ ] Create `services/limits/enforcement.ts` with enforcement hooks
- [ ] Update organization creation with limit checks
- [ ] Update invoice creation with limit checks
- [ ] Update client creation with limit checks
- [ ] Update file upload with storage checks
- [ ] Test all enforcement points

### **📋 Phase 4: UI/UX Limit Handling** 🔄
- [ ] Create `LimitWarning.tsx` component
- [ ] Create `LimitExceeded.tsx` modal
- [ ] Create `UsageDashboard.tsx` component
- [ ] Integrate limit warnings in creation forms
- [ ] Add usage dashboard to settings
- [ ] Test user experience flows

### **📋 Phase 5: E2E Testing** 🔄
- [ ] Write organization limit tests
- [ ] Write invoice limit tests
- [ ] Write client limit tests
- [ ] Write storage limit tests
- [ ] Write branding restriction tests
- [ ] Test all subscription plan scenarios

### **📋 Phase 6: Edge Cases & Polish** 🔄
- [ ] Implement race condition handling
- [ ] Add data consistency checks
- [ ] Create comprehensive error handling
- [ ] Add usage sync on app start
- [ ] Test edge cases thoroughly
- [ ] Polish user experience

### **📋 Final Verification** 🔄
- [ ] All limits enforced correctly
- [ ] Usage tracking is accurate
- [ ] User experience is smooth
- [ ] Error handling is comprehensive
- [ ] Performance is acceptable
- [ ] Ready for backend integration

---

## 🎯 **SUCCESS CRITERIA**

### **✅ Functionality:**
- [ ] All subscription limits enforced accurately
- [ ] Real-time usage tracking working
- [ ] Proper error handling and user feedback
- [ ] Smooth upgrade prompts and flows

### **✅ Quality:**
- [ ] Comprehensive test coverage
- [ ] Edge cases handled gracefully
- [ ] Performance optimized
- [ ] Type safety maintained

### **✅ User Experience:**
- [ ] Clear limit communication
- [ ] Intuitive upgrade paths
- [ ] No unexpected failures
- [ ] Responsive interface

---

**🚀 READY TO START:** This E2E roadmap ensures subscription limits work perfectly before backend implementation. Each phase builds systematic coverage of all limit scenarios.

**🎯 IMMEDIATE NEXT STEP:** Start with Phase 1 - Create the subscription usage and limits type definitions with comprehensive Zod schemas. 