# Code Guide: State Management in Invoice Plus

This guide provides a comprehensive overview of how an Invoice Plus project would use **Zustand** for client-side state management and **React Query (TanStack Query)** for server state management.

## Architecture Overview

The project uses a **hybrid state management approach** for multi-company invoice management:

- **Zustand**: Client-side state (UI state, selected company, form state, local preferences)
- **React Query**: Server state (companies, invoices, clients, services data)
- **Integration Layer**: Synchronizes server data with client stores and handles company context

```
┌─────────────────┐    ┌─────────────────┐
│   React Query   │    │     Zustand     │
│  (Server State) │◄──►│ (Client State)  │
│                 │    │                 │
│ • Companies     │    │ • UI State      │
│ • Invoices      │    │ • Active Company│
│ • Clients       │    │ • Form Data     │
│ • Services      │    │ • Preferences   │
│ • Tax Items     │    │ • Local Logic   │
└─────────────────┘    └─────────────────┘
```

## Zustand State Management

### Store Organization

Stores are organized by domain in `apps/client/src/stores/`:

```
stores/
├── auth.ts              # Authentication state
├── company.ts           # Active company and company data
├── invoice.ts           # Invoice document state
├── builder.ts           # Invoice builder UI state
├── userStore.ts         # User profile data
├── dialog.ts            # Modal/dialog state
├── templates.ts         # Invoice template management
└── preferences.ts       # User preferences and settings
```

### Store Examples

#### 1. Company Store (New Core Concept)

```typescript
// apps/client/src/stores/company.ts
import { create } from "zustand";
import { persist } from "zustand/middleware";

type CompanyState = {
  activeCompany: CompanyDto | null;
  companies: CompanyDto[];
  isLoading: boolean;
};

type CompanyActions = {
  setActiveCompany: (company: CompanyDto | null) => void;
  setCompanies: (companies: CompanyDto[]) => void;
  addCompany: (company: CompanyDto) => void;
  updateCompany: (id: string, updates: Partial<CompanyDto>) => void;
  removeCompany: (id: string) => void;
  setLoading: (loading: boolean) => void;
};

export const useCompanyStore = create<CompanyState & CompanyActions>()(
  persist(
    (set, get) => ({
      activeCompany: null,
      companies: [],
      isLoading: false,
      
      setActiveCompany: (company) => {
        set({ activeCompany: company });
        // Clear invoice cache when switching companies
        queryClient.removeQueries({ queryKey: ['invoices'] });
        queryClient.removeQueries({ queryKey: ['clients'] });
        queryClient.removeQueries({ queryKey: ['services'] });
      },
      
      setCompanies: (companies) => {
        set({ companies });
        // Auto-select first company if none selected
        const current = get();
        if (!current.activeCompany && companies.length > 0) {
          current.setActiveCompany(companies[0]);
        }
      },
      
      addCompany: (company) => {
        set((state) => ({
          companies: [...state.companies, company],
          activeCompany: state.activeCompany || company // Set as active if first company
        }));
      },
      
      updateCompany: (id, updates) => {
        set((state) => ({
          companies: state.companies.map(company =>
            company.id === id ? { ...company, ...updates } : company
          ),
          activeCompany: state.activeCompany?.id === id
            ? { ...state.activeCompany, ...updates }
            : state.activeCompany
        }));
      },
      
      removeCompany: (id) => {
        set((state) => {
          const newCompanies = state.companies.filter(c => c.id !== id);
          const newActiveCompany = state.activeCompany?.id === id
            ? newCompanies[0] || null
            : state.activeCompany;
          
          return {
            companies: newCompanies,
            activeCompany: newActiveCompany
          };
        });
      },
      
      setLoading: (isLoading) => set({ isLoading }),
    }),
    { 
      name: "company",
      partialize: (state) => ({ 
        activeCompany: state.activeCompany,
        companies: state.companies 
      })
    },
  ),
);

// Computed selectors
export const useActiveCompanyId = () => 
  useCompanyStore((state) => state.activeCompany?.id);

export const useHasActiveCompany = () => 
  useCompanyStore((state) => !!state.activeCompany);
```

#### 2. Invoice Store (Adapted from Resume Store)

```typescript
// apps/client/src/stores/invoice.ts
import { create } from "zustand";
import { devtools } from "zustand/middleware";
import { immer } from "zustand/middleware/immer";
import { temporal } from "zundo";

export const useInvoiceStore = create<DocumentStore<InvoiceDto>>()(
  devtools(
    temporal(
      immer((set, get) => ({
        document: {} as InvoiceDto,
        
        setValue: (path, value) => {
          set((state) => {
            if (path === "status") {
              state.document.status = value as "draft" | "sent" | "paid";
            } else if (path === "invoiceNumber") {
              state.document.invoiceNumber = value as string;
            } else {
              state.document.data = _set(state.document.data, path, value);
            }
            
            // Auto-calculate totals when line items change
            if (path.includes('lineItems')) {
              state.document.data.totals = calculateInvoiceTotals(state.document.data);
            }
            
            // Auto-save to server
            void debouncedUpdateInvoice(JSON.parse(JSON.stringify(state.document)));
          });
        },
        
        addLineItem: () => {
          set((state) => {
            const lineItem: InvoiceLineItem = {
              id: createId(),
              description: '',
              quantity: 1,
              rate: 0,
              amount: 0,
            };
            state.document.data.lineItems.push(lineItem);
            state.document.data.totals = calculateInvoiceTotals(state.document.data);
            void debouncedUpdateInvoice(JSON.parse(JSON.stringify(state.document)));
          });
        },
        
        removeLineItem: (itemId: string) => {
          set((state) => {
            state.document.data.lineItems = state.document.data.lineItems.filter(
              item => item.id !== itemId
            );
            state.document.data.totals = calculateInvoiceTotals(state.document.data);
            void debouncedUpdateInvoice(JSON.parse(JSON.stringify(state.document)));
          });
        },
        
        setClient: (client: ClientDto) => {
          set((state) => {
            state.document.data.client = client;
            // Auto-populate client-specific tax rates
            state.document.data.taxItems = getClientTaxItems(client);
            state.document.data.totals = calculateInvoiceTotals(state.document.data);
            void debouncedUpdateInvoice(JSON.parse(JSON.stringify(state.document)));
          });
        },
      })),
      {
        limit: 100,
        wrapTemporal: (fn) => devtools(fn),
        partialize: ({ document }) => ({ document }),
      },
    ),
  ),
);
```

## React Query Usage with Company Context

### Company-Aware Query Hooks

```typescript
// apps/client/src/services/invoice/invoices.ts
import { useQuery } from "@tanstack/react-query";
import { useActiveCompanyId } from "@/client/stores/company";

export const useInvoices = () => {
  const companyId = useActiveCompanyId();
  
  const {
    error,
    isPending: loading,
    data: invoices,
  } = useQuery({
    queryKey: ['invoices', { companyId }],
    queryFn: () => fetchInvoices(companyId!),
    enabled: !!companyId, // Only fetch when company is selected
  });

  return { invoices, loading, error };
};

export const useClients = () => {
  const companyId = useActiveCompanyId();
  
  const {
    error,
    isPending: loading,
    data: clients,
  } = useQuery({
    queryKey: ['clients', { companyId }],
    queryFn: () => fetchClients(companyId!),
    enabled: !!companyId,
  });

  return { clients, loading, error };
};

export const useServices = () => {
  const companyId = useActiveCompanyId();
  
  const {
    error,
    isPending: loading,
    data: services,
  } = useQuery({
    queryKey: ['services', { companyId }],
    queryFn: () => fetchServices(companyId!),
    enabled: !!companyId,
  });

  return { services, loading, error };
};
```

### Company-Aware Mutations

```typescript
// apps/client/src/services/invoice/create.ts
import { useMutation } from "@tanstack/react-query";
import { useActiveCompanyId } from "@/client/stores/company";

export const useCreateInvoice = () => {
  const companyId = useActiveCompanyId();
  
  const {
    error,
    isPending: loading,
    mutateAsync: createInvoiceFn,
  } = useMutation({
    mutationFn: (data: CreateInvoiceDto) => createInvoice({ ...data, companyId: companyId! }),
    onSuccess: (data) => {
      // Update company-specific cache
      queryClient.setQueryData<InvoiceDto>(
        ["invoice", { id: data.id, companyId }], 
        data
      );

      queryClient.setQueryData<InvoiceDto[]>(
        ["invoices", { companyId }], 
        (cache) => {
          if (!cache) return [data];
          return [...cache, data];
        }
      );
    },
  });

  return { createInvoice: createInvoiceFn, loading, error };
};

// Client creation with company context
export const useCreateClient = () => {
  const companyId = useActiveCompanyId();
  
  const {
    error,
    isPending: loading,
    mutateAsync: createClientFn,
  } = useMutation({
    mutationFn: (data: CreateClientDto) => createClient({ ...data, companyId: companyId! }),
    onSuccess: (data) => {
      queryClient.setQueryData<ClientDto[]>(
        ["clients", { companyId }], 
        (cache) => {
          if (!cache) return [data];
          return [...cache, data];
        }
      );
    },
  });

  return { createClient: createClientFn, loading, error };
};
```

## Integration Patterns for Multi-Company Architecture

### 1. Company Selection Pattern

```typescript
// Pattern: Company selection affects all data fetching
const CompanySwitcher = () => {
  const { companies, activeCompany, setActiveCompany } = useCompanyStore();
  
  const handleCompanyChange = (company: CompanyDto) => {
    setActiveCompany(company);
    // This automatically triggers re-fetching of company-specific data
    // due to queryKey changes in hooks like useInvoices, useClients, etc.
  };
  
  return (
    <select 
      value={activeCompany?.id || ''} 
      onChange={(e) => {
        const company = companies.find(c => c.id === e.target.value);
        if (company) handleCompanyChange(company);
      }}
    >
      {companies.map(company => (
        <option key={company.id} value={company.id}>
          {company.name}
        </option>
      ))}
    </select>
  );
};
```

### 2. Conditional Data Fetching

```typescript
// Pattern: Only fetch data when company is selected
const InvoiceDashboard = () => {
  const hasActiveCompany = useHasActiveCompany();
  const { invoices, loading } = useInvoices(); // Only fetches if company selected
  const { clients } = useClients();
  
  if (!hasActiveCompany) {
    return <CompanySelectionPrompt />;
  }
  
  if (loading) {
    return <LoadingSpinner />;
  }
  
  return (
    <div>
      <InvoiceList invoices={invoices} />
      <ClientList clients={clients} />
    </div>
  );
};
```

### 3. Company Context Provider Pattern

```typescript
// Pattern: Provide company context throughout the app
const CompanyContextProvider = ({ children }) => {
  const { activeCompany, isLoading } = useCompanyStore();
  const { data: companies } = useCompanies();
  
  // Auto-select first company if user has companies but none selected
  useEffect(() => {
    if (companies?.length && !activeCompany && !isLoading) {
      useCompanyStore.getState().setActiveCompany(companies[0]);
    }
  }, [companies, activeCompany, isLoading]);
  
  return (
    <CompanyContext.Provider value={{ activeCompany, companies }}>
      {children}
    </CompanyContext.Provider>
  );
};
```

## File Structure for Invoice Application

```
apps/client/src/
├── stores/
│   ├── auth.ts              # Authentication state
│   ├── company.ts           # Company selection and management
│   ├── invoice.ts           # Invoice document state
│   ├── builder.ts           # Invoice builder UI state
│   ├── preferences.ts       # User preferences
│   └── dialog.ts            # Modal/dialog state
├── services/
│   ├── auth/                # Authentication services
│   ├── company/
│   │   ├── companies.ts     # Fetch companies query
│   │   ├── create.ts        # Create company mutation
│   │   └── update.ts        # Update company mutation
│   ├── invoice/
│   │   ├── invoices.ts      # Fetch invoices query (company-scoped)
│   │   ├── create.ts        # Create invoice mutation
│   │   ├── update.ts        # Update invoice mutation
│   │   └── delete.ts        # Delete invoice mutation
│   ├── client/
│   │   ├── clients.ts       # Fetch clients query (company-scoped)
│   │   ├── create.ts        # Create client mutation
│   │   └── update.ts        # Update client mutation
│   ├── service/
│   │   ├── services.ts      # Fetch services query (company-scoped)
│   │   ├── create.ts        # Create service mutation
│   │   └── update.ts        # Update service mutation
│   └── tax/
│       ├── tax-items.ts     # Fetch tax items (company-scoped)
│       └── create.ts        # Create tax item mutation
├── constants/
│   └── query-keys.ts        # Company-aware query keys
└── components/
    ├── company/
    │   ├── CompanySwitcher.tsx
    │   └── CompanySetup.tsx
    ├── invoice/
    │   ├── InvoiceBuilder.tsx
    │   └── InvoiceList.tsx
    └── client/
        ├── ClientSelector.tsx
        └── ClientForm.tsx
```

## Company-Aware Query Keys

```typescript
// constants/query-keys.ts
export const COMPANIES_KEY = ['companies'];
export const INVOICES_KEY = (companyId: string) => ['invoices', { companyId }];
export const INVOICE_KEY = (id: string, companyId: string) => ['invoice', { id, companyId }];
export const CLIENTS_KEY = (companyId: string) => ['clients', { companyId }];
export const SERVICES_KEY = (companyId: string) => ['services', { companyId }];
export const TAX_ITEMS_KEY = (companyId: string) => ['tax-items', { companyId }];
```

## Best Practices for Multi-Company Applications

### 1. Company Context Management

```typescript
// ✅ Good: Always include company context in API calls
const useInvoices = () => {
  const companyId = useActiveCompanyId();
  return useQuery({
    queryKey: ['invoices', { companyId }],
    queryFn: () => fetchInvoices(companyId!),
    enabled: !!companyId,
  });
};

// ❌ Bad: Missing company context
const useInvoices = () => {
  return useQuery({
    queryKey: ['invoices'],
    queryFn: fetchInvoices, // No company filtering
  });
};
```

### 2. Cache Invalidation on Company Switch

```typescript
// ✅ Good: Clear related caches when switching companies
setActiveCompany: (company) => {
  set({ activeCompany: company });
  queryClient.removeQueries({ queryKey: ['invoices'] });
  queryClient.removeQueries({ queryKey: ['clients'] });
  queryClient.removeQueries({ queryKey: ['services'] });
},

// ❌ Bad: Leaving stale data from previous company
setActiveCompany: (company) => {
  set({ activeCompany: company });
  // No cache cleanup
},
```

### 3. Conditional Rendering Based on Company

```typescript
// ✅ Good: Guard against missing company context
const InvoiceBuilder = () => {
  const hasActiveCompany = useHasActiveCompany();
  
  if (!hasActiveCompany) {
    return <CompanySelectionPrompt />;
  }
  
  return <InvoiceForm />;
};

// ❌ Bad: Assuming company is always available
const InvoiceBuilder = () => {
  return <InvoiceForm />; // Could crash if no company selected
};
```

## Key Benefits of This Architecture

1. **Multi-tenancy**: Each company acts as a separate workspace
2. **Data Isolation**: Company-scoped queries ensure data separation
3. **Context Switching**: Easy switching between companies with automatic data refetch
4. **Scalable Structure**: Clear separation of concerns for different business entities
5. **Type Safety**: Strong TypeScript support for company-aware operations
6. **Performance**: Optimized caching with company-specific invalidation
7. **User Experience**: Seamless company switching without data conflicts

The key difference from the resume application is the introduction of the **company layer** that acts as a container and context for all business operations, making it suitable for B2B invoice management scenarios where users need to manage multiple companies or workspaces. 