/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/client-detail`; params?: Router.UnknownInputParams; } | { pathname: `/create-client`; params?: Router.UnknownInputParams; } | { pathname: `/create-invoice-collapsible`; params?: Router.UnknownInputParams; } | { pathname: `/create-organization`; params?: Router.UnknownInputParams; } | { pathname: `/create-service`; params?: Router.UnknownInputParams; } | { pathname: `/edit-client`; params?: Router.UnknownInputParams; } | { pathname: `/edit-service`; params?: Router.UnknownInputParams; } | { pathname: `/invoice-detail`; params?: Router.UnknownInputParams; } | { pathname: `/service-detail`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/clients` | `/clients`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/create` | `/create`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/invoices` | `/invoices`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/services` | `/services`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/settings` | `/settings`; params?: Router.UnknownInputParams; } | { pathname: `/settings/account`; params?: Router.UnknownInputParams; } | { pathname: `/settings/defaults`; params?: Router.UnknownInputParams; } | { pathname: `/settings/design`; params?: Router.UnknownInputParams; } | { pathname: `/settings/feedback`; params?: Router.UnknownInputParams; } | { pathname: `/settings/subscription`; params?: Router.UnknownInputParams; } | { pathname: `/settings/support`; params?: Router.UnknownInputParams; } | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/client-detail`; params?: Router.UnknownOutputParams; } | { pathname: `/create-client`; params?: Router.UnknownOutputParams; } | { pathname: `/create-invoice-collapsible`; params?: Router.UnknownOutputParams; } | { pathname: `/create-organization`; params?: Router.UnknownOutputParams; } | { pathname: `/create-service`; params?: Router.UnknownOutputParams; } | { pathname: `/edit-client`; params?: Router.UnknownOutputParams; } | { pathname: `/edit-service`; params?: Router.UnknownOutputParams; } | { pathname: `/invoice-detail`; params?: Router.UnknownOutputParams; } | { pathname: `/service-detail`; params?: Router.UnknownOutputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/clients` | `/clients`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/create` | `/create`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/invoices` | `/invoices`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/services` | `/services`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/settings` | `/settings`; params?: Router.UnknownOutputParams; } | { pathname: `/settings/account`; params?: Router.UnknownOutputParams; } | { pathname: `/settings/defaults`; params?: Router.UnknownOutputParams; } | { pathname: `/settings/design`; params?: Router.UnknownOutputParams; } | { pathname: `/settings/feedback`; params?: Router.UnknownOutputParams; } | { pathname: `/settings/subscription`; params?: Router.UnknownOutputParams; } | { pathname: `/settings/support`; params?: Router.UnknownOutputParams; } | { pathname: `/+not-found`, params: Router.UnknownOutputParams & {  } };
      href: Router.RelativePathString | Router.ExternalPathString | `/client-detail${`?${string}` | `#${string}` | ''}` | `/create-client${`?${string}` | `#${string}` | ''}` | `/create-invoice-collapsible${`?${string}` | `#${string}` | ''}` | `/create-organization${`?${string}` | `#${string}` | ''}` | `/create-service${`?${string}` | `#${string}` | ''}` | `/edit-client${`?${string}` | `#${string}` | ''}` | `/edit-service${`?${string}` | `#${string}` | ''}` | `/invoice-detail${`?${string}` | `#${string}` | ''}` | `/service-detail${`?${string}` | `#${string}` | ''}` | `/_sitemap${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/clients${`?${string}` | `#${string}` | ''}` | `/clients${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/create${`?${string}` | `#${string}` | ''}` | `/create${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}${`?${string}` | `#${string}` | ''}` | `/${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/invoices${`?${string}` | `#${string}` | ''}` | `/invoices${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/services${`?${string}` | `#${string}` | ''}` | `/services${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/settings${`?${string}` | `#${string}` | ''}` | `/settings${`?${string}` | `#${string}` | ''}` | `/settings/account${`?${string}` | `#${string}` | ''}` | `/settings/defaults${`?${string}` | `#${string}` | ''}` | `/settings/design${`?${string}` | `#${string}` | ''}` | `/settings/feedback${`?${string}` | `#${string}` | ''}` | `/settings/subscription${`?${string}` | `#${string}` | ''}` | `/settings/support${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/client-detail`; params?: Router.UnknownInputParams; } | { pathname: `/create-client`; params?: Router.UnknownInputParams; } | { pathname: `/create-invoice-collapsible`; params?: Router.UnknownInputParams; } | { pathname: `/create-organization`; params?: Router.UnknownInputParams; } | { pathname: `/create-service`; params?: Router.UnknownInputParams; } | { pathname: `/edit-client`; params?: Router.UnknownInputParams; } | { pathname: `/edit-service`; params?: Router.UnknownInputParams; } | { pathname: `/invoice-detail`; params?: Router.UnknownInputParams; } | { pathname: `/service-detail`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/clients` | `/clients`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/create` | `/create`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/invoices` | `/invoices`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/services` | `/services`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/settings` | `/settings`; params?: Router.UnknownInputParams; } | { pathname: `/settings/account`; params?: Router.UnknownInputParams; } | { pathname: `/settings/defaults`; params?: Router.UnknownInputParams; } | { pathname: `/settings/design`; params?: Router.UnknownInputParams; } | { pathname: `/settings/feedback`; params?: Router.UnknownInputParams; } | { pathname: `/settings/subscription`; params?: Router.UnknownInputParams; } | { pathname: `/settings/support`; params?: Router.UnknownInputParams; } | `/+not-found` | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } };
    }
  }
}
