[{"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dappmodules_EXPORTS -I/home/<USER>/code/turborepo-shadcn-ui/node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/build/generated/autolinking/src/main/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/../../../../common/cpp -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/reactnativekeyboardcontroller -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -I/home/<USER>/code/turborepo-shadcn-ui/node_modules/.pnpm/react-native-edge-to-edge@1.6.0_react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0__react@19.0.0/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/node_modules/.pnpm/react-native-edge-to-edge@1.6.0_react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0__react@19.0.0/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles/appmodules.dir/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dappmodules_EXPORTS -I/home/<USER>/code/turborepo-shadcn-ui/node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/build/generated/autolinking/src/main/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/../../../../common/cpp -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/reactnativekeyboardcontroller -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -I/home/<USER>/code/turborepo-shadcn-ui/node_modules/.pnpm/react-native-edge-to-edge@1.6.0_react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0__react@19.0.0/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/node_modules/.pnpm/react-native-edge-to-edge@1.6.0_react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0__react@19.0.0/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles/appmodules.dir/OnLoad.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/EventEmitters.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/EventEmitters.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/Props.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/Props.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ShadowNodes.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ShadowNodes.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/States.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/States.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/rnasyncstorage-generated.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/rnasyncstorage-generated.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/Props.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/Props.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/States.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/States.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/rngesturehandler_codegen-generated.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/rngesturehandler_codegen-generated.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dreact_codegen_reactnativekeyboardcontroller_EXPORTS -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/../../../../common/cpp -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/reactnativekeyboardcontroller -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o reactnativekeyboardcontroller_autolinked_build/CMakeFiles/react_codegen_reactnativekeyboardcontroller.dir/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/common/cpp/react/renderer/components/reactnativekeyboardcontroller/RNKCKeyboardControllerViewShadowNode.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/common/cpp/react/renderer/components/reactnativekeyboardcontroller/RNKCKeyboardControllerViewShadowNode.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/common/cpp/react/renderer/components/reactnativekeyboardcontroller/RNKCKeyboardControllerViewShadowNode.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dreact_codegen_reactnativekeyboardcontroller_EXPORTS -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/../../../../common/cpp -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/reactnativekeyboardcontroller -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o reactnativekeyboardcontroller_autolinked_build/CMakeFiles/react_codegen_reactnativekeyboardcontroller.dir/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/common/cpp/react/renderer/components/reactnativekeyboardcontroller/RNKCKeyboardGestureAreaShadowNode.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/common/cpp/react/renderer/components/reactnativekeyboardcontroller/RNKCKeyboardGestureAreaShadowNode.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/common/cpp/react/renderer/components/reactnativekeyboardcontroller/RNKCKeyboardGestureAreaShadowNode.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dreact_codegen_reactnativekeyboardcontroller_EXPORTS -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/../../../../common/cpp -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/reactnativekeyboardcontroller -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o reactnativekeyboardcontroller_autolinked_build/CMakeFiles/react_codegen_reactnativekeyboardcontroller.dir/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/common/cpp/react/renderer/components/reactnativekeyboardcontroller/RNKCOverKeyboardViewShadowNode.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/common/cpp/react/renderer/components/reactnativekeyboardcontroller/RNKCOverKeyboardViewShadowNode.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/common/cpp/react/renderer/components/reactnativekeyboardcontroller/RNKCOverKeyboardViewShadowNode.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dreact_codegen_reactnativekeyboardcontroller_EXPORTS -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/../../../../common/cpp -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/reactnativekeyboardcontroller -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o reactnativekeyboardcontroller_autolinked_build/CMakeFiles/react_codegen_reactnativekeyboardcontroller.dir/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/common/cpp/react/renderer/components/reactnativekeyboardcontroller/RNKCOverKeyboardViewState.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/common/cpp/react/renderer/components/reactnativekeyboardcontroller/RNKCOverKeyboardViewState.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/common/cpp/react/renderer/components/reactnativekeyboardcontroller/RNKCOverKeyboardViewState.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dreact_codegen_reactnativekeyboardcontroller_EXPORTS -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/../../../../common/cpp -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/reactnativekeyboardcontroller -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o reactnativekeyboardcontroller_autolinked_build/CMakeFiles/react_codegen_reactnativekeyboardcontroller.dir/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/build/generated/source/codegen/jni/react/renderer/components/reactnativekeyboardcontroller/ComponentDescriptors.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/build/generated/source/codegen/jni/react/renderer/components/reactnativekeyboardcontroller/ComponentDescriptors.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/build/generated/source/codegen/jni/react/renderer/components/reactnativekeyboardcontroller/ComponentDescriptors.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dreact_codegen_reactnativekeyboardcontroller_EXPORTS -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/../../../../common/cpp -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/reactnativekeyboardcontroller -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o reactnativekeyboardcontroller_autolinked_build/CMakeFiles/react_codegen_reactnativekeyboardcontroller.dir/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/build/generated/source/codegen/jni/react/renderer/components/reactnativekeyboardcontroller/EventEmitters.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/build/generated/source/codegen/jni/react/renderer/components/reactnativekeyboardcontroller/EventEmitters.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/build/generated/source/codegen/jni/react/renderer/components/reactnativekeyboardcontroller/EventEmitters.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dreact_codegen_reactnativekeyboardcontroller_EXPORTS -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/../../../../common/cpp -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/reactnativekeyboardcontroller -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o reactnativekeyboardcontroller_autolinked_build/CMakeFiles/react_codegen_reactnativekeyboardcontroller.dir/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/build/generated/source/codegen/jni/react/renderer/components/reactnativekeyboardcontroller/Props.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/build/generated/source/codegen/jni/react/renderer/components/reactnativekeyboardcontroller/Props.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/build/generated/source/codegen/jni/react/renderer/components/reactnativekeyboardcontroller/Props.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dreact_codegen_reactnativekeyboardcontroller_EXPORTS -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/../../../../common/cpp -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/reactnativekeyboardcontroller -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o reactnativekeyboardcontroller_autolinked_build/CMakeFiles/react_codegen_reactnativekeyboardcontroller.dir/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/build/generated/source/codegen/jni/react/renderer/components/reactnativekeyboardcontroller/ShadowNodes.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/build/generated/source/codegen/jni/react/renderer/components/reactnativekeyboardcontroller/ShadowNodes.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/build/generated/source/codegen/jni/react/renderer/components/reactnativekeyboardcontroller/ShadowNodes.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dreact_codegen_reactnativekeyboardcontroller_EXPORTS -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/../../../../common/cpp -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/reactnativekeyboardcontroller -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o reactnativekeyboardcontroller_autolinked_build/CMakeFiles/react_codegen_reactnativekeyboardcontroller.dir/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/build/generated/source/codegen/jni/react/renderer/components/reactnativekeyboardcontroller/States.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/build/generated/source/codegen/jni/react/renderer/components/reactnativekeyboardcontroller/States.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/build/generated/source/codegen/jni/react/renderer/components/reactnativekeyboardcontroller/States.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dreact_codegen_reactnativekeyboardcontroller_EXPORTS -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/../../../../common/cpp -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/reactnativekeyboardcontroller -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o reactnativekeyboardcontroller_autolinked_build/CMakeFiles/react_codegen_reactnativekeyboardcontroller.dir/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/build/generated/source/codegen/jni/react/renderer/components/reactnativekeyboardcontroller/reactnativekeyboardcontrollerJSI-generated.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/build/generated/source/codegen/jni/react/renderer/components/reactnativekeyboardcontroller/reactnativekeyboardcontrollerJSI-generated.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/build/generated/source/codegen/jni/react/renderer/components/reactnativekeyboardcontroller/reactnativekeyboardcontrollerJSI-generated.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dreact_codegen_reactnativekeyboardcontroller_EXPORTS -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/../../../../common/cpp -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/reactnativekeyboardcontroller -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o reactnativekeyboardcontroller_autolinked_build/CMakeFiles/react_codegen_reactnativekeyboardcontroller.dir/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/build/generated/source/codegen/jni/reactnativekeyboardcontroller-generated.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/build/generated/source/codegen/jni/reactnativekeyboardcontroller-generated.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-keyboard-controller/android/build/generated/source/codegen/jni/reactnativekeyboardcontroller-generated.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ComponentDescriptors.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ComponentDescriptors.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/EventEmitters.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/EventEmitters.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/Props.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/Props.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ShadowNodes.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ShadowNodes.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/States.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/States.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/rnreanimated-generated.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/rnreanimated-generated.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/rnscreens.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/rnscreens.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/RNCWebViewSpec-generated.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/RNCWebViewSpec-generated.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/RNCWebViewSpec-generated.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/Props.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/Props.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/Props.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/States.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/States.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/States.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -I/home/<USER>/code/turborepo-shadcn-ui/node_modules/.pnpm/react-native-edge-to-edge@1.6.0_react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0__react@19.0.0/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/node_modules/.pnpm/react-native-edge-to-edge@1.6.0_react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0__react@19.0.0/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/node_modules/.pnpm/react-native-edge-to-edge@1.6.0_react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0__react@19.0.0/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/RNEdgeToEdge-generated.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/node_modules/.pnpm/react-native-edge-to-edge@1.6.0_react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0__react@19.0.0/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/RNEdgeToEdge-generated.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -I/home/<USER>/code/turborepo-shadcn-ui/node_modules/.pnpm/react-native-edge-to-edge@1.6.0_react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0__react@19.0.0/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/node_modules/.pnpm/react-native-edge-to-edge@1.6.0_react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0__react@19.0.0/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/node_modules/.pnpm/react-native-edge-to-edge@1.6.0_react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0__react@19.0.0/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/node_modules/.pnpm/react-native-edge-to-edge@1.6.0_react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0__react@19.0.0/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -I/home/<USER>/code/turborepo-shadcn-ui/node_modules/.pnpm/react-native-edge-to-edge@1.6.0_react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0__react@19.0.0/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/node_modules/.pnpm/react-native-edge-to-edge@1.6.0_react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0__react@19.0.0/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/node_modules/.pnpm/react-native-edge-to-edge@1.6.0_react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0__react@19.0.0/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/node_modules/.pnpm/react-native-edge-to-edge@1.6.0_react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0__react@19.0.0/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -I/home/<USER>/code/turborepo-shadcn-ui/node_modules/.pnpm/react-native-edge-to-edge@1.6.0_react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0__react@19.0.0/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/node_modules/.pnpm/react-native-edge-to-edge@1.6.0_react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0__react@19.0.0/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/node_modules/.pnpm/react-native-edge-to-edge@1.6.0_react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0__react@19.0.0/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/Props.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/node_modules/.pnpm/react-native-edge-to-edge@1.6.0_react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0__react@19.0.0/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/Props.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -I/home/<USER>/code/turborepo-shadcn-ui/node_modules/.pnpm/react-native-edge-to-edge@1.6.0_react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0__react@19.0.0/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/node_modules/.pnpm/react-native-edge-to-edge@1.6.0_react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0__react@19.0.0/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/node_modules/.pnpm/react-native-edge-to-edge@1.6.0_react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0__react@19.0.0/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/node_modules/.pnpm/react-native-edge-to-edge@1.6.0_react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0__react@19.0.0/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -I/home/<USER>/code/turborepo-shadcn-ui/node_modules/.pnpm/react-native-edge-to-edge@1.6.0_react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0__react@19.0.0/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/node_modules/.pnpm/react-native-edge-to-edge@1.6.0_react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0__react@19.0.0/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/node_modules/.pnpm/react-native-edge-to-edge@1.6.0_react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0__react@19.0.0/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/node_modules/.pnpm/react-native-edge-to-edge@1.6.0_react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0__react@19.0.0/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp"}, {"directory": "/home/<USER>/code/turborepo-shadcn-ui/apps/mobile/android/app/.cxx/Debug/5vg5p1d1/arm64-v8a", "command": "/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/home/<USER>/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/linux-x86_64/sysroot -I/home/<USER>/code/turborepo-shadcn-ui/node_modules/.pnpm/react-native-edge-to-edge@1.6.0_react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0__react@19.0.0/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -I/home/<USER>/code/turborepo-shadcn-ui/node_modules/.pnpm/react-native-edge-to-edge@1.6.0_react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0__react@19.0.0/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem /home/<USER>/.gradle/caches/8.13/transforms/5a89f88737dcaf880ea21b9fdcb02480/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem /home/<USER>/.gradle/caches/8.13/transforms/f5f3eb598334922b3ec7ac664aab84b4/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o -c /home/<USER>/code/turborepo-shadcn-ui/node_modules/.pnpm/react-native-edge-to-edge@1.6.0_react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0__react@19.0.0/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/States.cpp", "file": "/home/<USER>/code/turborepo-shadcn-ui/node_modules/.pnpm/react-native-edge-to-edge@1.6.0_react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0__react@19.0.0/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/States.cpp"}]