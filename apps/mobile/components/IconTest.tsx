import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import { FlatList, StyleSheet, Text, View } from 'react-native';

// Test component to check all icons display correctly
export default function IconTest() {
  // Sample icons to test
  const testIcons = [
    'home-outline',
    'person-outline', 
    'briefcase-outline',
    'document-text-outline',
    'settings-outline',
    'search-outline',
    'add-circle-outline',
    'checkmark-circle-outline'
  ] as const;

  const renderIcon = ({ item }: { item: string }) => {
    return (
      <View style={styles.iconContainer}>
        <Ionicons name={item as any} size={24} color="#000" />
        <Text style={styles.iconLabel}>{item}</Text>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Icon Test</Text>
      <FlatList
        data={testIcons}
        renderItem={renderIcon}
        keyExtractor={(item) => item}
        numColumns={2}
        contentContainerStyle={styles.listContainer}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  listContainer: {
    paddingBottom: 20,
  },
  iconContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    margin: 10,
    padding: 20,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
  },
  iconLabel: {
    marginTop: 8,
    fontSize: 12,
    textAlign: 'center',
  },
}); 