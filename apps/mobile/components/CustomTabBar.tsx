import { colors } from '@/constants/Colors';
import { BottomTabBarProps } from '@react-navigation/bottom-tabs';
import React from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { ThemedText } from './ThemedText';
import { IconSymbol } from './ui/IconSymbol';

export function CustomTabBar({ state, descriptors, navigation }: BottomTabBarProps) {
  const insets = useSafeAreaInsets();

  return (
    <View style={[styles.container, { paddingBottom: insets.bottom }]}>
      {/* Tab items */}
      <View style={styles.content}>
        {/* Home tab */}
        <TabItem 
          label="Home"
          iconName="square.grid.2x2.fill"
          isActive={state.index === 0}
          onPress={() => navigation.navigate('index')}
        />
        
        {/* Invoices tab */}
        <TabItem 
          label="Invoices"
          iconName="doc.text.fill"
          isActive={state.index === 1}
          onPress={() => navigation.navigate('invoices')}
        />
        
        {/* Services tab */}
        <TabItem 
          label="Services"
          iconName="briefcase.fill"
          isActive={state.index === 2}
          onPress={() => navigation.navigate('services')}
        />
        
        {/* Clients tab */}
        <TabItem 
          label="Clients"
          iconName="person.fill"
          isActive={state.index === 3}
          onPress={() => navigation.navigate('clients')}
        />
        
        {/* Settings tab */}
        <TabItem 
          label="Settings"
          iconName="gear"
          isActive={state.index === 4}
          onPress={() => navigation.navigate('settings')}
        />
      </View>
    </View>
  );
}

// Simple tab item component
function TabItem({ 
  label, 
  iconName, 
  isActive, 
  onPress 
}: { 
  label: string;
  iconName: any;
  isActive: boolean;
  onPress: () => void;
}) {
  const activeColor = colors.primary;
  const inactiveColor = '#9da0a6';
  
  return (
    <TouchableOpacity style={styles.tabButton} onPress={onPress} activeOpacity={0.7}>
      <IconSymbol 
        name={iconName} 
        color={isActive ? activeColor : inactiveColor} 
        size={22} 
      />
      <ThemedText 
        style={[
          styles.tabLabel, 
          { color: isActive ? activeColor : inactiveColor }
        ]}
      >
        {label}
      </ThemedText>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.05)',
  },
  content: {
    flexDirection: 'row',
    height: 60,
    paddingTop: 8,
  },
  tabButton: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  tabLabel: {
    fontSize: 12,
  }
}); 