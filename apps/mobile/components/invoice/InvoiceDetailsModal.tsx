import { Typography } from '@/components/ui';
import { colors } from '@/constants/Colors';
import { formatCurrency } from '@/stores';
import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import {
    Animated,
    Modal,
    ScrollView,
    StyleSheet,
    TouchableOpacity,
    TouchableWithoutFeedback,
    View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

interface InvoiceDetailsModalProps {
	visible: boolean;
	onClose: () => void;
	animation: Animated.Value;
	lineItems: any[];
	taxConfig: any;
	getItemTaxAmount: (itemId: string) => number;
	getSubtotal: () => number;
	getTaxAmount: () => number;
	getPreTaxAmount: () => number;
	getTotal: () => number;
	getTaxDataByRate: (taxRate: string | number) => any;
	getTaxDataById: (taxId: string) => any;
}

export function InvoiceDetailsModal({
	visible,
	onClose,
	animation,
	lineItems,
	taxConfig,
	getItemTaxAmount,
	getSubtotal,
	getTaxAmount,
	getPreTaxAmount,
	getTotal,
	getTaxDataByRate,
	getTaxDataById
}: InvoiceDetailsModalProps) {

	const handleClose = () => {
		onClose();
	};

	const filteredLineItems = lineItems.filter(item => item.description && item.description.trim() !== '');

	return (
		<Modal
			visible={visible}
			transparent={true}
			animationType="none"
			statusBarTranslucent={true}
			presentationStyle="overFullScreen"
			onRequestClose={handleClose}
		>
			<View style={styles.modalContainer}>
				<TouchableWithoutFeedback onPress={handleClose}>
					<View style={{ flex: 1 }} />
				</TouchableWithoutFeedback>

				<Animated.View style={[styles.sheetContainer, {
					transform: [{
						translateY: animation.interpolate({
							inputRange: [0, 1],
							outputRange: [600, 0],
						}),
					}],
				}]}>
					<SafeAreaView edges={['bottom']}>
						<View style={styles.sheetHeader}>
							<Typography variant="h4">Invoice Details</Typography>
							<TouchableOpacity onPress={handleClose} style={styles.closeButton}>
								<Ionicons name="close" size={24} color={colors.text.secondary} />
							</TouchableOpacity>
						</View>

						<ScrollView
							style={styles.detailsScrollView}
							showsVerticalScrollIndicator={false}
							contentContainerStyle={{ paddingBottom: 20 }}
						>
							{/* Items List */}
							<View style={styles.sectionContainer}>
								<Typography variant="body" bold style={styles.sectionTitle}>Items & Services</Typography>
								{filteredLineItems.length > 0 ? (
									filteredLineItems.map((item, index) => {
										const itemTaxAmount = getItemTaxAmount(item.id);
										const showItemTax = taxConfig.method === 'per_item' && item.taxable;
										const preDiscountTotal = parseFloat(item.quantity || '0') * parseFloat(item.price || '0');
										const discountAmount = item.discount && parseFloat(item.discount) > 0
											? (item.discountType === 'percentage'
												? preDiscountTotal * (parseFloat(item.discount) / 100)
												: parseFloat(item.discount))
											: 0;

										// Calculate correct base amount for display (excluding tax if inclusive)
										const taxRateString = (item.taxRate || 0).toString();
										const taxRateNum = parseFloat(taxRateString);
										const displayBaseAmount = showItemTax && taxConfig.inclusive
											? preDiscountTotal - (preDiscountTotal * taxRateNum / (100 + taxRateNum))
											: preDiscountTotal;

										// Calculate correct item total including tax
										const baseAfterDiscount = preDiscountTotal - discountAmount;
										const correctItemTotal = showItemTax
											? (taxConfig.inclusive
												? baseAfterDiscount // Tax already included in price
												: baseAfterDiscount + itemTaxAmount) // Add tax to base
											: baseAfterDiscount; // No tax

										return (
											<View key={item.id} style={styles.detailItem}>
												<View style={styles.detailItemHeader}>
													<Typography variant="body" bold>#{index + 1} {item.description}</Typography>
													<Typography variant="body" color="primary" bold>{formatCurrency(correctItemTotal)}</Typography>
												</View>

												{/* Item Calculation Breakdown */}
												<View style={styles.itemCalculations}>
													<View style={styles.calculationRow}>
														<Typography variant="bodySmall" color="secondary">
															{showItemTax && taxConfig.inclusive ? 'Base Amount (excl. tax):' : 'Base Amount:'}
														</Typography>
														<Typography variant="bodySmall">{item.quantity || '0'} × {formatCurrency(showItemTax && taxConfig.inclusive ? (displayBaseAmount / parseFloat(item.quantity || '1')) : parseFloat(item.price || '0'))} = {formatCurrency(displayBaseAmount)}</Typography>
													</View>

													{discountAmount > 0 && (
														<View style={styles.calculationRow}>
															<Typography variant="bodySmall" color="secondary">Discount:</Typography>
															<Typography variant="bodySmall" color="secondary">
																-{item.discountType === 'percentage' ? `${item.discount}%` : formatCurrency(parseFloat(item.discount || '0'))} = -{formatCurrency(discountAmount)}
															</Typography>
														</View>
													)}

													{showItemTax && (
														<>
															<View style={styles.calculationRow}>
																<Typography variant="bodySmall" color="secondary">
																	{(() => {
																		const taxRate = item.taxRate || '0';
																		const taxData = getTaxDataByRate(taxRate);
																		return taxData ? `${taxData.name} (${taxRate}%):` : `Tax (${taxRate}%):`;
																	})()}
																</Typography>
																<Typography variant="bodySmall" color={itemTaxAmount > 0 ? "primary" : "secondary"}>
																	{taxConfig.inclusive ? "Included" : `+${formatCurrency(itemTaxAmount)}`}
																</Typography>
															</View>
															{taxConfig.inclusive && (
																<View style={styles.calculationRow}>
																	<Typography variant="bodySmall" color="secondary">Tax Amount:</Typography>
																	<Typography variant="bodySmall">{formatCurrency(itemTaxAmount)}</Typography>
																</View>
															)}
														</>
													)}

													<View style={[styles.calculationRow, styles.totalRow]}>
														<Typography variant="bodySmall" bold>Item Total:</Typography>
														<Typography variant="bodySmall" bold color="primary">{formatCurrency(correctItemTotal)}</Typography>
													</View>
												</View>

												{item.itemDescription && (
													<Typography variant="bodySmall" color="secondary" style={styles.itemDescription}>
														{item.itemDescription}
													</Typography>
												)}
											</View>
										);
									})
								) : (
									<View style={styles.emptyStateContainer}>
										<Typography variant="body" color="secondary">No items added yet</Typography>
										<Typography variant="bodySmall" color="secondary">Add items to see details here</Typography>
									</View>
								)}
							</View>

							{/* Tax Configuration Details */}
							<View style={styles.sectionContainer}>
								<Typography variant="body" bold style={styles.sectionTitle}>Tax Configuration</Typography>
								<View style={styles.taxConfigDetails}>
									<View style={styles.calculationRow}>
										<Typography variant="bodySmall" color="secondary">Tax Method:</Typography>
										<Typography variant="bodySmall">
											{taxConfig.method === 'none' ? 'No Tax' :
												taxConfig.method === 'on_total' ? 'Tax on Total' :
													taxConfig.method === 'per_item' ? 'Tax per Item' :
														taxConfig.method === 'as_deduction' ? 'Tax Deduction' : 'Unknown'}
										</Typography>
									</View>

									{taxConfig.method !== 'none' && (
										<View style={styles.calculationRow}>
											<Typography variant="bodySmall" color="secondary">Tax Type:</Typography>
											<Typography variant="bodySmall">
												{taxConfig.inclusive ? 'Tax Inclusive' : 'Tax Exclusive'}
											</Typography>
										</View>
									)}

									{(taxConfig.method === 'on_total' || taxConfig.method === 'as_deduction') && (
										<View style={styles.calculationRow}>
											<Typography variant="bodySmall" color="secondary">Tax Rate:</Typography>
											<Typography variant="bodySmall">
												{(() => {
													const tax = taxConfig.taxId ?
														getTaxDataById(taxConfig.taxId) : null;
													return tax ? `${tax.name} (${tax.rate}%)` : 'No tax selected';
												})()}
											</Typography>
										</View>
									)}
								</View>
							</View>

							{/* Summary Calculations */}
							<View style={styles.detailsSummary}>
								<Typography variant="body" bold style={styles.sectionTitle}>Invoice Summary</Typography>

								<View style={styles.calculationRow}>
									<Typography variant="body" color="secondary">Items Subtotal:</Typography>
									<Typography variant="body">{formatCurrency(getSubtotal())}</Typography>
								</View>

								{getTaxAmount() !== 0 && (
									<>
										{taxConfig.inclusive ? (
											<>
												<View style={styles.calculationRow}>
													<Typography variant="body" color="secondary">Amount (excl. tax):</Typography>
													<Typography variant="body">{formatCurrency(getPreTaxAmount())}</Typography>
												</View>
												<View style={styles.calculationRow}>
													<Typography variant="body" color="secondary">Tax Amount (included):</Typography>
													<Typography variant="body">{formatCurrency(Math.abs(getTaxAmount()))}</Typography>
												</View>
											</>
										) : (
											<View style={styles.calculationRow}>
												<Typography variant="body" color="secondary">
													{taxConfig.method === 'as_deduction' ? 'Tax Deduction:' : 'Tax Amount:'}
												</Typography>
												<Typography variant="body" color={taxConfig.method === 'as_deduction' ? 'secondary' : 'primary'}>
													{taxConfig.method === 'as_deduction' ? '-' : '+'}{formatCurrency(Math.abs(getTaxAmount()))}
												</Typography>
											</View>
										)}
									</>
								)}

								<View style={styles.summaryDetailTotal}>
									<Typography variant="h4" bold>Final Total:</Typography>
									<Typography variant="h4" color="primary" bold>{formatCurrency(getTotal())}</Typography>
								</View>
							</View>
						</ScrollView>
					</SafeAreaView>
				</Animated.View>
			</View>
		</Modal>
	);
}

const styles = StyleSheet.create({
	modalContainer: {
		position: 'absolute',
		top: 0,
		left: 0,
		right: 0,
		bottom: 0,
		backgroundColor: 'rgba(0, 0, 0, 0.5)',
	},
	sheetContainer: {
		position: 'absolute',
		bottom: 0,
		left: 0,
		right: 0,
		backgroundColor: colors.cardBackground,
		borderTopLeftRadius: 20,
		borderTopRightRadius: 20,
		paddingTop: 6,
		paddingHorizontal: 16,
		paddingBottom: 16,
		maxHeight: '90%',
	},
	sheetHeader: {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		paddingVertical: 12,
		borderBottomWidth: 1,
		borderBottomColor: colors.divider,
	},
	closeButton: {
		padding: 4,
	},
	detailsScrollView: {
		maxHeight: 600,
		paddingHorizontal: 8,
	},
	sectionContainer: {
		marginBottom: 16,
		borderBottomWidth: 1,
		borderBottomColor: colors.divider,
		paddingBottom: 8,
	},
	sectionTitle: {
		marginBottom: 8,
		paddingHorizontal: 0,
		fontSize: 16,
		fontWeight: '600',
	},
	detailItem: {
		padding: 8,
	},
	detailItemHeader: {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
	},
	itemCalculations: {
		marginTop: 4,
	},
	calculationRow: {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		paddingVertical: 2,
	},
	totalRow: {
		borderTopWidth: 1,
		borderTopColor: colors.divider,
		paddingTop: 4,
		marginTop: 4,
	},
	itemDescription: {
		marginTop: 4,
	},
	emptyStateContainer: {
		padding: 24,
		alignItems: 'center',
	},
	taxConfigDetails: {
		marginTop: 8,
	},
	detailsSummary: {
		marginTop: 16,
	},
	summaryDetailTotal: {
		marginTop: 16,
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
	},
}); 