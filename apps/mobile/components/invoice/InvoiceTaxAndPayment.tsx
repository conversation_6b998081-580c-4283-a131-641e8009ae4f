import { PaymentMethods, RowItem, TaxManagement } from '@/components/ui';
import { getTaxCollapsedText } from '@/components/ui/TaxCollapsed';
import { colors } from '@/constants/Colors';
import { useInvoiceStore } from '@/stores';
import React from 'react';

interface InvoiceTaxAndPaymentProps {
	isTaxCollapsed: boolean;
	onToggleTaxSection: () => void;
	onOpenPaymentMethodsSheet: () => void;
}

export function InvoiceTaxAndPayment({
	isTaxCollapsed,
	onToggleTaxSection,
	onOpenPaymentMethodsSheet
}: InvoiceTaxAndPaymentProps) {
	const invoiceStore = useInvoiceStore();

	return (
		<>
			{/* Tax Configuration - Collapsible */}
			<RowItem
				leftIcon="calculator-outline"
				leftIconColor={colors.primary}
				title="Tax Configuration"
				rightIcon={isTaxCollapsed ? "chevron-forward" : "chevron-down"}
				subtitle={isTaxCollapsed ? getTaxCollapsedText(invoiceStore.taxConfig, invoiceStore.getTaxAmount) : ""}
				onPress={onToggleTaxSection}
				showDivider={true}
				isCollapsed={isTaxCollapsed}
			>
				{!isTaxCollapsed && (
					<TaxManagement />
				)}
			</RowItem>

			{/* Payment Methods */}
			<PaymentMethods onPress={onOpenPaymentMethodsSheet} />
		</>
	);
} 