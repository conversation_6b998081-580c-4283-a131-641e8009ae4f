import { Typography } from '@/components/ui';
import { colors } from '@/constants/Colors';
import { Ionicons } from '@expo/vector-icons';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { EdgeInsets } from 'react-native-safe-area-context';

interface InvoiceHeaderProps {
	isEditMode: boolean;
	onBack: () => void;
	insets: EdgeInsets;
	isLoading?: boolean;
}

export function InvoiceHeader({ isEditMode, onBack, insets, isLoading = false }: InvoiceHeaderProps) {
	const getTitle = () => {
		if (isLoading) return 'Loading Invoice...';
		return isEditMode ? 'Edit Invoice' : 'Create Invoice';
	};

	return (
		<>
			<Stack.Screen options={{ headerShown: false }} />
			<StatusBar style="dark" />

			<View style={[
				styles.header,
				{ paddingTop: insets.top + 16 }
			]}>
				<TouchableOpacity
					style={styles.backButton}
					onPress={onBack}
				>
					<Ionicons name="arrow-back" size={24} color={colors.text.primary} />
				</TouchableOpacity>
				<Typography variant="body" style={styles.headerTitle}>
					{getTitle()}
				</Typography>
				<View style={styles.headerRightPlaceholder} />
			</View>
		</>
	);
}

const styles = StyleSheet.create({
	header: {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		paddingHorizontal: 16,
		paddingBottom: 6,
		backgroundColor: colors.cardBackground,
		borderBottomWidth: 1,
		borderBottomColor: colors.divider,
	},
	headerTitle: {
		fontSize: 16,
		fontWeight: '600',
		color: colors.text.primary,
	},
	backButton: {
		padding: 4,
		marginLeft: -4,
	},
	headerRightPlaceholder: {
		width: 24 + 8,
	},
}); 