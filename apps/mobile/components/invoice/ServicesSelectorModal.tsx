import { Typography } from '@/components/ui';
import { ServiceCreationForm } from '@/components/ui/ServiceCreationForm';
import { colors } from '@/constants/Colors';
import { formatCurrency } from '@/stores';
import { Ionicons } from '@expo/vector-icons';
import React, { useEffect, useState } from 'react';
import {
    Animated,
    Modal,
    StyleSheet,
    TouchableOpacity,
    TouchableWithoutFeedback,
    View
} from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-controller';

interface ServicesSelectorModalProps {
	visible: boolean;
	onClose: () => void;
	animation: Animated.Value;
	availableServices: any[];
	activeOrganizationId: string | null;
	onSelectService: (service: any) => void;
	onCreateService: (serviceData: any) => Promise<void>;
}

export const ServicesSelectorModal = React.memo(function ServicesSelectorModal({
	visible,
	onClose,
	animation,
	availableServices,
	activeOrganizationId,
	onSelectService,
	onCreateService
}: ServicesSelectorModalProps) {
	const [showNewServiceForm, setShowNewServiceForm] = useState(false);
	const [internalVisible, setInternalVisible] = useState(visible);

	// Handle visibility changes with proper animation timing
	useEffect(() => {
		if (visible) {
			setInternalVisible(true);
		} else {
			// Delay hiding the modal to allow animation to complete
			const timer = setTimeout(() => {
				setInternalVisible(false);
			}, 200); // Match the animation duration
			return () => clearTimeout(timer);
		}
	}, [visible]);

	const handleCreateNewService = () => {
		setShowNewServiceForm(true);
	};

	const handleServiceCreationSubmit = async (serviceData: any) => {
		try {
			await onCreateService(serviceData);
			setShowNewServiceForm(false);
			onClose();
		} catch (error) {
			console.error('Failed to create service:', error);
		}
	};

	const handleServiceCreationCancel = () => {
		setShowNewServiceForm(false);
	};

	const handleSelectService = (service: any) => {
		onSelectService(service);
		onClose();
	};

	const handleClose = () => {
		setShowNewServiceForm(false);
		onClose();
	};

	return (
		<Modal
			visible={internalVisible}
			transparent={true}
			animationType="none"
			statusBarTranslucent={true}
			presentationStyle="overFullScreen"
			onRequestClose={handleClose}
		>
			<View style={styles.modalContainer}>
				<TouchableWithoutFeedback onPress={handleClose}>
					<View style={{ flex: 1 }} />
				</TouchableWithoutFeedback>

				<Animated.View style={[styles.sheetContainer, {
					transform: [{
						translateY: animation.interpolate({
							inputRange: [0, 1],
							outputRange: [600, 0],
						}),
					}],
				}]}>
					<View style={styles.sheetHeader}>
						<Typography variant="body" bold>
							{showNewServiceForm ? 'Create New Service' : 'Select Service'}
						</Typography>
						<TouchableOpacity onPress={handleClose} style={styles.closeButton}>
							<Ionicons name="close" size={24} color={colors.text.secondary} />
						</TouchableOpacity>
					</View>

					{showNewServiceForm ? (
						// Service Creation Form
						<KeyboardAwareScrollView
							style={{ flex: 1 }}
							showsVerticalScrollIndicator={false}
							keyboardShouldPersistTaps="handled"
						>
							<ServiceCreationForm
								onSubmit={handleServiceCreationSubmit}
								onCancel={handleServiceCreationCancel}
								cancelButtonText="Back"
							/>
						</KeyboardAwareScrollView>
					) : (
						// Service Selection List
						<KeyboardAwareScrollView
							style={styles.servicesList}
							showsVerticalScrollIndicator={false}
							keyboardShouldPersistTaps="handled"
						>
							{/* Add New Service Button - At the top */}
							<TouchableOpacity
								style={styles.addNewButton}
								onPress={handleCreateNewService}
							>
								<View style={styles.addNewContent}>
									<Ionicons name="add-circle-outline" size={24} color={colors.primary} />
									<View style={styles.addNewText}>
										<Typography variant="body" color="primary" bold>Add New Service</Typography>
										<Typography variant="bodySmall" color="secondary">Create a custom service</Typography>
									</View>
								</View>
							</TouchableOpacity>

							{availableServices.map((service: any) => (
								<TouchableOpacity
									key={service.id}
									style={styles.serviceItem}
									onPress={() => handleSelectService(service)}
								>
									<View style={styles.serviceItemContent}>
										<View style={styles.serviceHeader}>
											<Typography variant="body" bold>{service.name}</Typography>
											<View style={styles.unitBadge}>
												<Typography variant="caption" color="primary" bold>
													{(service.pricing?.unit || service.unit || 'fixed').toUpperCase()}
												</Typography>
											</View>
										</View>
										<Typography variant="bodySmall" color="secondary" numberOfLines={2}>
											{service.description}
										</Typography>
										<View style={styles.servicePrice}>
											<Typography variant="body" color="primary">
												{formatCurrency(service.pricing?.rate || service.rate || 0)}
											</Typography>
										</View>
									</View>
								</TouchableOpacity>
							))}

							{availableServices.length === 0 && (
								<View style={styles.emptyServices}>
									<Typography variant="body" color="secondary" center>
										No services added yet
									</Typography>
									<Typography variant="bodySmall" color="secondary" center style={{ marginTop: 8 }}>
										Add your first service to get started
									</Typography>
								</View>
							)}
						</KeyboardAwareScrollView>
					)}
				</Animated.View>
			</View>
		</Modal>
	);
});

const styles = StyleSheet.create({
	modalContainer: {
		position: 'absolute',
		top: 0,
		left: 0,
		right: 0,
		bottom: 0,
		backgroundColor: 'rgba(0, 0, 0, 0.5)',
	},
	sheetContainer: {
		position: 'absolute',
		bottom: 0,
		left: 0,
		right: 0,
		backgroundColor: colors.cardBackground,
		borderTopLeftRadius: 20,
		borderTopRightRadius: 20,
		paddingTop: 6,
		paddingHorizontal: 16,
		paddingBottom: 16,
		maxHeight: '90%',
	},
	sheetHeader: {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		paddingVertical: 12,
		borderBottomWidth: 1,
		borderBottomColor: colors.divider,
	},
	closeButton: {
		padding: 4,
	},
	servicesList: {
		marginTop: 6,
		maxHeight: 400,
	},
	serviceItem: {
		flexDirection: 'row',
		alignItems: 'center',
		paddingVertical: 8,
		borderRadius: 8,
	},
	serviceItemContent: {
		flex: 1,
	},
	serviceHeader: {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: 8,
	},
	unitBadge: {
		padding: 4,
		borderWidth: 1,
		borderColor: colors.divider,
		borderRadius: 4,
	},
	servicePrice: {
		marginTop: 4,
	},
	emptyServices: {
		padding: 24,
		alignItems: 'center',
	},
	addNewButton: {
		flexDirection: 'row',
		alignItems: 'center',
		padding: 8,
		borderBottomWidth: 1,
		borderBottomColor: colors.divider,
		marginBottom: 8,
	},
	addNewContent: {
		flexDirection: 'row',
		alignItems: 'center',
	},
	addNewText: {
		marginLeft: 8,
	},
}); 