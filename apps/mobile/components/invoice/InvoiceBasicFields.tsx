import { Avatar, RowItem, RowItemDatePicker, RowItemInput } from '@/components/ui';
import { colors } from '@/constants/Colors';
import { useInvoiceStore } from '@/stores';
import React from 'react';

interface InvoiceBasicFieldsProps {
	selectedClientObj: any;
	onOpenClientSheet: () => void;
}

export function InvoiceBasicFields({ selectedClientObj, onOpenClientSheet }: InvoiceBasicFieldsProps) {
	const invoiceStore = useInvoiceStore();

	return (
		<>
			{/* Invoice Number - Looks same, behaves like direct input */}
			<RowItemInput
				leftIcon="receipt-outline"
				leftIconColor={colors.primary}
				title="Invoice #"
				value={invoiceStore.invoiceNumber}
				placeholder="Enter invoice number"
				onChangeText={invoiceStore.setInvoiceNumber}
			/>

			{/* Invoice Date - Looks same, behaves like direct date picker */}
			<RowItemDatePicker
				leftIcon="calendar-outline"
				leftIconColor={colors.primary}
				title="Invoice Date"
				value={invoiceStore.invoiceDate}
				onChange={invoiceStore.setInvoiceDate}
			/>

			{/* Due Date - Looks same, behaves like direct date picker with quick day buttons */}
			<RowItemDatePicker
				leftIcon="calendar-outline"
				leftIconColor={colors.primary}
				title="Due Date"
				value={invoiceStore.dueDate}
				onChange={invoiceStore.setDueDate}
				showQuickDays={true}
			/>

			{/* Client Selection */}
			<RowItem
				leftIcon={selectedClientObj ? undefined : "person-outline"}
				leftIconColor={colors.primary}
				leftContent={selectedClientObj ? (
					<Avatar
						name={selectedClientObj.name}
						photo={selectedClientObj.photo}
						size={20}
					/>
				) : undefined}
				title="Client*"
				subtitle={selectedClientObj ? `${selectedClientObj.name} (${selectedClientObj.contact?.email || 'No email'})` : 'Select a client...'}
				onPress={onOpenClientSheet}
			/>
		</>
	);
} 