import { Button, Typography } from '@/components/ui';
import { colors } from '@/constants/Colors';
import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import {
    Animated,
    Modal,
    StyleSheet,
    TouchableWithoutFeedback,
    View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

interface SignatureModalProps {
	visible: boolean;
	onClose: () => void;
	animation: Animated.Value;
}

export function SignatureModal({
	visible,
	onClose,
	animation
}: SignatureModalProps) {

	const handleClose = () => {
		onClose();
	};

	return (
		<Modal
			visible={visible}
			transparent={true}
			animationType="none"
			statusBarTranslucent={true}
			presentationStyle="overFullScreen"
			onRequestClose={handleClose}
		>
			<View style={styles.modalContainer}>
				<TouchableWithoutFeedback onPress={handleClose}>
					<View style={{ flex: 1 }} />
				</TouchableWithoutFeedback>

				<Animated.View style={[styles.sheetContainer, {
					transform: [{
						translateY: animation.interpolate({
							inputRange: [0, 1],
							outputRange: [600, 0],
						}),
					}],
				}]}>
					<SafeAreaView edges={['bottom']}>
						<View style={styles.sheetHeader}>
							<Typography variant="h4">Signature</Typography>
							<TouchableWithoutFeedback onPress={handleClose}>
								<View style={styles.closeButton}>
									<Ionicons name="close" size={24} color={colors.text.secondary} />
								</View>
							</TouchableWithoutFeedback>
						</View>
						<View style={styles.signatureArea}>
							<Typography variant="body" color="secondary" center>
								Draw your signature here
							</Typography>
						</View>
						<Button
							title="Done"
							onPress={handleClose}
							style={styles.modalButton}
						/>
					</SafeAreaView>
				</Animated.View>
			</View>
		</Modal>
	);
}

const styles = StyleSheet.create({
	modalContainer: {
		position: 'absolute',
		top: 0,
		left: 0,
		right: 0,
		bottom: 0,
		backgroundColor: 'rgba(0, 0, 0, 0.5)',
	},
	sheetContainer: {
		position: 'absolute',
		bottom: 0,
		left: 0,
		right: 0,
		backgroundColor: colors.cardBackground,
		borderTopLeftRadius: 20,
		borderTopRightRadius: 20,
		paddingTop: 6,
		paddingHorizontal: 16,
		paddingBottom: 16,
		maxHeight: '90%',
	},
	sheetHeader: {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		paddingVertical: 12,
		borderBottomWidth: 1,
		borderBottomColor: colors.divider,
	},
	closeButton: {
		padding: 4,
	},
	signatureArea: {
		padding: 20,
		minHeight: 120,
		justifyContent: 'center',
		alignItems: 'center',
	},
	modalButton: {
		marginTop: 16,
	},
}); 