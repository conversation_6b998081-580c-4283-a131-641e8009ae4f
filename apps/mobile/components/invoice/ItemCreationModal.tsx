import { Typography } from '@/components/ui';
import { ItemCreationForm } from '@/components/ui/ItemCreationForm';
import { colors } from '@/constants/Colors';
import { Ionicons } from '@expo/vector-icons';
import React, { useEffect, useState } from 'react';
import {
	Animated,
	Modal,
	StyleSheet,
	TouchableOpacity,
	TouchableWithoutFeedback,
	View
} from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-controller';

interface ItemCreationModalProps {
	visible: boolean;
	onClose: () => void;
	animation: Animated.Value;
	showNewItemForm: boolean;
	editingItem: string | null;
	lineItems: any[];
	onSubmit: (itemData: any) => Promise<void>;
	onCancel: () => void;
	onDelete?: () => void;
	selectedService?: any;
}

export const ItemCreationModal = React.memo(function ItemCreationModal({
	visible,
	onClose,
	animation,
	showNewItemForm,
	editingItem,
	lineItems,
	onSubmit,
	onCancel,
	onDelete,
	selectedService
}: ItemCreationModalProps) {
	const [internalVisible, setInternalVisible] = useState(visible);

	// Handle visibility changes with proper animation timing
	useEffect(() => {
		if (visible) {
			setInternalVisible(true);
		} else {
			// Delay hiding the modal to allow animation to complete
			const timer = setTimeout(() => {
				setInternalVisible(false);
			}, 200); // Match the animation duration
			return () => clearTimeout(timer);
		}
	}, [visible]);

	const handleClose = () => {
		onClose();
	};

	// Get the current item being edited
	const currentItem = editingItem ? lineItems.find(item => item.id === editingItem) : null;

	// Create initial data from service if available, otherwise use current item
	const getInitialData = () => {
		if (selectedService && !editingItem) {
			// Pre-populate with service data for new items
			const serviceRate = selectedService.pricing?.rate || selectedService.rate || 0;
			const serviceUnit = selectedService.pricing?.unit || selectedService.unit || 'fixed';
			return {
				name: selectedService.name || '',
				description: selectedService.description || '',
				price: serviceRate.toString(),
				quantity: '1', // Default quantity
				unit: serviceUnit,
				discount: '', // Empty discount for user to set
				discountType: 'percentage' as const,
			};
		} else if (currentItem) {
			// Use existing item data for editing
			return {
				name: currentItem.description || '',
				description: currentItem.itemDescription || '',
				price: currentItem.price || '',
				quantity: currentItem.quantity || '1',
				unit: currentItem.unit || 'fixed',
				discount: currentItem.discount || '',
				discountType: currentItem.discountType || 'percentage',
			};
		}
		return undefined;
	};

	return (
		<Modal
			visible={internalVisible}
			transparent={true}
			animationType="none"
			statusBarTranslucent={true}
			presentationStyle="overFullScreen"
			onRequestClose={handleClose}
		>
			<View style={styles.modalContainer}>
				<TouchableWithoutFeedback onPress={handleClose}>
					<View style={{ flex: 1 }} />
				</TouchableWithoutFeedback>

				<Animated.View style={[styles.sheetContainer, {
					transform: [{
						translateY: animation.interpolate({
							inputRange: [0, 1],
							outputRange: [600, 0],
						}),
					}],
				}]}>
					<View style={styles.sheetHeader}>
						<Typography variant="body" bold>
							{editingItem 
								? 'Edit Item' 
								: selectedService 
									? `Add ${selectedService.name}` 
									: 'Create New Item'
							}
						</Typography>
						<TouchableOpacity onPress={handleClose} style={styles.closeButton}>
							<Ionicons name="close" size={24} color={colors.text.secondary} />
						</TouchableOpacity>
					</View>

					<KeyboardAwareScrollView
						style={{ flex: 1 }}
						contentContainerStyle={{ padding: 16 }}
						showsVerticalScrollIndicator={false}
						keyboardShouldPersistTaps="handled"
					>
						{showNewItemForm && (
							<ItemCreationForm
								onSubmit={onSubmit}
								onCancel={onCancel}
								onDelete={onDelete}
								cancelButtonText="Cancel"
								initialData={getInitialData()}
								isEditing={!!editingItem}
								canSaveAsService={(editingItem || selectedService) ? false : true}
							/>
						)}
					</KeyboardAwareScrollView>
				</Animated.View>
			</View>
		</Modal>
	);
});

const styles = StyleSheet.create({
	modalContainer: {
		position: 'absolute',
		top: 0,
		left: 0,
		right: 0,
		bottom: 0,
		backgroundColor: 'rgba(0, 0, 0, 0.5)',
	},
	sheetContainer: {
		position: 'absolute',
		bottom: 0,
		left: 0,
		right: 0,
		backgroundColor: colors.cardBackground,
		borderTopLeftRadius: 20,
		borderTopRightRadius: 20,
		paddingTop: 6,
		// paddingHorizontal: 16,
		paddingBottom: 16,
		maxHeight: '90%',
	},
	sheetHeader: {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: 16,
		borderBottomWidth: 1,
		borderBottomColor: colors.divider,
	},
	closeButton: {
		padding: 4,
	},
}); 