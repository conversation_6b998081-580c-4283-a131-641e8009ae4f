import { ItemsServicesExpanded, RowItem } from '@/components/ui';
import { getItemsServicesCollapsedText } from '@/components/ui/ItemsServicesCollapsed';
import { colors } from '@/constants/Colors';
import { useInvoiceStore } from '@/stores';
import React from 'react';

interface InvoiceItemsAndServicesProps {
	isItemsCollapsed: boolean;
	onToggleItemsSection: () => void;
	onAddLineItem: () => void;
	onOpenServicesSheet: () => void;
	onEditLineItem: (id: string) => void;
}

export function InvoiceItemsAndServices({
	isItemsCollapsed,
	onToggleItemsSection,
	onAddLineItem,
	onOpenServicesSheet,
	onEditLineItem
}: InvoiceItemsAndServicesProps) {
	const invoiceStore = useInvoiceStore();

	return (
		<RowItem
			leftIcon="list-outline"
			leftIconColor={colors.primary}
			title="Items & Services"
			rightIcon={isItemsCollapsed ? "chevron-forward" : "chevron-down"}
			subtitle={isItemsCollapsed ? getItemsServicesCollapsedText(invoiceStore.lineItems) : ""}
			onPress={onToggleItemsSection}
			showDivider={true}
			isCollapsed={isItemsCollapsed}
		>
			{!isItemsCollapsed && (
				<ItemsServicesExpanded
					onAddLineItem={onAddLineItem}
					onOpenServicesSheet={onOpenServicesSheet}
					onEditLineItem={onEditLineItem}
				/>
			)}
		</RowItem>
	);
} 