import { Avatar, Typography } from '@/components/ui';
import { ClientCreationForm } from '@/components/ui/ClientCreationForm';
import { colors } from '@/constants/Colors';
import { Ionicons } from '@expo/vector-icons';
import React, { useEffect, useState } from 'react';
import {
	Animated,
	Modal,
	StyleSheet,
	TouchableOpacity,
	TouchableWithoutFeedback,
	View
} from 'react-native';
import { KeyboardAwareView } from '../ui/KeyboardAwareView';

interface ClientSelectorModalProps {
	visible: boolean;
	onClose: () => void;
	animation: Animated.Value;
	availableClients: any[];
	selectedClientId?: string;
	onSelectClient: (clientId: string) => void;
	onCreateClient: (clientData: any) => Promise<void>;
}

export const ClientSelectorModal = React.memo(function ClientSelectorModal({
	visible,
	onClose,
	animation,
	availableClients,
	selectedClientId,
	onSelectClient,
	onCreateClient
}: ClientSelectorModalProps) {
	const [showNewClientForm, setShowNewClientForm] = useState(false);
	const [internalVisible, setInternalVisible] = useState(visible);

	// Handle visibility changes with proper animation timing
	useEffect(() => {
		if (visible) {
			setInternalVisible(true);
		} else {
			// Delay hiding the modal to allow animation to complete
			const timer = setTimeout(() => {
				setInternalVisible(false);
			}, 200); // Match the animation duration
			return () => clearTimeout(timer);
		}
	}, [visible]);

	const handleCreateNewClient = () => {
		setShowNewClientForm(true);
	};

	const handleClientCreationSubmit = async (clientData: any) => {
		try {
			await onCreateClient(clientData);
			setShowNewClientForm(false);
			handleClose();
		} catch (error) {
			console.error('Failed to create client:', error);
		}
	};

	const handleClientCreationCancel = () => {
		setShowNewClientForm(false);
	};

	const handleSelectClient = (clientId: string) => {
		onSelectClient(clientId);
		// Don't call onClose directly, let the parent handle it
	};

	const handleClose = () => {
		setShowNewClientForm(false);
		onClose();
	};

	return (
		<Modal
			visible={internalVisible}
			transparent={true}
			animationType="none"
			statusBarTranslucent={true}
			presentationStyle="overFullScreen"
			onRequestClose={handleClose}
		>
			<View style={styles.modalContainer}>
				<TouchableWithoutFeedback onPress={handleClose}>
					<View style={{ flex: 1 }} />
				</TouchableWithoutFeedback>

				<Animated.View style={[styles.sheetContainer, {
					transform: [{
						translateY: animation.interpolate({
							inputRange: [0, 1],
							outputRange: [600, 0],
						}),
					}],
				}]}>
					<View style={styles.sheetHeader}>
						<Typography variant="body" bold>
							{showNewClientForm ? 'Create New Client' : 'Select Client'}
						</Typography>
						<TouchableOpacity onPress={handleClose} style={styles.closeButton}>
							<Ionicons name="close" size={24} color={colors.text.secondary} />
						</TouchableOpacity>
					</View>

					{showNewClientForm ? (
						// Client Creation Form
						<KeyboardAwareView
							showsVerticalScrollIndicator={false}
							bottomOffset={20}
						>
							<ClientCreationForm
								onSubmit={handleClientCreationSubmit}
								onCancel={handleClientCreationCancel}
								cancelButtonText="Back"
							/>
						</KeyboardAwareView>
					) : (
						// Client Selection List
						<KeyboardAwareView
							style={styles.clientList}
							contentContainerStyle={{ padding: 16 }}
							showsVerticalScrollIndicator={false}
						>
							{/* Add New Client Button - At the top */}
							<TouchableOpacity
								style={styles.addNewButton}
								onPress={handleCreateNewClient}
							>
								<View style={styles.addNewContent}>
									<Ionicons name="add-circle-outline" size={24} color={colors.primary} />
									<View style={styles.addNewText}>
										<Typography variant="body" color="primary" bold>Add New Client</Typography>
										<Typography variant="bodySmall" color="secondary">Create a new client profile</Typography>
									</View>
								</View>
							</TouchableOpacity>

							{availableClients.map((client: any) => (
								<TouchableOpacity
									key={client.id}
									style={[
										styles.clientItem,
										selectedClientId === client.id && styles.activeClientItem
									]}
									onPress={() => handleSelectClient(client.id)}
								>
									<Avatar
										name={client.name}
										photo={client.photo}
										size={40}
									/>
									<View style={styles.clientItemContent}>
										<Typography variant="body" bold>{client.displayName}</Typography>
										<Typography variant="bodySmall" color="secondary">{client.contact?.email}</Typography>
									</View>
									{selectedClientId === client.id && (
										<Ionicons name="checkmark" size={20} color={colors.primary} />
									)}
								</TouchableOpacity>
							))}

							{availableClients.length === 0 && (
								<View style={styles.emptyClients}>
									<Typography variant="body" color="secondary" center>
										No clients added yet
									</Typography>
									<Typography variant="bodySmall" color="secondary" center style={{ marginTop: 8 }}>
										Add your first client to get started
									</Typography>
								</View>
							)}
						</KeyboardAwareView>
					)}
				</Animated.View>
			</View>
		</Modal>
	);
});

const styles = StyleSheet.create({
	modalContainer: {
		position: 'absolute',
		top: 0,
		left: 0,
		right: 0,
		bottom: 0,
		backgroundColor: 'rgba(0, 0, 0, 0.5)',
	},
	sheetContainer: {
		position: 'absolute',
		bottom: 0,
		left: 0,
		right: 0,
		backgroundColor: colors.cardBackground,
		borderTopLeftRadius: 20,
		borderTopRightRadius: 20,
		paddingTop: 6,
		// paddingHorizontal: 16,
		// paddingBottom: 16,
		maxHeight: '90%',
	},
	sheetHeader: {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		paddingVertical: 12,
		paddingHorizontal: 16,
		borderBottomWidth: 1,
		borderBottomColor: colors.divider,
	},
	closeButton: {
		padding: 4,
	},
	clientList: {
		marginTop: 6,
		maxHeight: 400,
	},
	clientItem: {
		flexDirection: 'row',
		alignItems: 'center',
		paddingVertical: 8,
		borderRadius: 8,
	},
	activeClientItem: {
		backgroundColor: 'rgba(52, 144, 243, 0.05)',
	},
	clientItemContent: {
		flex: 1,
		marginLeft: 12,
	},
	emptyClients: {
		paddingVertical: 24,
		alignItems: 'center',
	},
	addNewButton: {
		flexDirection: 'row',
		alignItems: 'center',
		padding: 8,
		borderBottomWidth: 1,
		borderBottomColor: colors.divider,
		marginBottom: 8,
	},
	addNewContent: {
		flexDirection: 'row',
		alignItems: 'center',
	},
	addNewText: {
		marginLeft: 8,
	},
}); 