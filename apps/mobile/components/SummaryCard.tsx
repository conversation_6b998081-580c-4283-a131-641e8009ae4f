import React from 'react';
import { StyleSheet, View, ViewStyle } from 'react-native';
import { ThemedText } from './ThemedText';
import { ThemedView } from './ThemedView';

type SummaryCardProps = {
  title: string;
  value: string;
  percentChange: number;
  style?: ViewStyle;
};

export function SummaryCard({ title, value, percentChange, style }: SummaryCardProps) {
  const isPositive = percentChange >= 0;
  
  return (
    <ThemedView style={[styles.container, style]}>
      <ThemedText style={styles.title}>{title}</ThemedText>
      <ThemedText style={styles.value}>{value}</ThemedText>
      <View style={styles.percentContainer}>
        <View style={styles.arrowContainer}>
          {isPositive ? (
            <ThemedText style={[styles.arrow, styles.positive]}>↑</ThemedText>
          ) : (
            <ThemedText style={[styles.arrow, styles.negative]}>↓</ThemedText>
          )}
        </View>
        <ThemedText style={[
          styles.percentText,
          isPositive ? styles.positive : styles.negative
        ]}>
          {Math.abs(percentChange)}%
        </ThemedText>
      </View>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  title: {
    fontSize: 15,
    color: '#8E8E93',
    marginBottom: 6,
  },
  value: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 6,
  },
  percentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  arrowContainer: {
    marginRight: 2,
  },
  arrow: {
    fontSize: 12,
    fontWeight: '600',
  },
  percentText: {
    fontSize: 13,
    fontWeight: '600',
  },
  positive: {
    color: '#34C759',
  },
  negative: {
    color: '#FF3B30',
  },
}); 