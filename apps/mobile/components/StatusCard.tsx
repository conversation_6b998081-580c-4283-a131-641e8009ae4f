import { colors } from '@/constants/Colors';
import React from 'react';
import { StyleSheet, ViewStyle } from 'react-native';
import { ThemedText } from './ThemedText';
import { ThemedView } from './ThemedView';

type StatusCardProps = {
  title: string;
  amount: string;
  style?: ViewStyle;
  bgColor?: string;
};

export function StatusCard({ title, amount, style }: StatusCardProps) {
  return (
    <ThemedView style={[styles.container, style, { backgroundColor: colors.primaryLight }]}>
      <ThemedText style={styles.title}>{title}</ThemedText>
      <ThemedText style={styles.amount}>{amount}</ThemedText>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingVertical: 12,
    paddingHorizontal: 0,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    minHeight: 80,
    marginHorizontal: 2,
    shadowColor: colors.primary,
    shadowOpacity: 0.08,
    shadowRadius: 6,
    shadowOffset: { width: 0, height: 2 }
  },
  title: {
    fontSize: 14,
    fontWeight: '400',
    marginBottom: 6,
    color: 'white',
    letterSpacing: 0.1,
  },
  amount: {
    fontSize: 20,
    fontWeight: '700',
    color: 'white',
    letterSpacing: 0.2,
  },
}); 