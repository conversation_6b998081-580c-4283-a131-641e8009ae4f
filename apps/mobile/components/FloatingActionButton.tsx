import { colors } from '@/constants/Colors';
import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';

// Define the icon name type based on Ionicons 
type IoniconsName = React.ComponentProps<typeof Ionicons>['name'];

interface FloatingActionButtonProps {
  onPress: () => void;
  iconName: IoniconsName;
  secondaryIconName?: IoniconsName;
  color?: string;
  size?: number;
  bottom?: number;
  right?: number;
}

export function FloatingActionButton({
  onPress,
  iconName,
  secondaryIconName,
  color = colors.primary,
  size = 65,
  bottom = 90,
  right = 20,
}: FloatingActionButtonProps) {
  return (
    <TouchableOpacity
      style={[
        styles.fab,
        {
          width: size,
          height: size,
          borderRadius: size / 2,
          backgroundColor: color,
          bottom: bottom,
          right: right,
        },
      ]}
      onPress={onPress}
      activeOpacity={0.8}
    >
      {secondaryIconName ? (
        <View style={styles.iconContainer}>
          {/* Main icon (screen icon) */}
          <Ionicons name={secondaryIconName} size={size * 0.40} color="#FFFFFF" />
          
          {/* Plus icon overlay */}
          <View style={styles.plusIconContainer}>
            <View style={styles.plusIconBackground}>
              <Ionicons name="add-circle-outline" size={size * 0.25} color="#FFFFFF" />
            </View>
          </View>
        </View>
      ) : (
        <Ionicons name={iconName} size={size * 0.42} color="#FFFFFF" />
      )}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  fab: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: colors.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
    zIndex: 1000,
  },
  iconContainer: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
  },
  plusIconContainer: {
    position: 'absolute',
    top: -10,
    right: -10,
  },
  plusIconBackground: {
    // backgroundColor: colors.primary,
    // borderRadius: 50,
    // padding: 2,
    // borderWidth: 1.5,
    // borderColor: 'white',
  },
}); 