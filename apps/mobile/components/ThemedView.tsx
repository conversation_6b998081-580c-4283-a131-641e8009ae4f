import { View, type ViewProps } from 'react-native';

import { useThemeColor } from '@/hooks/useThemeColor';

export type ThemedViewProps = ViewProps & {
  lightColor?: string;
};

export function ThemedView({ style, lightColor, ...otherProps }: ThemedViewProps) {
  // Create the props object conditionally
  const themeProps = lightColor ? { light: lightColor } : { light: '' };
  const backgroundColor = useThemeColor(themeProps, 'background');

  return <View style={[{ backgroundColor }, style]} {...otherProps} />;
}
