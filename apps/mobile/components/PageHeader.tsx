import { ThemedText } from '@/components/ThemedText';
import { colors } from '@/constants/Colors';
import { LinearGradient } from 'expo-linear-gradient';
import React, { ReactNode } from 'react';
import { Platform, StyleSheet, View } from 'react-native';
import { OrganizationSelector } from './OrganizationSelector';

interface PageHeaderProps {
  title: string;
  subtitle?: string;
  /** Optional custom content to display after the title and subtitle */
  children?: ReactNode;
  /** Hide organization selector when not needed */
  hideOrganizationSelector?: boolean;
  /** Additional padding for the header */
  paddingTop?: number;
  /** Additional padding for the bottom */
  paddingBottom?: number;
}

export function PageHeader({ 
  title, 
  subtitle, 
  children, 
  hideOrganizationSelector = false,
  paddingTop,
  paddingBottom = 24
}: PageHeaderProps) {
  return (
    <LinearGradient
      colors={[colors.primary, colors.primary]}
      style={[
        styles.headerGradient,
        { 
          paddingTop: paddingTop !== undefined ? paddingTop : Platform.OS === 'ios' ? 10 : 20,
          paddingBottom: paddingBottom
        } 
      ]}
    >
      <View style={styles.headerContent}>
        <View>
          <ThemedText style={styles.title}>{title}</ThemedText>
          {subtitle && (
            <ThemedText style={styles.subtitle}>{subtitle}</ThemedText>
          )}
        </View>
        
        {!hideOrganizationSelector && <OrganizationSelector />}
      </View>
      
      {children && (
        <View style={styles.childrenContainer}>
          {children}
        </View>
      )}
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  headerGradient: {
    paddingHorizontal: 20,
    // borderBottomLeftRadius: 24,
    // borderBottomRightRadius: 24,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: colors.text.white,
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255,255,255,0.85)',
    fontWeight: '400',
  },
  childrenContainer: {
    marginTop: 16,
  }
}); 