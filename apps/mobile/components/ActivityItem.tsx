import React from 'react';
import { Image, StyleSheet, View, ViewStyle } from 'react-native';
import { ThemedText } from './ThemedText';
import { ThemedView } from './ThemedView';
import { IconSymbol } from './ui/IconSymbol';

type ActivityItemProps = {
  title: string;
  date: string;
  amount: string;
  iconName?: any;
  iconColor?: string;
  avatarUrl?: string;
  style?: ViewStyle;
};

export function ActivityItem({ 
  title, 
  date, 
  amount, 
  iconName,
  iconColor = '#4CAF50',
  avatarUrl,
  style 
}: ActivityItemProps) {
  return (
    <ThemedView style={[styles.container, style]}>
      <View style={styles.avatarContainer}>
        {avatarUrl ? (
          <Image source={{ uri: avatarUrl }} style={styles.avatar} />
        ) : (
          <View style={styles.avatarPlaceholder} />
        )}
      </View>
      
      <View style={styles.contentContainer}>
        <View style={styles.titleRow}>
          {iconName && <IconSymbol name={iconName} size={16} color={iconColor} style={styles.icon} />}
          <ThemedText style={styles.title} numberOfLines={1}>{title}</ThemedText>
        </View>
        
        <View style={styles.detailsRow}>
          <ThemedText style={styles.date}>{date}</ThemedText>
          <ThemedText style={styles.dot}>•</ThemedText>
          <ThemedText style={styles.amount}>{amount}</ThemedText>
        </View>
      </View>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    padding: 14,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F2F2F7',
    backgroundColor: 'white',
    borderRadius: 16,
    marginBottom: 10,
  },
  avatarContainer: {
    marginRight: 14,
  },
  avatar: {
    width: 44,
    height: 44,
    borderRadius: 22,
  },
  avatarPlaceholder: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#F2F2F7',
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 5,
  },
  icon: {
    marginRight: 6,
  },
  title: {
    fontSize: 15,
    fontWeight: '500',
    flex: 1,
  },
  detailsRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  date: {
    fontSize: 13,
    color: '#8E8E93',
  },
  dot: {
    marginHorizontal: 6,
    color: '#C7C7CC',
  },
  amount: {
    fontSize: 13,
    fontWeight: '500',
    color: '#333',
  },
}); 