import { colors } from '@/constants/Colors';
import React from 'react';
import {
	Animated,
	DimensionValue,
	Modal as RNModal,
	ModalProps as RNModalProps,
	StyleSheet,
	TouchableWithoutFeedback,
	View,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

interface ModalProps extends Omit<RNModalProps, 'children'> {
	visible: boolean;
	onClose: () => void;
	children: React.ReactNode;
	animationValue?: Animated.Value;
	maxHeight?: DimensionValue;
	showHeader?: boolean;
	headerContent?: React.ReactNode;
}

export function Modal({
	visible,
	onClose,
	children,
	animationValue,
	maxHeight = '90%',
	showHeader = true,
	headerContent,
	...modalProps
}: ModalProps) {
	return (
		<RNModal
			visible={visible}
			transparent={true}
			animationType="none"
			statusBarTranslucent={true}
			presentationStyle="overFullScreen"
			onRequestClose={onClose}
			{...modalProps}
		>
			<View style={styles.modalContainer}>
				<TouchableWithoutFeedback onPress={onClose}>
					<View style={{ flex: 1 }} />
				</TouchableWithoutFeedback>

				<Animated.View
					style={[
						styles.sheetContainer,
						animationValue && {
							transform: [{
								translateY: animationValue.interpolate({
									inputRange: [0, 1],
									outputRange: [600, 0],
								}),
							}],
						}
					]}
				>
					<View style={{ maxHeight }}>
						<SafeAreaView edges={['bottom', 'top']}>
							{showHeader && headerContent && (
								<View style={styles.headerContainer}>
									{headerContent}
								</View>
							)}
							{children}
						</SafeAreaView>
					</View>
				</Animated.View>
			</View>
		</RNModal>
	);
}

const styles = StyleSheet.create({
	modalContainer: {
		position: 'absolute',
		top: 0,
		left: 0,
		right: 0,
		bottom: 0,
		backgroundColor: 'rgba(0, 0, 0, 0.5)',
	},
	sheetContainer: {
		position: 'absolute',
		bottom: 0,
		left: 0,
		right: 0,
		backgroundColor: colors.cardBackground,
		borderTopLeftRadius: 20,
		borderTopRightRadius: 20,
		paddingTop: 6,
		paddingHorizontal: 16,
		paddingBottom: 16,
	},
	headerContainer: {
		borderBottomWidth: 1,
		borderBottomColor: colors.divider,
		paddingVertical: 12,
		marginBottom: 6,
	},
}); 