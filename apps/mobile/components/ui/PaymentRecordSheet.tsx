import { colors } from '@/constants/Colors';
import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import {
  Modal,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import { PaymentRecordData, PaymentRecordForm } from './PaymentRecordForm';
import { Typography } from './Typography';

interface Invoice {
  id: string;
  invoiceNumber: string;
  clientName: string;
  amount: string;
  status: string;
}

interface PaymentRecordSheetProps {
  visible: boolean;
  onClose: () => void;
  onSubmit: (data: PaymentRecordData) => void;
  invoice: Invoice;
  invoiceTotal: number;
  outstandingBalance: number;
  paymentStatus?: 'unpaid' | 'partially_paid' | 'paid' | 'overpaid' | 'refunded';
  overpaidAmount?: number;
}

export function PaymentRecordSheet({ 
  visible, 
  onClose, 
  onSubmit, 
  invoice,
  invoiceTotal,
  outstandingBalance,
  paymentStatus = 'unpaid',
  overpaidAmount = 0
}: PaymentRecordSheetProps) {

  const handleSubmit = (data: PaymentRecordData) => {
    onSubmit(data);
    onClose();
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color={colors.text.primary} />
          </TouchableOpacity>
          <Typography variant="h4" bold style={styles.title}>
            Record Payment
          </Typography>
          <View style={styles.placeholder} />
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <PaymentRecordForm
            onSubmit={handleSubmit}
            invoiceTotal={invoiceTotal}
            outstandingBalance={outstandingBalance}
            selectedInvoice={invoice}
            disableInvoiceSelection={true}
            paymentStatus={paymentStatus}
            overpaidAmount={overpaidAmount}
          />
        </ScrollView>
      </SafeAreaView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background

  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.divider,
  },
  closeButton: {
    padding: 4,
  },
  title: {
    flex: 1,
    textAlign: 'center',
  },
  placeholder: {
    width: 32,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
}); 