import { colors } from '@/constants/Colors';
import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import { StyleSheet, Text, View, ViewStyle } from 'react-native';

// Status types
export type StatusType = 'paid' | 'pending' | 'overdue' | 'draft';

// Status config object
export const STATUS_CONFIG = {
  paid: {
    label: 'Paid',
    color: colors.success,
    bgColor: colors.statusBackground.paid,
    icon: 'checkmark-circle-outline' as React.ComponentProps<typeof Ionicons>['name'],
  },
  pending: {
    label: 'Pending',
    color: colors.primary,
    bgColor: colors.statusBackground.pending,
    icon: 'time-outline' as React.ComponentProps<typeof Ionicons>['name'],
  },
  overdue: {
    label: 'Overdue',
    color: colors.error,
    bgColor: colors.statusBackground.overdue,
    icon: 'alert-circle-outline' as React.ComponentProps<typeof Ionicons>['name'],
  },
  draft: {
    label: 'Draft',
    color: colors.text.secondary,
    bgColor: 'rgba(200, 200, 210, 0.2)',
    icon: 'document-outline' as React.ComponentProps<typeof Ionicons>['name'],
  },
};

interface StatusDotProps {
  status: StatusType;
  size?: number;
  style?: ViewStyle;
}

export function StatusDot({ status, size = 6, style }: StatusDotProps) {
  return (
    <View 
      style={[
        styles.dot,
        { 
          backgroundColor: STATUS_CONFIG[status].color,
          width: size,
          height: size,
          borderRadius: size / 2
        },
        style
      ]}
    />
  );
}

interface StatusBadgeProps {
  status: StatusType;
  showIcon?: boolean;
  showLabel?: boolean;
  size?: 'small' | 'medium' | 'large';
  style?: ViewStyle;
}

export function StatusBadge({ 
  status, 
  showIcon = true, 
  showLabel = true,
  size = 'medium',
  style 
}: StatusBadgeProps) {
  const config = STATUS_CONFIG[status];
  
  const getIconSize = () => {
    switch (size) {
      case 'small': return 12;
      case 'large': return 16;
      default: return 14;
    }
  };
  
  const getFontSize = () => {
    switch (size) {
      case 'small': return 10;
      case 'large': return 14;
      default: return 12;
    }
  };
  
  return (
    <View 
      style={[
        styles.badge,
        { backgroundColor: config.bgColor },
        style
      ]}
    >
      {showIcon && (
        <Ionicons 
          name={config.icon} 
          size={getIconSize()} 
          color={config.color} 
          style={styles.icon} 
        />
      )}
      {showLabel && (
        <Text 
          style={[
            styles.label, 
            { color: config.color, fontSize: getFontSize() }
          ]}
        >
          {config.label}
        </Text>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  dot: {
    width: 6,
    height: 6,
    borderRadius: 3,
  },
  badge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 50,
  },
  icon: {
    marginRight: 4,
  },
  label: {
    fontWeight: '500',
    fontSize: 12,
  },
}); 