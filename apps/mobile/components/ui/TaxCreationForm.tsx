import { colors } from '@/constants/Colors';
import React, { useState } from 'react';
import { StyleSheet, View } from 'react-native';
import { Button } from './Button';
import { Input } from './Input';

interface TaxCreationFormProps {
  onSubmit: (taxData: {
    name: string;
    rate: string;
    description: string;
  }) => void;
  onCancel: () => void;
  submitButtonText?: string;
  cancelButtonText?: string;
}

export function TaxCreationForm({
  onSubmit,
  onCancel,
  submitButtonText = 'Create Tax',
  cancelButtonText = 'Cancel'
}: TaxCreationFormProps) {
  const [taxName, setTaxName] = useState('');
  const [rate, setRate] = useState('');
  const [description, setDescription] = useState('');

  const handleSubmit = () => {
    if (taxName.trim() && rate.trim()) {
      onSubmit({
        name: taxName.trim(),
        rate: rate.trim(),
        description: description.trim()
      });
      
      // Reset form
      setTaxName('');
      setRate('');
      setDescription('');
    }
  };

  const isFormValid = taxName.trim() !== '' && rate.trim() !== '';

  return (
    <View style={styles.container}>
      <Input
        label="Tax Name"
        placeholder="e.g., Sales Tax, VAT, GST"
        value={taxName}
        onChangeText={setTaxName}
        containerStyle={styles.input}
      />
      <Input
        label="Tax Rate (%)"
        placeholder="e.g., 8.5"
        value={rate}
        onChangeText={setRate}
        keyboardType="decimal-pad"
        containerStyle={styles.input}
      />
      <Input
        label="Description (Optional)"
        placeholder="e.g., California State Sales Tax"
        value={description}
        onChangeText={setDescription}
        multiline={true}
        numberOfLines={2}
        containerStyle={styles.input}
      />
      
      <View style={styles.buttons}>
        <Button
          title={cancelButtonText}
          variant="outline"
          onPress={onCancel}
          style={styles.cancelBtn}
        />
        <Button
          title={submitButtonText}
          onPress={handleSubmit}
          disabled={!isFormValid}
          style={{
            ...styles.submitBtn,
            backgroundColor: !isFormValid ? colors.disabled : colors.primary
          }}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingTop: 8,
  },
  input: {
    marginBottom: 12,
  },
  buttons: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 8,
  },
  cancelBtn: {
    flex: 1,
    paddingVertical: 8,
    borderRadius: 12,
  },
  submitBtn: {
    flex: 1,
    paddingVertical: 8,
    borderRadius: 12,
  },
}); 