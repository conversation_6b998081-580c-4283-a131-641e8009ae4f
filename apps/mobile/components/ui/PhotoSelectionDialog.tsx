import { colors } from '@/constants/Colors';
import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import {
    Modal,
    StyleSheet,
    TouchableOpacity,
    TouchableWithoutFeedback,
    View
} from 'react-native';
import { Typography } from './Typography';

interface PhotoSelectionDialogProps {
  visible: boolean;
  onCamera: () => void;
  onLibrary: () => void;
  onCancel: () => void;
}

export function PhotoSelectionDialog({
  visible,
  onCamera,
  onLibrary,
  onCancel
}: PhotoSelectionDialogProps) {
  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onCancel}
    >
      <TouchableWithoutFeedback onPress={onCancel}>
        <View style={styles.overlay}>
          <TouchableWithoutFeedback>
            <View style={styles.dialog}>
              <View style={styles.header}>
                <Typography variant="h4" bold style={styles.title}>
                  Select Photo
                </Typography>
                <Typography variant="body" color="secondary" style={styles.subtitle}>
                  Choose how you want to add a photo
                </Typography>
              </View>
              
              <View style={styles.options}>
                <TouchableOpacity style={styles.option} onPress={onCamera}>
                  <View style={styles.optionIcon}>
                    <Ionicons name="camera" size={24} color={colors.primary} />
                  </View>
                  <View style={styles.optionContent}>
                    <Typography variant="body" bold>Camera</Typography>
                    <Typography variant="bodySmall" color="secondary">
                      Take a new photo
                    </Typography>
                  </View>
                  <Ionicons name="chevron-forward" size={20} color={colors.text.secondary} />
                </TouchableOpacity>
                
                <View style={styles.divider} />
                
                <TouchableOpacity style={styles.option} onPress={onLibrary}>
                  <View style={styles.optionIcon}>
                    <Ionicons name="images" size={24} color={colors.primary} />
                  </View>
                  <View style={styles.optionContent}>
                    <Typography variant="body" bold>Photo Library</Typography>
                    <Typography variant="bodySmall" color="secondary">
                      Choose from existing photos
                    </Typography>
                  </View>
                  <Ionicons name="chevron-forward" size={20} color={colors.text.secondary} />
                </TouchableOpacity>
              </View>
              
              <View style={styles.actions}>
                <TouchableOpacity style={styles.cancelButton} onPress={onCancel}>
                  <Typography variant="body" color="secondary">Cancel</Typography>
                </TouchableOpacity>
              </View>
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  dialog: {
    backgroundColor: colors.cardBackground,
    borderRadius: 16,
    width: '100%',
    maxWidth: 340,
    overflow: 'hidden',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 20,
    elevation: 8,
  },
  header: {
    padding: 24,
    paddingBottom: 16,
    alignItems: 'center',
  },
  title: {
    marginBottom: 8,
  },
  subtitle: {
    textAlign: 'center',
  },
  options: {
    paddingHorizontal: 8,
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    marginHorizontal: 8,
  },
  optionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: colors.primaryVeryLight,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  optionContent: {
    flex: 1,
  },
  divider: {
    height: 1,
    backgroundColor: colors.divider,
    marginHorizontal: 16,
  },
  actions: {
    padding: 16,
    alignItems: 'center',
  },
  cancelButton: {
    padding: 12,
  },
}); 