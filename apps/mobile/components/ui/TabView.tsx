import { colors } from '@/constants/Colors';
import React, { ReactNode } from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { Typography } from './Typography';

interface TabItem {
  key: string;
  title: string;
  content: ReactNode;
}

interface TabViewProps {
  tabs: TabItem[];
  activeTab: string;
  onTabChange: (tabKey: string) => void;
  containerStyle?: any;
}

export function TabView({ tabs, activeTab, onTabChange, containerStyle }: TabViewProps) {
  return (
    <View style={[styles.container, containerStyle]}>
      <View style={styles.tabsContainer}>
        {tabs.map((tab) => (
          <TouchableOpacity
            key={tab.key}
            style={[
              styles.tab,
              activeTab === tab.key ? styles.activeTab : null
            ]}
            onPress={() => onTabChange(tab.key)}
          >
            <Typography
              variant="body"
              color={activeTab === tab.key ? 'primary' : 'secondary'}
              style={styles.tabText}
            >
              {tab.title}
            </Typography>
            {activeTab === tab.key && <View style={styles.activeIndicator} />}
          </TouchableOpacity>
        ))}
      </View>
      <View style={styles.contentContainer}>
        {tabs.find((tab) => tab.key === activeTab)?.content}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  tabsContainer: {
    flexDirection: 'row',
    backgroundColor: 'white',
    borderRadius: 24,
    padding: 4,
    marginBottom: 16,
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    position: 'relative',
  },
  activeTab: {
    backgroundColor: 'transparent',
  },
  tabText: {
    textAlign: 'center',
    fontWeight: '600',
  },
  activeIndicator: {
    position: 'absolute',
    bottom: 0,
    height: 3,
    width: '60%',
    backgroundColor: colors.primary,
    borderRadius: 3,
  },
  contentContainer: {
    flex: 1,
  },
}); 