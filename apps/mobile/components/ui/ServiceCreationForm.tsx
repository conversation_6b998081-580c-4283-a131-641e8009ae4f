import { colors } from '@/constants/Colors';
import React, { useState } from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { Button } from './Button';
import { Input } from './Input';
import { Typography } from './Typography';

interface ServiceCreationFormProps {
  onSubmit: (serviceData: {
    name: string;
    description: string;
    pricing: {
      rate: number;
      unit: string;
      currency: string;
    };
    isActive: boolean;
    taxable: boolean;
    tags: string[];
  }) => void;
  onCancel: () => void;
  submitButtonText?: string;
  cancelButtonText?: string;
  initialData?: {
    name: string;
    description: string;
    pricing: {
      rate: number;
      unit: string;
      currency: string;
    };
    isActive: boolean;
  };
}

export function ServiceCreationForm({
  onSubmit,
  onCancel,
  submitButtonText = 'Create Service',
  cancelButtonText = 'Cancel',
  initialData
}: ServiceCreationFormProps) {
  const [serviceName, setServiceName] = useState(initialData?.name || '');
  const [description, setDescription] = useState(initialData?.description || '');
  const [rate, setRate] = useState(initialData?.pricing.rate.toString() || '');
  const [selectedUnit, setSelectedUnit] = useState(initialData?.pricing.unit || 'hour');
  const [isCustomUnit, setIsCustomUnit] = useState(() => {
    const standardUnits = ['hour', 'day', 'month', 'fixed'];
    return initialData?.pricing.unit ? !standardUnits.includes(initialData.pricing.unit) : false;
  });
  const [isActive, setIsActive] = useState(initialData?.isActive ?? true);



  const handleUnitSelection = (unit: string) => {
    if (unit === 'custom') {
      setIsCustomUnit(true);
      setSelectedUnit('');
    } else {
      setIsCustomUnit(false);
      setSelectedUnit(unit);
    }
  };

  const handleSubmit = () => {
    if (serviceName.trim() && rate.trim() && selectedUnit.trim()) {
      onSubmit({
        name: serviceName.trim(),
        description: description.trim(),
        pricing: {
          rate: parseFloat(rate.trim()),
          unit: selectedUnit.trim(),
          currency: 'USD', // Use default USD currency
        },
        isActive,
        taxable: true,
        tags: [],
      });

      // Only reset form if it's in creation mode (no initial data)
      if (!initialData) {
        setServiceName('');
        setDescription('');
        setRate('');
        setSelectedUnit('hour');
        setIsCustomUnit(false);
        setIsActive(true);
      }
    }
  };

  const isFormValid = serviceName.trim() !== '' && rate.trim() !== '' && selectedUnit.trim() !== '';

  return (
    <View style={styles.container}>
      <Input
        label="Service Name"
        placeholder="e.g., Web Development, Logo Design"
        value={serviceName}
        onChangeText={setServiceName}
        containerStyle={styles.input}
      />
      <Input
        label="Description"
        placeholder="Brief description of the service"
        value={description}
        onChangeText={setDescription}
        multiline={true}
        numberOfLines={2}
        containerStyle={styles.input}
      />

      {/* Rate Field */}
      <View style={styles.rateContainer}>
        <Input
          label="Rate"
          placeholder="e.g., 75.00"
          value={rate}
          onChangeText={setRate}
          keyboardType="decimal-pad"
        />
      </View>

      {/* Unit Selection */}
      <View style={styles.unitContainer}>
        <Typography variant="label" color="primary" style={styles.unitLabel}>
          Unit
        </Typography>
        <View style={styles.unitOptions}>
          {['hour', 'day', 'month', 'fixed', 'custom'].map((unit) => (
            <TouchableOpacity
              key={unit}
              style={[
                styles.unitOption,
                (unit === 'custom' ? isCustomUnit : selectedUnit === unit) && styles.unitOptionSelected
              ]}
              onPress={() => handleUnitSelection(unit)}
            >
              <Typography
                variant="bodySmall"
                color={(unit === 'custom' ? isCustomUnit : selectedUnit === unit) ? 'primary' : 'secondary'}
                bold={(unit === 'custom' ? isCustomUnit : selectedUnit === unit)}
              >
                {unit.charAt(0).toUpperCase() + unit.slice(1)}
              </Typography>
            </TouchableOpacity>
          ))}
        </View>

        {/* Custom Unit Input */}
        {isCustomUnit && (
          <Input
            label="Custom Unit"
            placeholder="e.g., project, week, page"
            value={selectedUnit}
            onChangeText={setSelectedUnit}
            containerStyle={styles.customUnitInput}
          />
        )}
      </View>



      {/* Settings Toggles */}
      <View style={styles.settingsContainer}>
        <View style={styles.settingRow}>
          <View style={styles.settingContent}>
            <Typography variant="body" color="primary" bold>
              Active Service
            </Typography>
            <Typography variant="bodySmall" color="secondary">
              Service is available for use
            </Typography>
          </View>
          <TouchableOpacity
            style={styles.modernToggleContainer}
            onPress={() => setIsActive(!isActive)}
            activeOpacity={0.7}
          >
            <View style={[
              styles.modernToggle,
              isActive && styles.modernToggleActive
            ]}>
              <View style={[
                styles.modernToggleThumb,
                isActive && styles.modernToggleThumbActive
              ]} />
            </View>
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.buttons}>
        <Button
          title={cancelButtonText}
          variant="outline"
          onPress={onCancel}
          style={styles.cancelBtn}
        />
        <Button
          title={submitButtonText}
          onPress={handleSubmit}
          disabled={!isFormValid}
          style={styles.submitBtn}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  input: {
    marginBottom: 12,
  },
  unitContainer: {
    marginBottom: 12,
  },
  unitLabel: {
    marginBottom: 8,
  },
  unitOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  unitOption: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: colors.divider,
    borderRadius: 20,
    backgroundColor: colors.cardBackground,
  },
  unitOptionSelected: {
    backgroundColor: 'rgba(52, 144, 243, 0.1)',
    borderColor: colors.primary,
  },
  buttons: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 8,
  },
  cancelBtn: {
    flex: 1,
    paddingVertical: 8,
    borderRadius: 12,
  },
  submitBtn: {
    flex: 1,
    paddingVertical: 8,
    borderRadius: 12,
  },
  disabledBtn: {
    backgroundColor: colors.disabled,
  },
  settingsContainer: {
    marginBottom: 16,
    gap: 12,
  },
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  customUnitInput: {
    marginTop: 8,
  },
  rateContainer: {
    marginBottom: 12,
  },
  modernToggleContainer: {
    width: 44,
    height: 24,
  },
  modernToggle: {
    width: 44,
    height: 24,
    borderRadius: 12,
    backgroundColor: colors.divider,
    justifyContent: 'center',
    paddingHorizontal: 2,
    position: 'relative',
  },
  modernToggleActive: {
    backgroundColor: colors.primary,
  },
  modernToggleThumb: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: colors.cardBackground,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
    position: 'absolute',
    left: 2,
    transform: [{ translateX: 0 }],
  },
  modernToggleThumbActive: {
    left: 22,
    backgroundColor: colors.cardBackground,
  },
  settingContent: {
    flex: 1,
  },
}); 