import { colors } from '@/constants/Colors';
import { Ionicons } from '@expo/vector-icons';
import React, { useRef, useState } from 'react';
import { Dimensions, Modal, StyleSheet, TouchableOpacity, TouchableWithoutFeedback, View, ViewStyle } from 'react-native';
import { Typography } from './Typography';

export interface DropdownOption<T = any> {
  /** Unique identifier for the option */
  key: T;
  /** Display title for the option */
  title: string;
  /** Optional description text */
  description?: string;
  /** Optional Ionicons icon name */
  icon?: keyof typeof Ionicons.glyphMap;
  /** Whether the option is disabled */
  disabled?: boolean;
}

interface DropdownProps<T = any> {
  /** Array of dropdown options */
  options: DropdownOption<T>[];
  /** Currently selected value */
  value: T;
  /** Callback when an option is selected */
  onSelect: (value: T) => void;
  /** Placeholder text when no option is selected */
  placeholder?: string;
  /** Optional label above the dropdown */
  label?: string;
  /** Whether the dropdown is disabled */
  disabled?: boolean;
  /** Custom container styles */
  containerStyle?: ViewStyle;
  /** Custom button styles */
  buttonStyle?: ViewStyle;
  /** Custom options container styles */
  optionsStyle?: ViewStyle;
  /** Whether to show icons for options */
  showIcons?: boolean;
  /** Whether to show descriptions for options */
  showDescriptions?: boolean;
  /** Visual variant of the dropdown */
  variant?: 'default' | 'compact' | 'minimal';
}

/**
 * A highly customizable dropdown component with support for icons, descriptions, and multiple variants.
 * Uses a Modal for the options to prevent clipping by parent containers.
 * 
 * @example
 * // Basic usage with string values
 * const [selectedColor, setSelectedColor] = useState('red');
 * const colorOptions = [
 *   { key: 'red', title: 'Red', description: 'A warm color', icon: 'color-fill' },
 *   { key: 'blue', title: 'Blue', description: 'A cool color', icon: 'color-fill' },
 * ];
 * 
 * <Dropdown
 *   options={colorOptions}
 *   value={selectedColor}
 *   onSelect={setSelectedColor}
 *   placeholder="Choose a color..."
 * />
 * 
 * @example
 * // Compact variant without descriptions
 * <Dropdown
 *   options={options}
 *   value={selected}
 *   onSelect={setSelected}
 *   variant="compact"
 *   showDescriptions={false}
 * />
 * 
 * @example
 * // Minimal variant without icons
 * <Dropdown
 *   options={options}
 *   value={selected}
 *   onSelect={setSelected}
 *   variant="minimal"
 *   showIcons={false}
 * />
 */
export function Dropdown<T = any>({
  options,
  value,
  onSelect,
  placeholder = 'Select an option...',
  label,
  disabled = false,
  containerStyle,
  buttonStyle,
  optionsStyle,
  showIcons = true,
  showDescriptions = true,
  variant = 'default',
}: DropdownProps<T>) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [buttonLayout, setButtonLayout] = useState<{ x: number; y: number; width: number; height: number } | null>(null);
  const buttonRef = useRef<View>(null);
  
  const selectedOption = options.find(option => option.key === value);
  const isCompact = variant === 'compact';
  const isMinimal = variant === 'minimal';

  const handleSelect = (selectedValue: T) => {
    onSelect(selectedValue);
    setIsExpanded(false);
  };

  const toggleExpanded = () => {
    if (!disabled) {
      if (!isExpanded && buttonRef.current) {
        // Measure button position before showing modal
        buttonRef.current.measureInWindow((x: number, y: number, width: number, height: number) => {
          setButtonLayout({ x, y, width, height });
          setIsExpanded(true);
        });
      } else {
        setIsExpanded(false);
      }
    }
  };

  const handleClose = () => {
    setIsExpanded(false);
  };

  const renderSelectedDisplay = () => {
    if (selectedOption) {
      return (
        <View style={styles.selectedContent}>
          {showIcons && selectedOption.icon && (
            <Ionicons 
              name={selectedOption.icon} 
              size={isCompact ? 16 : 20} 
              color={disabled ? colors.text.tertiary : colors.primary} 
            />
          )}
          <View style={[styles.selectedTextContainer, !showIcons && styles.noIconPadding]}>
            <Typography 
              variant={isCompact ? "bodySmall" : "body"} 
              bold={!isMinimal}
              color={disabled ? "tertiary" : undefined}
            >
              {selectedOption.title}
            </Typography>
            {showDescriptions && selectedOption.description && !isCompact && (
              <Typography variant="caption" color="secondary">
                {selectedOption.description}
              </Typography>
            )}
          </View>
        </View>
      );
    }

    return (
      <Typography 
        variant={isCompact ? "bodySmall" : "body"} 
        color="secondary"
        style={showIcons ? styles.placeholder : { ...styles.placeholder, marginLeft: 0 }}
      >
        {placeholder}
      </Typography>
    );
  };

  const getButtonStyle = () => {
    const baseStyle = [
      isMinimal ? styles.minimalButton : styles.defaultButton,
      isCompact && styles.compactButton,
      disabled && styles.disabledButton,
      isExpanded && !isMinimal && styles.expandedButton,
      buttonStyle,
    ];
    return baseStyle;
  };

  const getModalOptionsPosition = () => {
    if (!buttonLayout) return {};
    
    const { height: screenHeight } = Dimensions.get('window');
    const optionsHeight = Math.min(options.length * (isCompact ? 36 : 48) + 16, 300);
    
    let top = buttonLayout.y + buttonLayout.height + 4;
    
    // If options would go off bottom of screen, show above button
    if (top + optionsHeight > screenHeight - 100) {
      top = buttonLayout.y - optionsHeight - 4;
    }
    
    return {
      position: 'absolute' as const,
      top,
      left: buttonLayout.x,
      width: buttonLayout.width,
      maxHeight: 300,
    };
  };

  return (
    <View style={[styles.container, containerStyle]}>
      {label && (
        <Typography variant="label" color="secondary" style={styles.label}>
          {label}
        </Typography>
      )}
      
      <TouchableOpacity 
        ref={buttonRef}
        style={getButtonStyle()}
        onPress={toggleExpanded}
        disabled={disabled}
        activeOpacity={0.7}
      >
        {renderSelectedDisplay()}
        
        <Ionicons 
          name={isExpanded ? "chevron-up" : "chevron-down"} 
          size={isCompact ? 16 : 20} 
          color={disabled ? colors.text.tertiary : colors.text.secondary} 
        />
      </TouchableOpacity>

      {/* Options Modal - prevents clipping by parent containers */}
      <Modal
        visible={isExpanded}
        transparent={true}
        animationType="none"
        onRequestClose={handleClose}
      >
        <TouchableWithoutFeedback onPress={handleClose}>
          <View style={styles.modalOverlay}>
            <TouchableWithoutFeedback>
              <View style={[
                isMinimal ? styles.minimalOptions : styles.defaultOptions,
                getModalOptionsPosition(),
                optionsStyle
              ]}>
                {options.map((option, index) => {
                  const isSelected = option.key === value;
                  const isLastItem = index === options.length - 1;
                  
                  return (
                    <TouchableOpacity
                      key={String(option.key)}
                      style={[
                        styles.optionItem,
                        isCompact && styles.compactOptionItem,
                        isSelected && styles.selectedOptionItem,
                        !isLastItem && styles.optionItemBorder,
                        option.disabled && styles.disabledOptionItem,
                      ]}
                      onPress={() => !option.disabled && handleSelect(option.key)}
                      disabled={option.disabled}
                      activeOpacity={0.7}
                    >
                      {showIcons && option.icon && (
                        <Ionicons 
                          name={option.icon} 
                          size={isCompact ? 16 : 18} 
                          color={
                            option.disabled 
                              ? colors.text.tertiary 
                              : isSelected 
                                ? colors.primary 
                                : colors.text.secondary
                          } 
                        />
                      )}
                      
                      <View style={[styles.optionTextContainer, !showIcons && styles.noIconPadding]}>
                        <Typography 
                          variant={isCompact ? "caption" : "bodySmall"} 
                          bold={isSelected}
                          color={
                            option.disabled 
                              ? "tertiary" 
                              : isSelected 
                                ? "primary" 
                                : undefined
                          }
                        >
                          {option.title}
                        </Typography>
                        {showDescriptions && option.description && !isCompact && (
                          <Typography 
                            variant="caption" 
                            color={option.disabled ? "tertiary" : "secondary"}
                            style={styles.optionDescription}
                          >
                            {option.description}
                          </Typography>
                        )}
                      </View>
                      
                      {isSelected && (
                        <Ionicons 
                          name="checkmark" 
                          size={isCompact ? 14 : 16} 
                          color={colors.primary} 
                        />
                      )}
                    </TouchableOpacity>
                  );
                })}
              </View>
            </TouchableWithoutFeedback>
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 12,
    position: 'relative',
  },
  label: {
    marginBottom: 8,
  },
  defaultButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 12,
    backgroundColor: colors.cardBackground,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.divider,
  },
  minimalButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 8,
    backgroundColor: 'transparent',
    borderRadius: 6,
    borderWidth: 1,
    borderColor: colors.divider,
  },
  compactButton: {
    paddingVertical: 8,
    paddingHorizontal: 10,
  },
  disabledButton: {
    opacity: 0.6,
    backgroundColor: colors.background,
  },
  expandedButton: {
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    borderBottomColor: 'transparent',
    zIndex: 1001,
  },
  selectedContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  selectedTextContainer: {
    marginLeft: 12,
    flex: 1,
  },
  noIconPadding: {
    marginLeft: 0,
  },
  placeholder: {
    flex: 1,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  defaultOptions: {
    borderRadius: 8,
    backgroundColor: colors.cardBackground,
    borderWidth: 1,
    borderColor: colors.divider,
    overflow: 'hidden',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  minimalOptions: {
    borderRadius: 8,
    backgroundColor: colors.cardBackground,
    borderWidth: 1,
    borderColor: colors.divider,
    overflow: 'hidden',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
  },
  compactOptionItem: {
    paddingVertical: 8,
    paddingHorizontal: 10,
  },
  selectedOptionItem: {
    backgroundColor: 'rgba(52, 144, 243, 0.05)',
  },
  disabledOptionItem: {
    opacity: 0.5,
  },
  optionItemBorder: {
    borderBottomWidth: 1,
    borderBottomColor: colors.divider,
  },
  optionTextContainer: {
    marginLeft: 12,
    flex: 1,
  },
  optionDescription: {
    marginTop: 2,
    lineHeight: 16,
  },
}); 