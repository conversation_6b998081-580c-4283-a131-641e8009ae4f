import { formatCurrency } from '@/stores';

export function getItemsServicesCollapsedText(lineItems: any[]) {
    // Get non-empty line items
    const activeItems = lineItems.filter(item => 
        item.description && item.description.trim() !== ''
    );
    
    if (activeItems.length === 0) {
        return 'No items added';
    }

    // Calculate total discounts
    const totalDiscounts = activeItems.reduce((sum, item) => {
        const discountValue = parseFloat(item.discount || '0') || 0;
        if (discountValue > 0) {
            const quantity = parseFloat(item.quantity) || 0;
            const price = parseFloat(item.price) || 0;
            const subtotal = quantity * price;
            
            if (item.discountType === 'percentage') {
                return sum + (subtotal * (discountValue / 100));
            } else {
                return sum + discountValue;
            }
        }
        return sum;
    }, 0);

    // Create summary of item names and costs
    const itemSummaries = activeItems.map(item => {
        const hasDiscount = parseFloat(item.discount || '0') > 0;
        const discountDisplay = hasDiscount 
            ? ` (${item.discountType === 'percentage' ? `-${item.discount}%` : `-${formatCurrency(parseFloat(item.discount || '0'))}`})`
            : '';
        return `${item.description}${discountDisplay} ${formatCurrency(parseFloat(item.total))}`;
    });
    
    const summaryText = itemSummaries.join(', ');
    const discountSummary = totalDiscounts > 0 ? ` • Total discounts: ${formatCurrency(totalDiscounts)}` : '';

    return `${summaryText}${discountSummary}`;
} 