import { colors } from '@/constants/Colors';
import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { DatePickerModal } from './DatePickerModal';
import { Typography } from './Typography';

interface DatePickerProps {
  label?: string;
  value: Date;
  onChange: (date: Date) => void;
  containerStyle?: any;
  error?: string;
}

export function DatePicker({ 
  label, 
  value, 
  onChange, 
  containerStyle,
  error
}: DatePickerProps) {
  const [modalVisible, setModalVisible] = useState(false);
  
  // Format the date to display DD/MM/YYYY
  const formatDate = (date: Date) => {
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  };

  const handleConfirm = (date: Date) => {
    onChange(date);
    setModalVisible(false);
  };

  const handleCancel = () => {
    setModalVisible(false);
  };

  return (
    <View style={[styles.container, containerStyle]}>
      {label && (
        <Typography variant="label" color="primary" style={styles.label}>
          {label}
        </Typography>
      )}
      
      <TouchableOpacity 
        style={[styles.pickerButton, error ? styles.pickerButtonError : null]} 
        onPress={() => setModalVisible(true)}
      >
        <Typography variant="body" color="primary">
          {formatDate(value)}
        </Typography>
        <Ionicons name="chevron-down" size={18} color={colors.text.secondary} />
      </TouchableOpacity>
      
      {error && (
        <Typography variant="caption" color="error" style={styles.errorText}>
          {error}
        </Typography>
      )}
      
      <DatePickerModal
        visible={modalVisible}
        value={value}
        onConfirm={handleConfirm}
        onCancel={handleCancel}
        title={label || "Select Date"}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    marginBottom: 8,
  },
  pickerButton: {
    backgroundColor: 'white',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(230, 230, 250, 0.8)',
    paddingHorizontal: 16,
    paddingVertical: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  pickerButtonError: {
    borderColor: colors.error,
  },
  errorText: {
    marginTop: 4,
  },
}); 