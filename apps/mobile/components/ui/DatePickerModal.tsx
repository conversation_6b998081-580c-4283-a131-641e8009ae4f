import { colors } from '@/constants/Colors';
import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import { Modal, Pressable, StyleSheet, TouchableOpacity, View } from 'react-native';
import { Button } from './Button';
import { Typography } from './Typography';

interface DatePickerModalProps {
  visible: boolean;
  value: Date;
  onConfirm: (date: Date) => void;
  onCancel: () => void;
  title?: string;
  showQuickDays?: boolean;
}

export function DatePickerModal({ 
  visible,
  value, 
  onConfirm, 
  onCancel,
  title = "Select Date",
  showQuickDays = false
}: DatePickerModalProps) {
  const [tempDate, setTempDate] = useState(new Date(value));
  
  // Update tempDate when value changes
  React.useEffect(() => {
    setTempDate(new Date(value));
  }, [value, visible]);

  const handleConfirm = () => {
    onConfirm(tempDate);
  };

  const handleCancel = () => {
    setTempDate(value); // Reset temp date
    onCancel();
  };

  // Simple date adjustment functions for the custom picker
  const addDays = (days: number) => {
    const newDate = new Date(tempDate);
    newDate.setDate(newDate.getDate() + days);
    setTempDate(newDate);
  };

  const addMonths = (months: number) => {
    const newDate = new Date(tempDate);
    newDate.setMonth(newDate.getMonth() + months);
    setTempDate(newDate);
  };

  const addYears = (years: number) => {
    const newDate = new Date(tempDate);
    newDate.setFullYear(newDate.getFullYear() + years);
    setTempDate(newDate);
  };

  // Quick day functions - add days to current date
  const setQuickDays = (days: number) => {
    const newDate = new Date(); // Start from current date
    newDate.setDate(newDate.getDate() + days);
    setTempDate(newDate);
  };

  const quickDayOptions = [
    { days: 15, label: '15 Days' },
    { days: 30, label: '30 Days' },
    { days: 45, label: '45 Days' },
  ];

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
      onRequestClose={handleCancel}
    >
      <Pressable style={styles.modalOverlay} onPress={handleCancel}>
        <Pressable style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Typography variant="h4">{title}</Typography>
            <TouchableOpacity onPress={handleCancel}>
              <Ionicons name="close" size={24} color={colors.text.secondary} />
            </TouchableOpacity>
          </View>
          
          {/* Quick Day Buttons - Show only for due dates */}
          {showQuickDays && (
            <View style={styles.quickDaysContainer}>
              <Typography variant="bodySmall" color="secondary" style={styles.quickDaysLabel}>
                Quick due dates from today:
              </Typography>
              <View style={styles.quickDaysRow}>
                {quickDayOptions.map((option) => (
                  <TouchableOpacity
                    key={option.days}
                    style={styles.quickDayButton}
                    onPress={() => setQuickDays(option.days)}
                  >
                    <Typography variant="bodySmall" color="primary" style={styles.quickDayText}>
                      {option.label}
                    </Typography>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          )}
          
          <View style={styles.pickerContainer}>
            {/* Day picker */}
            <View style={styles.pickerColumn}>
              <TouchableOpacity style={styles.pickerArrow} onPress={() => addDays(1)}>
                <Ionicons name="chevron-up" size={24} color={colors.primary} />
              </TouchableOpacity>
              <Typography variant="h3" style={styles.pickerValue}>
                {tempDate.getDate().toString().padStart(2, '0')}
              </Typography>
              <TouchableOpacity style={styles.pickerArrow} onPress={() => addDays(-1)}>
                <Ionicons name="chevron-down" size={24} color={colors.primary} />
              </TouchableOpacity>
              <Typography variant="caption" color="secondary" style={styles.pickerLabel}>
                Day
              </Typography>
            </View>
            
            {/* Month picker */}
            <View style={styles.pickerColumn}>
              <TouchableOpacity style={styles.pickerArrow} onPress={() => addMonths(1)}>
                <Ionicons name="chevron-up" size={24} color={colors.primary} />
              </TouchableOpacity>
              <Typography variant="h3" style={styles.pickerValue}>
                {(tempDate.getMonth() + 1).toString().padStart(2, '0')}
              </Typography>
              <TouchableOpacity style={styles.pickerArrow} onPress={() => addMonths(-1)}>
                <Ionicons name="chevron-down" size={24} color={colors.primary} />
              </TouchableOpacity>
              <Typography variant="caption" color="secondary" style={styles.pickerLabel}>
                Month
              </Typography>
            </View>
            
            {/* Year picker */}
            <View style={styles.pickerColumn}>
              <TouchableOpacity style={styles.pickerArrow} onPress={() => addYears(1)}>
                <Ionicons name="chevron-up" size={24} color={colors.primary} />
              </TouchableOpacity>
              <Typography variant="h3" style={styles.pickerValue}>
                {tempDate.getFullYear()}
              </Typography>
              <TouchableOpacity style={styles.pickerArrow} onPress={() => addYears(-1)}>
                <Ionicons name="chevron-down" size={24} color={colors.primary} />
              </TouchableOpacity>
              <Typography variant="caption" color="secondary" style={styles.pickerLabel}>
                Year
              </Typography>
            </View>
          </View>
          
          <View style={styles.pickerActions}>
            <Button 
              title="Cancel" 
              variant="outline" 
              onPress={handleCancel} 
              style={styles.cancelButton}
            />
            <Button 
              title="Confirm" 
              onPress={handleConfirm} 
              style={styles.confirmButton}
            />
          </View>
        </Pressable>
      </Pressable>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 16,
    width: 320,
    padding: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  quickDaysContainer: {
    marginBottom: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.divider,
  },
  quickDaysLabel: {
    marginBottom: 12,
    textAlign: 'center',
  },
  quickDaysRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 8,
  },
  quickDayButton: {
    flex: 1,
    backgroundColor: colors.background,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.primary,
    paddingVertical: 10,
    paddingHorizontal: 12,
    alignItems: 'center',
  },
  quickDayText: {
    fontWeight: '500',
  },
  pickerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    marginBottom: 24,
  },
  pickerColumn: {
    alignItems: 'center',
    width: 60,
  },
  pickerArrow: {
    padding: 8,
  },
  pickerValue: {
    marginVertical: 4,
  },
  pickerLabel: {
    marginTop: 8,
  },
  pickerActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  cancelButton: {
    flex: 1,
    marginRight: 8,
  },
  confirmButton: {
    flex: 1,
    marginLeft: 8,
  },
}); 