import { colors } from '@/constants/Colors';
import { formatCurrency } from '@/stores/settingsStore';
import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import {
    Modal,
    SafeAreaView,
    ScrollView,
    StyleSheet,
    TouchableOpacity,
    View,
} from 'react-native';
import { Typography } from './Typography';

interface PaymentDetailSheetProps {
  visible: boolean;
  onClose: () => void;
  payment: any;
}

const formatPaymentMethod = (method: string): string => {
  const methodMap: Record<string, string> = {
    'bank_transfer': 'Bank Transfer',
    'credit_card': 'Credit Card', 
    'debit_card': 'Debit Card',
    'cash': 'Cash',
    'check': 'Check',
    'paypal': 'PayPal',
    'stripe': 'Stripe',
    'wire_transfer': 'Wire Transfer',
    'online_banking': 'Online Banking',
    'mobile_payment': 'Mobile Payment',
    'cryptocurrency': 'Cryptocurrency',
    'other': 'Other',
  };
  
  return methodMap[method] || method;
};

const formatDate = (date: Date | string): string => {
  const d = typeof date === 'string' ? new Date(date) : date;
  return d.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: 'numeric',
    minute: '2-digit'
  });
};

const getStatusColor = (status: string): string => {
  switch (status) {
    case 'completed': return colors.success;
    case 'pending': return colors.warning;
    case 'failed': return colors.error;
    case 'refunded': return colors.warning;
    case 'disputed': return colors.error;
    default: return colors.text.secondary;
  }
};

const getStatusIcon = (status: string): React.ComponentProps<typeof Ionicons>['name'] => {
  switch (status) {
    case 'completed': return 'checkmark-circle';
    case 'pending': return 'time';
    case 'failed': return 'close-circle';
    case 'refunded': return 'return-down-back';
    case 'disputed': return 'warning';
    default: return 'information-circle';
  }
};

export function PaymentDetailSheet({ visible, onClose, payment }: PaymentDetailSheetProps) {
  if (!payment) return null;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color={colors.text.primary} />
          </TouchableOpacity>
          <Typography variant="h4" bold style={styles.title}>
            Payment Details
          </Typography>
          <View style={styles.placeholder} />
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Amount */}
          <View style={styles.amountSection}>
            <Typography variant="h2" bold color="primary" style={styles.amount}>
              {payment.type === 'refund' ? '-' : '+'}{formatCurrency(Math.abs(payment.amount))}
            </Typography>
            <Typography variant="body" color="secondary" style={styles.amountLabel}>
              {payment.type === 'refund' ? 'Refund Amount' : 'Payment Amount'}
            </Typography>
          </View>

          {/* Status */}
          <View style={styles.statusSection}>
            <View style={styles.statusBadge}>
              <Ionicons 
                name={getStatusIcon(payment.status)} 
                size={20} 
                color={getStatusColor(payment.status)} 
              />
              <Typography 
                variant="body" 
                bold 
                style={{
                  ...styles.statusText,
                  color: getStatusColor(payment.status)
                }}
              >
                {payment.status.charAt(0).toUpperCase() + payment.status.slice(1)}
              </Typography>
            </View>
          </View>

          {/* Details */}
          <View style={styles.section}>
            <Typography variant="h4" bold style={styles.sectionTitle}>
              Transaction Details
            </Typography>
            <View style={styles.detailsCard}>
              <View style={styles.detailRowColumn}>
                <Typography variant="body" color="secondary" style={styles.detailLabel}>Payment Method</Typography>
                <Typography variant="body" bold style={styles.detailValue}>{formatPaymentMethod(payment.method)}</Typography>
              </View>
              <View style={styles.detailRowColumn}>
                <Typography variant="body" color="secondary" style={styles.detailLabel}>Date & Time</Typography>
                <Typography variant="body" bold style={styles.detailValue}>{formatDate(payment.paymentDate)}</Typography>
              </View>
              {payment.reference && (
                <View style={styles.detailRowColumn}>
                  <Typography variant="body" color="secondary" style={styles.detailLabel}>Reference</Typography>
                  <Typography variant="body" bold style={styles.detailValue}>{payment.reference}</Typography>
                </View>
              )}
              {payment.transactionId && (
                <View style={styles.detailRowColumn}>
                  <Typography variant="body" color="secondary" style={styles.detailLabel}>Transaction ID</Typography>
                  <Typography variant="bodySmall" bold style={styles.detailValue}>{payment.transactionId}</Typography>
                </View>
              )}
            </View>
          </View>

          {/* Notes */}
          {payment.notes && (
            <View style={styles.section}>
              <Typography variant="h4" bold style={styles.sectionTitle}>
                Notes
              </Typography>
              <View style={styles.notesCard}>
                <Typography variant="body">{payment.notes}</Typography>
              </View>
            </View>
          )}

          {/* Gateway Response */}
          {payment.gatewayResponse && (
            <View style={styles.section}>
              <Typography variant="h4" bold style={styles.sectionTitle}>
                Gateway Information
              </Typography>
              <View style={styles.detailsCard}>
                {Object.entries(payment.gatewayResponse).map(([key, value]) => (
                  <View key={key} style={styles.detailRowColumn}>
                    <Typography variant="bodySmall" color="secondary" style={styles.detailLabel}>
                      {key.charAt(0).toUpperCase() + key.slice(1)}
                    </Typography>
                    <Typography variant="bodySmall" bold style={styles.detailValue} numberOfLines={1}>
                      {String(value)}
                    </Typography>
                  </View>
                ))}
              </View>
            </View>
          )}
        </ScrollView>
      </SafeAreaView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.divider,
  },
  closeButton: {
    padding: 4,
  },
  title: {
    flex: 1,
    textAlign: 'center',
  },
  placeholder: {
    width: 32,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  amountSection: {
    alignItems: 'center',
    paddingVertical: 32,
    borderBottomWidth: 1,
    borderBottomColor: colors.divider,
  },
  amount: {
    fontSize: 36,
    fontWeight: '800',
    marginBottom: 8,
  },
  amountLabel: {
    fontSize: 16,
  },
  statusSection: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    backgroundColor: colors.primaryVeryLight,
  },
  statusText: {
    marginLeft: 8,
    fontSize: 16,
    fontWeight: '600',
  },
  section: {
    marginTop: 24,
    marginBottom: 16,
  },
  sectionTitle: {
    marginBottom: 12,
  },
  detailsCard: {
    backgroundColor: colors.cardBackground,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: colors.divider,
  },
  detailRowColumn: {
    flexDirection: 'column',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingVertical: 8,
  },
  detailLabel: {
    fontSize: 16,
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  notesCard: {
    backgroundColor: colors.cardBackground,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: colors.divider,
  },
}); 