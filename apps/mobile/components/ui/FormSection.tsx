import React, { ReactNode } from 'react';
import { StyleSheet, View, ViewStyle } from 'react-native';
import { Card } from './Card';
import { Typography } from './Typography';

interface FormSectionProps {
  title?: string;
  children: ReactNode;
  containerStyle?: ViewStyle;
  cardStyle?: ViewStyle;
  noBorder?: boolean;
}

export function FormSection({ title, children, containerStyle, cardStyle, noBorder = false }: FormSectionProps) {
  return (
    <View style={[styles.container, containerStyle]}>
      {title && (
        <Typography variant="h4" style={styles.title}>
          {title}
        </Typography>
      )}
      <Card style={{...styles.card, ...cardStyle, ...(noBorder ? {borderWidth: 0} : {})}}>
        {children}
      </Card>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 8,
  },
  title: {
    marginBottom: 6,
    paddingHorizontal: 4,
  },
  card: {
    marginBottom: 0,
  },
}); 