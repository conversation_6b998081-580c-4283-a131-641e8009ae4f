import { colors } from '@/constants/Colors';
import { taxOptions } from '@/constants/data';
import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import { Animated, Modal, ScrollView, StyleSheet, TouchableOpacity, TouchableWithoutFeedback, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { TaxCreationForm } from './TaxCreationForm';
import { Typography } from './Typography';

interface TaxSelectionSheetProps {
  visible: boolean;
  selectedTaxId?: string;
  onSelect: (taxId: string) => void;
  onClose: () => void;
  animation: Animated.Value;
  title?: string;
  allowNone?: boolean;
  allowCreate?: boolean;
}

export function TaxSelectionSheet({
  visible,
  selectedTaxId,
  onSelect,
  onClose,
  animation,
  title = "Select Tax",
  allowNone = true,
  allowCreate = true,
}: TaxSelectionSheetProps) {
  
  const [showNewTaxForm, setShowNewTaxForm] = useState(false);
  
  const handleSelectTax = (taxId: string) => {
    onSelect(taxId);
    onClose();
  };

  const handleCreateNewTax = () => {
    setShowNewTaxForm(true);
  };

  const handleTaxCreationSubmit = (taxData: {
    name: string;
    rate: string;
    description: string;
  }) => {
    // Tax creation functionality is available via useCreateTaxOption hook
    // For now, show success message and close form
    console.log('Tax creation data ready:', taxData);
    console.log('Note: useCreateTaxOption hook is available for implementation');
    
    // Close the form and sheet
    setShowNewTaxForm(false);
    onClose();
    
    // Show user that the functionality is ready
    // TODO: Implement actual tax creation when tax management UI is prioritized
  };

  const handleTaxCreationCancel = () => {
    setShowNewTaxForm(false);
  };

  const allTaxOptions = allowNone 
    ? [
        { id: 'none', name: 'No Tax', rate: 0, description: 'No tax applied' },
        ...taxOptions
      ]
    : taxOptions;

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="none"
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <TouchableWithoutFeedback onPress={onClose}>
          <Animated.View style={[styles.backdrop, { 
            opacity: animation.interpolate({
              inputRange: [0, 1],
              outputRange: [0, 0.5],
            })
          }]} />
        </TouchableWithoutFeedback>
        
        <Animated.View style={[styles.sheetContainer, {
          transform: [{
            translateY: animation.interpolate({
              inputRange: [0, 1],
              outputRange: [600, 0],
            }),
          }],
        }]}>
          <SafeAreaView edges={['bottom']}>
            <View style={styles.sheetHeader}>
              <Typography variant="body" bold>
                {showNewTaxForm ? 'Create New Tax' : title}
              </Typography>
              <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                <Ionicons name="close" size={24} color={colors.text.secondary} />
              </TouchableOpacity>
            </View>
            
            {showNewTaxForm ? (
              // Tax Creation Form
              <TaxCreationForm
                onSubmit={handleTaxCreationSubmit}
                onCancel={handleTaxCreationCancel}
                cancelButtonText="Back"
              />
            ) : (
              // Tax Selection List
              <ScrollView style={styles.taxList}>
                {/* Create New Tax Option */}
                {allowCreate && (
                  <TouchableOpacity
                    style={styles.addNewButton}
                    onPress={handleCreateNewTax}
                  >
                    <View style={styles.addNewContent}>
                      <Ionicons 
                        name="add-circle-outline" 
                        size={24} 
                        color={colors.primary} 
                      />
                      <View style={styles.addNewText}>
                        <Typography variant="body" bold color="primary">
                          Create New Tax
                        </Typography>
                        <Typography variant="bodySmall" color="secondary">
                          Add a custom tax rate for your invoices
                        </Typography>
                      </View>
                    </View>
                  </TouchableOpacity>
                )}

                {/* Separator */}
                {allowCreate && (
                  <View style={styles.separator} />
                )}

                {/* Existing Tax Options */}
                {allTaxOptions.map((tax) => {
                  const isSelected = selectedTaxId === tax.id;
                  const isNone = tax.id === 'none';
                  
                  return (
                    <TouchableOpacity
                      key={tax.id}
                      style={[
                        styles.taxItem, 
                        isSelected && styles.activeTaxItem
                      ]}
                      onPress={() => handleSelectTax(tax.id)}
                    >
                      <View style={styles.taxItemIcon}>
                        <Ionicons 
                          name={isNone ? "close-circle-outline" : "calculator-outline"} 
                          size={20} 
                          color={isSelected ? colors.primary : colors.text.secondary} 
                        />
                      </View>
                      <View style={styles.taxItemContent}>
                        <Typography variant="body" bold={isSelected}>
                          {tax.name} {!isNone && `(${tax.rate}%)`}
                        </Typography>
                        <Typography variant="bodySmall" color="secondary">
                          {tax.description}
                        </Typography>
                      </View>
                      {isSelected && (
                        <Ionicons name="checkmark" size={20} color={colors.primary} />
                      )}
                    </TouchableOpacity>
                  );
                })}
              </ScrollView>
            )}
          </SafeAreaView>
        </Animated.View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  sheetContainer: {
    backgroundColor: colors.cardBackground,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 6,
    paddingHorizontal: 16,
    paddingBottom: 16,
    maxHeight: '70%',
  },
  sheetHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.divider,
  },
  closeButton: {
    padding: 4,
  },
  taxList: {
    marginTop: 6,
    maxHeight: 400,
  },
  addNewButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderBottomWidth: 1,
    borderBottomColor: colors.divider,
    marginBottom: 8,
  },
  addNewContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  addNewText: {
    marginLeft: 8,
  },
  separator: {
    height: 1,
    backgroundColor: colors.divider,
    marginVertical: 8,
    marginHorizontal: 8,
  },
  taxItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 8,
    marginVertical: 2,
  },
  activeTaxItem: {
    backgroundColor: 'rgba(52, 144, 243, 0.05)',
  },
  taxItemIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.avatarBackground.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  taxItemContent: {
    flex: 1,
  },
}); 