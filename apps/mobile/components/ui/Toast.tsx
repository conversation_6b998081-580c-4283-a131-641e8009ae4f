import { colors } from '@/constants/Colors';
import { Ionicons } from '@expo/vector-icons';
import React, { useCallback, useEffect } from 'react';
import {
    Animated,
    StyleSheet,
    TouchableOpacity
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Typography } from './Typography';

interface ToastProps {
  visible: boolean;
  message: string;
  type?: 'success' | 'error' | 'info';
  onHide: () => void;
  duration?: number;
  position?: 'top' | 'bottom';
}

export function Toast({
  visible,
  message,
  type = 'info',
  onHide,
  duration = 3000,
  position = 'top'
}: ToastProps) {
  const insets = useSafeAreaInsets();
  const translateY = React.useRef(new Animated.Value(position === 'top' ? -100 : 100)).current;

  const hideToast = useCallback(() => {
    Animated.spring(translateY, {
      toValue: position === 'top' ? -100 : 100,
      useNativeDriver: true,
      tension: 100,
      friction: 8,
    }).start(onHide);
  }, [translateY, onHide, position]);

  useEffect(() => {
    if (visible) {
      Animated.spring(translateY, {
        toValue: 0,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();

      const timer = setTimeout(() => {
        hideToast();
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [visible, duration, hideToast, translateY]);

  const getIcon = () => {
    switch (type) {
      case 'success': return 'checkmark-circle';
      case 'error': return 'alert-circle';
      default: return 'information-circle';
    }
  };

  const getBackgroundColor = () => {
    switch (type) {
      case 'success': return colors.success;
      case 'error': return colors.error;
      default: return colors.primary;
    }
  };

  if (!visible) return null;

  const positionStyle = position === 'top' 
    ? { top: insets.top + 10 }
    : { bottom: insets.bottom + 80 }; // Above tab bar

  return (
    <Animated.View
      style={[
        styles.container,
        positionStyle,
        {
          transform: [{ translateY }],
          backgroundColor: getBackgroundColor(),
        }
      ]}
    >
      <TouchableOpacity
        style={styles.content}
        onPress={hideToast}
        activeOpacity={0.9}
      >
        <Ionicons 
          name={getIcon()} 
          size={20} 
          color="white" 
          style={styles.icon}
        />
        <Typography 
          variant="body" 
          color="white" 
          style={styles.message}
          numberOfLines={2}
        >
          {message}
        </Typography>
        <TouchableOpacity onPress={hideToast} style={styles.closeButton}>
          <Ionicons name="close" size={18} color="white" />
        </TouchableOpacity>
      </TouchableOpacity>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    left: 16,
    right: 16,
    zIndex: 1000,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 8,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  icon: {
    marginRight: 12,
  },
  message: {
    flex: 1,
    lineHeight: 20,
  },
  closeButton: {
    marginLeft: 12,
    padding: 2,
  },
}); 