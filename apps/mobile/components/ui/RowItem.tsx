import { colors } from '@/constants/Colors';
import { Ionicons } from '@expo/vector-icons';
import React, { ReactNode } from 'react';
import { StyleSheet, TouchableOpacity, View, ViewStyle } from 'react-native';
import { Typography } from './Typography';

interface RowItemProps {
  children?: ReactNode;
  style?: ViewStyle;
  leftIcon?: keyof typeof Ionicons.glyphMap;
  leftIconColor?: string;
  leftContent?: ReactNode;
  title?: string;
  subtitle?: string;
  rightIcon?: keyof typeof Ionicons.glyphMap | null;
  rightIconColor?: string;
  onPress?: () => void;
  isCollapsed?: boolean;
  showDivider?: boolean;
}

export function RowItem({
  children,
  style,
  leftIcon,
  leftIconColor = colors.primary,
  leftContent,
  title,
  subtitle,
  rightIcon = 'create-outline',
  rightIconColor = colors.primary,
  onPress,
  isCollapsed = true,
  showDivider = true,
}: RowItemProps) {
  return (
    <View style={[styles.container, style]}>
      <TouchableOpacity
        style={styles.header}
        onPress={onPress}
        activeOpacity={0.7}
      >
        <View style={styles.leftContent}>
          {leftContent ? (
            <View style={styles.iconContainer}>
              {leftContent}
            </View>
          ) : leftIcon ? (
            <View style={styles.iconContainer}>
              <Ionicons
                name={leftIcon}
                size={20}
                color={leftIconColor}
              />
            </View>
          ) : null}
          <View style={styles.textContent}>
            {title && (
              <Typography variant="body" color="secondary" style={styles.title}>
                {title}
              </Typography>
            )}
            {subtitle && (
              <Typography variant="body" color="primary" style={styles.subtitle}>
                {subtitle}
              </Typography>
            )}
          </View>
        </View>
        {rightIcon && (
          <Ionicons
            name={rightIcon}
            size={20}
            color={rightIconColor}
          />
        )}
      </TouchableOpacity>
      
      {!isCollapsed && children && (
        <View style={styles.content}>
          {children}
        </View>
      )}
      
      {showDivider && <View style={styles.divider} />}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.cardBackground,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    minHeight: 56,
  },
  leftContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    marginRight: 12,
  },
  textContent: {
    flex: 1,
  },
  title: {
    marginBottom: 2,
  },
  subtitle: {
    fontWeight: '500',
  },
  content: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  divider: {
    height: 1, //StyleSheet.hairlineWidth,
    backgroundColor: colors.divider,
    marginLeft: 16,
  },
}); 