import { colors } from '@/constants/Colors';
import { formatCurrency } from '@/stores';
import React from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { Typography } from './Typography';

export interface LineItem {
  id: string;
  description: string;
  quantity: string;
  price: string;
  total: string;
  serviceId?: string;
  unit?: 'hour' | 'fixed' | 'month' | 'custom';
  taxRate?: number;
  taxAmount?: number;
  taxable?: boolean;
  selectedTaxId?: string; // For per-item tax selection
  itemDescription?: string; // Additional description for the item
  discount?: string; // Discount amount or percentage
  discountType?: 'percentage' | 'fixed'; // Type of discount
}

interface InvoiceLineItemProps {
  item: LineItem;
  onEdit: (id: string) => void;
  itemNumber?: number;
}

export function InvoiceLineItem({ 
  item, 
  onEdit,
  itemNumber = 1
}: InvoiceLineItemProps) {
  const hasContent = item.description.trim() !== '';

  if (!hasContent) {
    return null;
  }

  // Calculate discount information
  const discountValue = parseFloat(item.discount || '0') || 0;
  const hasDiscount = discountValue > 0;

  return (
    <TouchableOpacity 
      style={styles.container}
      onPress={() => onEdit(item.id)}
      activeOpacity={0.7}
    >
      {/* Main Row: Item Name + Calculation/Total */}
      <View style={styles.mainRow}>
        <View style={styles.itemNumber}>
          <Typography variant="caption" color="secondary" bold>
            #{itemNumber}
          </Typography>
        </View>

        <View style={styles.itemContent}>
          <Typography variant="body" bold numberOfLines={2}>
            {item.description}
          </Typography>
          {item.itemDescription && item.itemDescription.trim() !== '' && (
            <Typography variant="bodySmall" color="secondary" numberOfLines={2} style={styles.itemDescription}>
              {item.itemDescription}
            </Typography>
          )}
        </View>

        <View style={styles.calculationColumn}>
          <Typography variant="caption" color="secondary">
            {item.quantity} × {formatCurrency(parseFloat(item.price))}{item.unit && item.unit !== 'fixed' ? ` per ${item.unit}` : ''}
          </Typography>
          {hasDiscount && (
            <Typography variant="caption" color="secondary" style={styles.discountText}>
              -{item.discountType === 'percentage' ? `${item.discount}%` : formatCurrency(discountValue)}
            </Typography>
          )}
          <Typography variant="body" color="primary" bold>
            {formatCurrency(parseFloat(item.total))}
          </Typography>
        </View>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 12,
    marginBottom: 6,
    backgroundColor: colors.cardBackground,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.divider,
  },
  mainRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  itemNumber: {
    marginRight: 12,
    marginTop: 2,
    minWidth: 24,
  },
  itemContent: {
    flex: 1,
    marginRight: 12,
  },
  calculationColumn: {
    flexDirection: 'column',
    alignItems: 'flex-end',
  },
  itemDescription: {
    marginTop: 4,
  },
  discountText: {
    color: colors.success || '#16a34a',
    fontWeight: '500',
  },
}); 