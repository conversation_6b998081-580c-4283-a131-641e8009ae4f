import { colors } from '@/constants/Colors';
import React from 'react';
import { StyleSheet, TouchableOpacity, View, ViewStyle } from 'react-native';

interface SwitchProps {
  value: boolean;
  onValueChange: (value: boolean) => void;
  disabled?: boolean;
  style?: ViewStyle;
  size?: 'small' | 'medium' | 'large';
}

export function Switch({ 
  value, 
  onValueChange, 
  disabled = false, 
  style,
  size = 'medium'
}: SwitchProps) {
  const toggleStyles = getToggleStyles(size);
  
  return (
    <TouchableOpacity
      style={[
        toggleStyles.container,
        value && toggleStyles.containerActive,
        disabled && toggleStyles.containerDisabled,
        style
      ]}
      onPress={() => !disabled && onValueChange(!value)}
      activeOpacity={disabled ? 1 : 0.7}
    >
      <View 
        style={[
          toggleStyles.thumb,
          value && toggleStyles.thumbActive,
          disabled && toggleStyles.thumbDisabled
        ]} 
      />
    </TouchableOpacity>
  );
}

const getToggleStyles = (size: 'small' | 'medium' | 'large') => {
  const dimensions = {
    small: { width: 36, height: 20, thumbSize: 16, activeLeft: 18 },
    medium: { width: 44, height: 24, thumbSize: 20, activeLeft: 22 },
    large: { width: 52, height: 28, thumbSize: 24, activeLeft: 26 }
  };
  
  const { width, height, thumbSize, activeLeft } = dimensions[size];
  
  return StyleSheet.create({
    container: {
      width,
      height,
      borderRadius: height / 2,
      backgroundColor: colors.divider,
      justifyContent: 'center',
      paddingHorizontal: 2,
      position: 'relative',
    },
    containerActive: {
      backgroundColor: colors.primary,
    },
    containerDisabled: {
      backgroundColor: colors.disabled,
      opacity: 0.6,
    },
    thumb: {
      width: thumbSize,
      height: thumbSize,
      borderRadius: thumbSize / 2,
      backgroundColor: colors.cardBackground,
      shadowColor: colors.shadow,
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.2,
      shadowRadius: 2,
      elevation: 2,
      position: 'absolute',
      left: 2,
    },
    thumbActive: {
      left: activeLeft,
      backgroundColor: colors.cardBackground,
    },
    thumbDisabled: {
      backgroundColor: colors.background,
    },
  });
}; 