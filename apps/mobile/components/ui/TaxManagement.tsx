import { colors } from '@/constants/Colors';
import { taxOptions } from '@/constants/data';
import { TaxConfiguration, TaxMethod, useInvoiceStore } from '@/stores';
import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import { Animated, StyleSheet, TouchableOpacity, View } from 'react-native';
import { Dropdown, DropdownOption } from './Dropdown';
import { TaxSelectionSheet } from './TaxSelectionSheet';
import { Typography } from './Typography';

interface TaxManagementProps {
  onTaxConfigChange?: (config: Partial<TaxConfiguration>) => void;
  containerStyle?: object;
}

export function TaxManagement({ onTaxConfigChange, containerStyle }: TaxManagementProps) {
  const invoiceStore = useInvoiceStore();
  
  const { taxConfig, lineItems } = invoiceStore;
  const selectedTax = taxOptions.find(t => t.id === taxConfig.taxId) || taxOptions[0];

  // Sheet state for per-item tax selection
  const [showTaxSheet, setShowTaxSheet] = useState(false);
  const [taxSheetAnimation] = useState(new Animated.Value(0));
  const [selectedItemForTax, setSelectedItemForTax] = useState<string | null>(null);

  const taxMethodOptions: DropdownOption<TaxMethod>[] = [
    {
      key: TaxMethod.NONE,
      title: 'No Tax',
      description: 'No tax applied to invoice',
      icon: 'close-circle-outline',
    },
    {
      key: TaxMethod.ON_TOTAL,
      title: 'Tax on Total',
      description: 'Apply tax percentage to entire invoice amount',
      icon: 'calculator-outline',
    },
    {
      key: TaxMethod.PER_ITEM,
      title: 'Tax per Item',
      description: 'Configure tax individually for each service/item',
      icon: 'list-outline',
    },
    {
      key: TaxMethod.AS_DEDUCTION,
      title: 'Tax as Deduction',
      description: 'Subtract tax amount from total (e.g., withholding tax)',
      icon: 'remove-circle-outline',
    },
  ];

  const handleMethodChange = (method: TaxMethod) => {
    const newConfig = { method };
    invoiceStore.setTaxConfiguration(newConfig);
    onTaxConfigChange?.(newConfig);
  };

  const handleTaxOptionChange = (taxId: string) => {
    const newConfig = { taxId };
    invoiceStore.setTaxConfiguration(newConfig);
    onTaxConfigChange?.(newConfig);
  };

  const handleTaxInclusiveToggle = (inclusive: boolean) => {
    const newConfig = { inclusive };
    invoiceStore.setTaxConfiguration(newConfig);
    onTaxConfigChange?.(newConfig);
  };

  const openTaxSheet = (itemId: string) => {
    setSelectedItemForTax(itemId);
    setShowTaxSheet(true);
    Animated.timing(taxSheetAnimation, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  const openGlobalTaxSheet = () => {
    setSelectedItemForTax(null); // null indicates global tax selection
    setShowTaxSheet(true);
    Animated.timing(taxSheetAnimation, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  const closeTaxSheet = () => {
    Animated.timing(taxSheetAnimation, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start(() => {
      setShowTaxSheet(false);
      setSelectedItemForTax(null);
    });
  };

  const handleItemTaxSelect = (taxId: string) => {
    if (!selectedItemForTax) {
      // Global tax selection
      handleTaxOptionChange(taxId);
      return;
    }

    // Per-item tax selection
    if (taxId === 'none') {
      // Remove tax from item
      invoiceStore.setItemTaxable(selectedItemForTax, false);
      invoiceStore.setItemSelectedTaxId(selectedItemForTax, '');
    } else {
      // Set tax for item
      const selectedTax = taxOptions.find(t => t.id === taxId);
      if (selectedTax) {
        invoiceStore.setItemTaxable(selectedItemForTax, true);
        invoiceStore.setItemTaxRate(selectedItemForTax, selectedTax.rate);
        invoiceStore.setItemSelectedTaxId(selectedItemForTax, taxId);
      }
    }
  };

  const getItemTaxDisplay = (item: any) => {
    if (item.selectedTaxId && item.selectedTaxId !== 'none') {
      const tax = taxOptions.find(t => t.id === item.selectedTaxId);
      return tax ? `${tax.name} (${tax.rate}%)` : 'None';
    }
    return item.taxable ? 'Tax Applied' : 'None';
  };

  const renderTaxRateSelector = () => {
    if (taxConfig.method === TaxMethod.NONE || taxConfig.method === TaxMethod.PER_ITEM) {
      return null;
    }

    return (
      <View style={styles.taxRateSection}>
        <Typography variant="label" color="secondary" style={styles.sectionLabel}>
          Tax Rate {taxConfig.method === TaxMethod.AS_DEDUCTION ? '(Deduction)' : ''}
        </Typography>
        
        <View style={styles.taxRateSelector}>
          <TouchableOpacity 
            style={styles.taxRateButton}
            onPress={openGlobalTaxSheet}
          >
            <View style={styles.taxRateContent}>
              <Typography variant="body">{selectedTax.name}</Typography>
              <Typography variant="caption" color="secondary">
                {selectedTax.description}
              </Typography>
            </View>
            <View style={styles.taxRateDisplay}>
              <Typography variant="body" color="primary" bold>
                {taxConfig.method === TaxMethod.AS_DEDUCTION ? '-' : ''}{selectedTax.rate}%
              </Typography>
            </View>
            <Ionicons name="chevron-forward" size={16} color={colors.text.secondary} style={{ marginLeft: 8 }} />
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const renderTaxInclusiveToggle = () => {
    if (taxConfig.method === TaxMethod.NONE) {
      return null;
    }

    return (
      <View style={styles.taxInclusiveSection}>
        <Typography variant="label" color="secondary" style={styles.sectionLabel}>
          Tax Configuration
        </Typography>
        
        <View style={styles.taxInclusiveSelector}>
          <TouchableOpacity 
            style={styles.taxInclusiveButton}
            onPress={() => handleTaxInclusiveToggle(!taxConfig.inclusive)}
            activeOpacity={0.7}
          >
            <View style={styles.taxInclusiveContent}>
              <Typography variant="body">Tax Inclusive</Typography>
              <Typography variant="caption" color="secondary">
                Prices include tax amount
              </Typography>
            </View>
            <View style={[
              styles.modernToggle,
              taxConfig.inclusive && styles.modernToggleActive
            ]}>
              <View style={[
                styles.modernToggleThumb,
                taxConfig.inclusive && styles.modernToggleThumbActive
              ]} />
            </View>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const renderPerItemConfiguration = () => {
    if (taxConfig.method !== TaxMethod.PER_ITEM) {
      return null;
    }

    const itemsWithDescriptions = lineItems.filter(item => item.description.trim() !== '');

    return (
      <View style={styles.perItemSection}>
        <Typography variant="label" color="secondary" style={styles.sectionLabel}>
          Configure Tax for Each Item
          {taxConfig.inclusive && (
            <Typography variant="caption" color="primary"> (Tax Inclusive)</Typography>
          )}
        </Typography>
        
        {itemsWithDescriptions.length === 0 ? (
          <View style={styles.noItemsNotice}>
            <Ionicons name="information-circle" size={16} color={colors.text.secondary} />
            <Typography variant="bodySmall" color="secondary" style={{ marginLeft: 8 }}>
              Add items above to configure their taxes
            </Typography>
          </View>
        ) : (
          <View style={styles.itemsList}>
            {itemsWithDescriptions.map((item, index) => (
              <View key={item.id} style={[styles.itemTaxRow, index > 0 && styles.itemBorder]}>
                {/* Left: Item Info */}
                <View style={styles.itemInfo}>
                  <Typography variant="bodySmall" numberOfLines={1} style={styles.itemName}>
                    {item.description}
                  </Typography>
                  <Typography variant="caption" color="secondary">
                    ${item.total}
                  </Typography>
                </View>

                {/* Right: Tax Selection and Amount */}
                <View style={styles.taxConfigColumn}>
                  <TouchableOpacity 
                    style={styles.compactTaxSelector}
                    onPress={() => openTaxSheet(item.id)}
                  >
                    <Typography variant="caption" style={styles.taxSelectorText} numberOfLines={1}>
                      {getItemTaxDisplay(item)}
                    </Typography>
                    <Ionicons name="chevron-forward" size={12} color={colors.text.secondary} />
                  </TouchableOpacity>
                  
                  {/* Tax Amount below */}
                  {item.taxable && (
                    <Typography variant="caption" color="primary" bold style={styles.taxAmountText}>
                      Tax: ${invoiceStore.getItemTaxAmount(item.id).toFixed(2)}
                      {taxConfig.inclusive && (
                        <Typography variant="caption" color="secondary"> (incl.)</Typography>
                      )}
                    </Typography>
                  )}
                </View>
              </View>
            ))}
          </View>
        )}
      </View>
    );
  };

  const renderTaxSummary = () => {
    const taxAmount = invoiceStore.getTaxAmount();
    
    if (taxConfig.method === TaxMethod.NONE || taxAmount === 0) {
      return null;
    }

    const isInclusive = taxConfig.inclusive && (taxConfig.method === TaxMethod.ON_TOTAL || taxConfig.method === TaxMethod.AS_DEDUCTION);

    return (
      <View style={styles.taxSummary}>
        <View style={styles.summaryHeader}>
          <Typography variant="bodySmall" color="secondary">
            {taxConfig.method === TaxMethod.AS_DEDUCTION 
              ? 'Tax Deduction' 
              : 'Total Tax Amount'}
          </Typography>
          <Typography 
            variant="bodySmall" 
            style={{
              fontWeight: '600',
              color: taxConfig.method === TaxMethod.AS_DEDUCTION ? colors.warning : colors.primary
            }}
            bold
          >
            {taxConfig.method === TaxMethod.AS_DEDUCTION ? '-' : ''}${Math.abs(taxAmount).toFixed(2)}
          </Typography>
        </View>
        {isInclusive && (
          <View style={styles.inclusiveIndicator}>
            <Ionicons name="information-circle" size={14} color={colors.primary} />
            <Typography variant="caption" color="primary" style={{ marginLeft: 4 }}>
              Tax included in prices above
            </Typography>
          </View>
        )}
      </View>
    );
  };

  const getCurrentItemTaxSelection = () => {
    if (!selectedItemForTax) {
      // Global tax selection
      return taxConfig.taxId;
    }
    // Per-item tax selection  
    const item = lineItems.find(i => i.id === selectedItemForTax);
    return item?.selectedTaxId || (item?.taxable ? 'default' : 'none');
  };

  const getTaxSheetTitle = () => {
    if (!selectedItemForTax) {
      return taxConfig.method === TaxMethod.AS_DEDUCTION ? 'Select Tax (Deduction)' : 'Select Tax Rate';
    }
    return 'Select Tax for Item';
  };

  return (
    <>
      <View style={containerStyle ? { ...styles.container, ...containerStyle } : styles.container}>
        <Dropdown
          options={taxMethodOptions}
          value={taxConfig.method}
          onSelect={handleMethodChange}
          placeholder="Select tax method..."
          showIcons={true}
          showDescriptions={true}
          containerStyle={styles.methodDropdown}
        />
        {renderTaxInclusiveToggle()}
        {renderTaxRateSelector()}
        {renderPerItemConfiguration()}
        {renderTaxSummary()}
      </View>

      {/* Tax Selection Sheet */}
      <TaxSelectionSheet
        visible={showTaxSheet}
        selectedTaxId={getCurrentItemTaxSelection()}
        onSelect={handleItemTaxSelect}
        onClose={closeTaxSheet}
        animation={taxSheetAnimation}
        title={getTaxSheetTitle()}
        allowNone={selectedItemForTax !== null} // Only allow "none" for per-item selection
      />
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    // Removed marginBottom since RowItem content handles spacing
  },
  methodDropdown: {
    marginBottom: 12,
  },
  sectionLabel: {
    marginBottom: 8,
  },
  taxRateSection: {
    marginBottom: 12,
  },
  taxRateSelector: {
    borderRadius: 8,
    backgroundColor: colors.cardBackground,
    borderWidth: 1,
    borderColor: colors.divider,
  },
  taxRateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 12,
  },
  taxRateContent: {
    flex: 1,
  },
  taxRateDisplay: {
    backgroundColor: 'rgba(52, 144, 243, 0.1)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  perItemSection: {
    marginBottom: 12,
  },
  noItemsNotice: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: colors.background,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.divider,
  },
  itemsList: {
    backgroundColor: colors.cardBackground,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.divider,
    overflow: 'hidden',
  },
  itemTaxRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 12,
    gap: 12,
  },
  itemBorder: {
    borderTopWidth: 1,
    borderTopColor: colors.divider,
  },
  itemInfo: {
    flex: 2,
    minWidth: 0, // Allow text truncation
  },
  itemName: {
    marginBottom: 2,
  },
  taxConfigColumn: {
    flex: 1.5,
    alignItems: 'flex-end',
  },
  compactTaxSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    backgroundColor: colors.background,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: colors.divider,
    minHeight: 32,
    width: '100%',
  },
  taxSelectorText: {
    flex: 1,
    textAlign: 'left',
    paddingRight: 8,
  },
  taxAmountText: {
    marginTop: 4,
    textAlign: 'right',
  },
  taxSummary: {
    backgroundColor: 'rgba(52, 144, 243, 0.05)',
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: 'rgba(52, 144, 243, 0.1)',
  },
  summaryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  taxInclusiveSection: {
    marginBottom: 12,
  },
  taxInclusiveSelector: {
    borderRadius: 8,
    backgroundColor: colors.cardBackground,
    borderWidth: 1,
    borderColor: colors.divider,
  },
  taxInclusiveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 12,
  },
  taxInclusiveContent: {
    flex: 1,
  },
  modernToggle: {
    width: 44,
    height: 24,
    borderRadius: 12,
    backgroundColor: colors.divider,
    justifyContent: 'center',
    paddingHorizontal: 2,
    position: 'relative',
  },
  modernToggleActive: {
    backgroundColor: colors.primary,
  },
  modernToggleThumb: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: colors.cardBackground,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
    position: 'absolute',
    left: 2,
    transform: [{ translateX: 0 }],
  },
  modernToggleThumbActive: {
    left: 22,
    backgroundColor: colors.cardBackground,
  },
  inclusiveIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    backgroundColor: 'rgba(52, 144, 243, 0.1)',
    borderRadius: 6,
  },
}); 