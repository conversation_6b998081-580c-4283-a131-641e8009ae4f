import { colors } from '@/constants/Colors';
import { Ionicons } from '@expo/vector-icons';
import React, { useRef, useState } from 'react';
import { Dimensions, Modal, Pressable, StyleSheet, TouchableOpacity, View } from 'react-native';
import { Typography } from './Typography';

export interface ActionMenuItem {
  id: string;
  title: string;
  icon: React.ComponentProps<typeof Ionicons>['name'];
  onPress: () => void;
  color?: string;
  destructive?: boolean;
}

interface ActionMenuProps {
  items: ActionMenuItem[];
  disabled?: boolean;
  style?: any;
  horizontalOffset?: number;
  verticalOffset?: number;
}

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export function ActionMenu({ items, disabled = false, style, horizontalOffset = 8, verticalOffset = -8 }: ActionMenuProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [buttonLayout, setButtonLayout] = useState({ x: 0, y: 0, width: 0, height: 0 });
  const buttonRef = useRef<View>(null);

  const handlePress = () => {
    if (buttonRef.current) {
      buttonRef.current.measureInWindow((x, y, width, height) => {
        setButtonLayout({ x, y, width, height });
        setIsVisible(true);
      });
    }
  };

  const handleItemPress = (item: ActionMenuItem) => {
    setIsVisible(false);
    item.onPress();
  };

  const getMenuPosition = () => {
    const menuWidth = 160;
    const menuHeight = items.length * 44;
    
    // Position to the right of the button, but adjust if it would go off screen
    let left = buttonLayout.x + buttonLayout.width - menuWidth - horizontalOffset;
    if (left < 10) {
      left = 10;
    }
    if (left + menuWidth > screenWidth - 10) {
      left = screenWidth - menuWidth - 10;
    }
    
    // Position below the button with offset
    let top = buttonLayout.y + buttonLayout.height + verticalOffset;
    
    // If menu would go off bottom, position it above
    if (top + menuHeight > screenHeight - 50) {
      top = buttonLayout.y - menuHeight + verticalOffset;
    }
    
    return {
      position: 'absolute' as const,
      left,
      top,
      width: menuWidth,
    };
  };

  return (
    <>
      <Pressable 
        ref={buttonRef}
        style={[styles.dotsButton, style]} 
        onPress={handlePress}
        disabled={disabled}
      >
        <Typography style={styles.dotsText}>⋮</Typography>
      </Pressable>

      <Modal
        visible={isVisible}
        transparent
        animationType="none"
        onRequestClose={() => setIsVisible(false)}
      >
        <Pressable 
          style={styles.overlay}
          onPress={() => setIsVisible(false)}
        >
          <View style={[styles.menuContainer, getMenuPosition()]}>
            {items.map((item, index) => (
              <TouchableOpacity
                key={item.id}
                style={[
                  styles.menuItem,
                  index === 0 && styles.firstItem,
                  index === items.length - 1 && styles.lastItem,
                ]}
                onPress={() => handleItemPress(item)}
                activeOpacity={0.7}
              >
                <Ionicons 
                  name={item.icon} 
                  size={18} 
                  color={item.destructive ? colors.error : (item.color || colors.text.primary)} 
                  style={styles.menuIcon}
                />
                <Typography 
                  variant="bodySmall" 
                  style={{
                    ...styles.menuText,
                    color: item.destructive ? colors.error : (item.color || colors.text.primary)
                  }}
                >
                  {item.title}
                </Typography>
              </TouchableOpacity>
            ))}
          </View>
        </Pressable>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  dotsButton: {
    padding: 8,
    borderRadius: 4,
  },
  dotsText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.text.secondary,
  },
  overlay: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  menuContainer: {
    backgroundColor: colors.cardBackground,
    borderRadius: 8,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 10,
    borderWidth: 1,
    borderColor: colors.divider,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.divider,
    minHeight: 44,
  },
  firstItem: {
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
  },
  lastItem: {
    borderBottomWidth: 0,
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
  },
  menuIcon: {
    marginRight: 10,
  },
  menuText: {
    fontSize: 14,
    fontWeight: '500',
  },
}); 