// Fallback for using MaterialIcons on Android and web.

import { Ionicons } from '@expo/vector-icons';
import { OpaqueColorValue, StyleProp, TextStyle } from 'react-native';

// Define specific icon names we use in our app
export type AppIconName = 
  | 'home'
  | 'grid'
  | 'document-text'
  | 'add'
  | 'person'
  | 'settings-outline'
  | 'time'
  | 'checkmark-circle'
  | 'alert-circle';

// These exact icon names exist in Ionicons
const mapToIonicons = (name: string): string => {
  switch (name) {
    case 'house.fill':
    case 'home':
      return 'home';
    case 'square.grid.2x2.fill':
    case 'grid':
      return 'apps';
    case 'doc.text.fill':
    case 'document':
    case 'file-text':
    case 'file-invoice':
      return 'document-text';
    case 'doc.text':
    case 'document-outline':
      return 'document-text-outline';
    case 'plus':
    case 'add':
      return 'add';
    case 'person.fill':
    case 'person':
    case 'user':
      return 'person';
    case 'gear':
    case 'settings-outline':
    case 'settings':
      return 'settings-outline';
    case 'gear.fill':
    case 'cog':
      return 'settings';
    case 'clock.fill':
    case 'time':
    case 'time-filled':
      return 'time';
    case 'clock':
    case 'time-outline':
      return 'time-outline';
    case 'check-circle':
    case 'check-circle-filled':
      return 'checkmark-circle';
    case 'checkmark.circle':
    case 'check-circle-outline':
      return 'checkmark-circle-outline';
    case 'exclamationmark.circle.fill':
    case 'alert-circle':
    case 'alert-circle-filled':
      return 'alert-circle';
    case 'exclamationmark.circle':
    case 'alert-circle-outline':
      return 'alert-circle-outline';
    case 'chart.bar.fill':
    case 'chart-bar':
      return 'bar-chart';
    case 'briefcase.fill':
    case 'briefcase':
    case 'business':
      return 'briefcase';
    default:
      return 'help-circle';
  }
};

/**
 * An icon component that uses Ionicons - a clean, versatile icon set
 * that works well across iOS and Android platforms.
 */
export function IconSymbol({
  name,
  size = 24,
  color,
  style,
}: {
  name: string;
  size?: number;
  color: string | OpaqueColorValue;
  style?: StyleProp<TextStyle>;
}) {
  return (
    <Ionicons
      name={mapToIonicons(name) as any}
      size={size}
      color={color}
      style={style}
    />
  );
}
