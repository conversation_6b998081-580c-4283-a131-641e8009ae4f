import { colors } from '@/constants/Colors';
import React from 'react';
import { Image, StyleSheet, View, ViewStyle } from 'react-native';
import { Typography } from './Typography';

interface AvatarProps {
  name?: string;
  photo?: string;
  imageUrl?: string; // Keep for backward compatibility
  size?: number;
  backgroundColor?: string;
  textColor?: string;
  style?: ViewStyle;
  variant?: 'primary' | 'secondary' | 'custom';
}

export function Avatar({
  name,
  photo,
  imageUrl, // Keep for backward compatibility
  size = 44,
  backgroundColor,
  textColor,
  style,
  variant = 'primary',
}: AvatarProps) {
  // Use photo prop first, then fallback to imageUrl for compatibility
  const image = photo || imageUrl;
  
  // Get variant colors
  const getBackgroundColor = () => {
    if (backgroundColor) return backgroundColor;
    switch (variant) {
      case 'primary': return colors.avatarBackground.primary;
      case 'secondary': return colors.primaryVeryLight;
      default: return colors.avatarBackground.primary;
    }
  };

  const getTextColor = () => {
    if (textColor) return textColor;
    return colors.primary;
  };

  // Helper function to get initials from name (max 3 characters)
  const getInitials = (name: string) => {
    if (!name) return '?';
    
    // Split the name into words
    const words = name.trim().split(/\s+/);
    
    // If there are multiple words, take the first character of each word (max 3)
    if (words.length > 1) {
      return words
        .slice(0, 3)
        .map(word => word[0])
        .join('')
        .toUpperCase();
    }
    
    // If it's a single word, take the first 3 characters
    return name.substring(0, 3).toUpperCase();
  };

  // Calculate font size based on avatar size and initials length
  const getFontSize = () => {
    const initials = getInitials(name || '');
    const baseSize = size * 0.35;
    
    // Adjust font size based on number of characters
    if (initials.length === 1) return baseSize * 1.2;
    if (initials.length === 2) return baseSize;
    return baseSize * 0.8; // 3 characters
  };

  return (
    <View
      style={[
        styles.container,
        {
          width: size,
          height: size,
          borderRadius: size / 2,
          backgroundColor: getBackgroundColor(),
        },
        style,
      ]}
    >
      {image ? (
        <Image
          source={{ uri: image }}
          style={{
            width: size,
            height: size,
            borderRadius: size / 2,
          }}
          resizeMode="cover"
        />
      ) : (
        <Typography
          variant="label"
          style={{
            ...styles.text,
            color: getTextColor(),
            fontSize: getFontSize(),
            fontWeight: '600',
          }}
        >
          {getInitials(name || '')}
        </Typography>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  text: {
    textAlign: 'center',
  },
}); 