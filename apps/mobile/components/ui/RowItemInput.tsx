import { colors } from '@/constants/Colors';
import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import { StyleSheet, TouchableOpacity, View, ViewStyle } from 'react-native';
import { Input } from './Input';
import { Typography } from './Typography';

interface RowItemInputProps {
  style?: ViewStyle;
  leftIcon?: keyof typeof Ionicons.glyphMap;
  leftIconColor?: string;
  title?: string;
  value: string;
  placeholder?: string;
  onChangeText: (text: string) => void;
  showDivider?: boolean;
}

export function RowItemInput({
  style,
  leftIcon,
  leftIconColor = colors.primary,
  title,
  value,
  placeholder,
  onChangeText,
  showDivider = true,
}: RowItemInputProps) {
  const [isEditing, setIsEditing] = useState(false);

  const handlePress = () => {
    setIsEditing(true);
  };

  const handleSubmit = () => {
    setIsEditing(false);
  };

  const handleCancel = () => {
    setIsEditing(false);
  };

  if (isEditing) {
    return (
      <View style={[styles.container, style]}>
        <View style={styles.header}>
          <View style={styles.leftContent}>
            {leftIcon && (
              <View style={styles.iconContainer}>
                <Ionicons
                  name={leftIcon}
                  size={20}
                  color={leftIconColor}
                />
              </View>
            )}
            <View style={styles.textContent}>
              {title && (
                <Typography variant="body" color="secondary" style={styles.title}>
                  {title}
                </Typography>
              )}
              <Input
                value={value}
                placeholder={placeholder}
                onChangeText={onChangeText}
                autoFocus={true}
                onBlur={handleSubmit}
                onSubmitEditing={handleSubmit}
                style={styles.input}
              />
            </View>
          </View>
          <TouchableOpacity onPress={handleCancel} style={styles.iconButton}>
            <Ionicons name="checkmark" size={20} color={colors.primary} />
          </TouchableOpacity>
        </View>
        {showDivider && <View style={styles.divider} />}
      </View>
    );
  }

  return (
    <View style={[styles.container, style]}>
      <TouchableOpacity
        style={styles.header}
        onPress={handlePress}
        activeOpacity={0.7}
      >
        <View style={styles.leftContent}>
          {leftIcon && (
            <View style={styles.iconContainer}>
              <Ionicons
                name={leftIcon}
                size={20}
                color={leftIconColor}
              />
            </View>
          )}
          <View style={styles.textContent}>
            {title && (
              <Typography variant="body" color="secondary" style={styles.title}>
                {title}
              </Typography>
            )}
            <Typography variant="body" color="primary" style={styles.subtitle}>
              {value || placeholder}
            </Typography>
          </View>
        </View>
        <Ionicons
          name="create-outline"
          size={20}
          color={colors.primary}
        />
      </TouchableOpacity>
      
      {showDivider && <View style={styles.divider} />}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.cardBackground,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    minHeight: 56,
  },
  leftContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    marginRight: 12,
  },
  textContent: {
    flex: 1,
  },
  title: {
    marginBottom: 2,
  },
  subtitle: {
    fontWeight: '500',
  },
  divider: {
    height: 1,
    backgroundColor: colors.divider,
    marginLeft: 16,
  },
  input: {
    marginBottom: 0,
    marginTop: 0,
    fontSize: 16,
    fontWeight: '500',
    color: colors.text.primary,
    paddingVertical: 0,
    paddingHorizontal: 0,
    borderWidth: 0,
    backgroundColor: 'transparent',
  },
  cancelButton: {
    padding: 4,
    marginLeft: 8,
  },
  iconButton: {
    padding: 4,
  },
}); 