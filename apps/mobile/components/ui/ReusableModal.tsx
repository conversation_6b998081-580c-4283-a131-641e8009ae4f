import React from 'react';
import { Modal, ModalProps, StyleSheet, View, ViewStyle } from 'react-native';
import { KeyboardAwareView } from './KeyboardAwareView';

interface ReusableModalProps extends Omit<ModalProps, 'visible' | 'onRequestClose'> {
  visible: boolean;
  onRequestClose: () => void;
  children: React.ReactNode;
  contentStyle?: ViewStyle;
}

const ReusableModal = ({
  visible,
  onRequestClose,
  children,
  contentStyle,
  ...modalProps
}: ReusableModalProps) => {
  return (
    <Modal
      visible={visible}
      onRequestClose={onRequestClose}
      transparent={true}
      animationType="none"
      presentationStyle='overFullScreen'
      {...modalProps}
    >
      <View style={styles.background}>
        <View style={[styles.content, contentStyle]}>
          <KeyboardAwareView 
            showsVerticalScrollIndicator={false}
            bottomOffset={20}
            extraKeyboardSpace={10}
            contentContainerStyle={styles.scrollContent}
          >
            {children}
          </KeyboardAwareView>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  background: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  content: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 10,
    width: '80%',
    maxHeight: '80%',
  },
  scrollContent: {
    flexGrow: 1,
  },
});

export default ReusableModal;
