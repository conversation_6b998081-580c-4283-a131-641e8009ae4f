import { colors } from '@/constants/Colors';
import React, { ReactNode } from 'react';
import { StyleSheet, View, ViewStyle } from 'react-native';

interface CardProps {
  children: ReactNode;
  style?: ViewStyle;
  noBorder?: boolean;
}

export function Card({ children, style, noBorder = false }: CardProps) {
  return (
    <View style={[
      styles.card,
      noBorder ? null : styles.withBorder,
      style
    ]}>
      {children}
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: colors.cardBackground,
    borderRadius: 16,
    padding: 10,
    marginBottom: 6,
  },
  withBorder: {
    borderWidth: 1,
    borderColor: 'rgba(230, 230, 250, 0.5)',
  },
}); 