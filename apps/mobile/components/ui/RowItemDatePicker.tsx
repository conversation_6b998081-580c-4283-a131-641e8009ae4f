import { colors } from '@/constants/Colors';
import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import { StyleSheet, TouchableOpacity, View, ViewStyle } from 'react-native';
import { DatePickerModal } from './DatePickerModal';
import { Typography } from './Typography';

interface RowItemDatePickerProps {
  style?: ViewStyle;
  leftIcon?: keyof typeof Ionicons.glyphMap;
  leftIconColor?: string;
  title?: string;
  value: Date;
  onChange: (date: Date) => void;
  showDivider?: boolean;
  showQuickDays?: boolean;
}

export function RowItemDatePicker({
  style,
  leftIcon,
  leftIconColor = colors.primary,
  title,
  value,
  onChange,
  showDivider = true,
  showQuickDays = false,
}: RowItemDatePickerProps) {
  const [modalVisible, setModalVisible] = useState(false);

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const handlePress = () => {
    setModalVisible(true);
  };

  const handleConfirm = (date: Date) => {
    onChange(date);
    setModalVisible(false);
  };

  const handleCancel = () => {
    setModalVisible(false);
  };

  return (
    <View style={[styles.container, style]}>
      <TouchableOpacity
        style={styles.header}
        onPress={handlePress}
        activeOpacity={0.7}
      >
        <View style={styles.leftContent}>
          {leftIcon && (
            <View style={styles.iconContainer}>
              <Ionicons
                name={leftIcon}
                size={20}
                color={leftIconColor}
              />
            </View>
          )}
          <View style={styles.textContent}>
            {title && (
              <Typography variant="body" color="secondary" style={styles.title}>
                {title}
              </Typography>
            )}
            <Typography variant="body" color="primary" style={styles.subtitle}>
              {formatDate(value)}
            </Typography>
          </View>
        </View>
        <Ionicons
          name="create-outline"
          size={20}
          color={colors.primary}
        />
      </TouchableOpacity>
      
      {showDivider && <View style={styles.divider} />}
      
      <DatePickerModal
        visible={modalVisible}
        value={value}
        onConfirm={handleConfirm}
        onCancel={handleCancel}
        title={title || "Select Date"}
        showQuickDays={showQuickDays}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.cardBackground,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    minHeight: 56,
  },
  leftContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    marginRight: 12,
  },
  textContent: {
    flex: 1,
  },
  title: {
    marginBottom: 2,
  },
  subtitle: {
    fontWeight: '500',
  },
  divider: {
    height: 1,
    backgroundColor: colors.divider,
    marginLeft: 16,
  },
}); 