import { colors } from '@/constants/Colors';
import React from 'react';
import { ScrollView, StyleSheet, View } from 'react-native';
import { Typography } from './Typography';

export interface TableLineItem {
  id: string;
  description: string;
  price: number;
  quantity: number;
  tax: number;
  total: number;
}

interface ItemsTableProps {
  items: TableLineItem[];
}

export function ItemsTable({ items }: ItemsTableProps) {
  return (
    <View style={styles.container}>
      {/* Table Header */}
      <View style={styles.headerRow}>
        <View style={[styles.cell, styles.numberCell]}>
          <Typography variant="caption" color="secondary" bold>
            No
          </Typography>
        </View>
        <View style={[styles.cell, styles.descriptionCell]}>
          <Typography variant="caption" color="secondary" bold>
            Item Description
          </Typography>
        </View>
        <View style={[styles.cell, styles.priceCell]}>
          <Typography variant="caption" color="secondary" bold>
            Price
          </Typography>
        </View>
        <View style={[styles.cell, styles.quantityCell]}>
          <Typography variant="caption" color="secondary" bold>
            Quantity
          </Typography>
        </View>
        <View style={[styles.cell, styles.taxCell]}>
          <Typography variant="caption" color="secondary" bold>
            Tax
          </Typography>
        </View>
        <View style={[styles.cell, styles.totalCell]}>
          <Typography variant="caption" color="secondary" bold>
            Total
          </Typography>
        </View>
      </View>

      {/* Scrollable Table Body */}
      <ScrollView style={styles.tableBody} showsVerticalScrollIndicator={false}>
        {items.map((item, index) => (
          <View key={item.id} style={styles.dataRow}>
            <View style={[styles.cell, styles.numberCell]}>
              <Typography variant="body" color="primary">
                {index + 1}
              </Typography>
            </View>
            <View style={[styles.cell, styles.descriptionCell]}>
              <Typography variant="body" color="primary" numberOfLines={2}>
                {item.description}
              </Typography>
            </View>
            <View style={[styles.cell, styles.priceCell]}>
              <Typography variant="body" color="primary">
                {item.price}
              </Typography>
            </View>
            <View style={[styles.cell, styles.quantityCell]}>
              <Typography variant="body" color="primary">
                {item.quantity}
              </Typography>
            </View>
            <View style={[styles.cell, styles.taxCell]}>
              <Typography variant="body" color="primary">
                {item.tax}%
              </Typography>
            </View>
            <View style={[styles.cell, styles.totalCell]}>
              <Typography variant="body" color="primary" bold>
                {item.total}
              </Typography>
            </View>
          </View>
        ))}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.cardBackground,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.divider,
    marginTop: 8,
  },
  headerRow: {
    flexDirection: 'row',
    backgroundColor: colors.background,
    paddingVertical: 8,
    paddingHorizontal: 4,
    borderTopLeftRadius: 7,
    borderTopRightRadius: 7,
  },
  dataRow: {
    flexDirection: 'row',
    paddingVertical: 12,
    paddingHorizontal: 4,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: colors.divider,
  },
  cell: {
    paddingHorizontal: 4,
    justifyContent: 'center',
  },
  numberCell: {
    width: 40,
    alignItems: 'center',
  },
  descriptionCell: {
    flex: 1,
    minWidth: 120,
  },
  priceCell: {
    width: 60,
    alignItems: 'center',
  },
  quantityCell: {
    width: 60,
    alignItems: 'center',
  },
  taxCell: {
    width: 50,
    alignItems: 'center',
  },
  totalCell: {
    width: 70,
    alignItems: 'flex-end',
  },
  tableBody: {
    maxHeight: 300,
  },
}); 