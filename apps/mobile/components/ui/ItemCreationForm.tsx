import { colors } from '@/constants/Colors';
import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { Button } from './Button';
import { Input } from './Input';
import { Typography } from './Typography';

interface ItemCreationFormProps {
  onSubmit: (itemData: {
    name: string;
    description: string;
    price: string;
    quantity: string;
    unit: string;
    discount?: string;
    discountType?: 'percentage' | 'fixed';
    tags: string[];
    saveAsService: boolean;
  }) => void;
  onCancel: () => void;
  onDelete?: () => void;
  cancelButtonText?: string;
  initialData?: {
    name: string;
    description: string;
    price: string;
    quantity: string;
    unit?: string;
    discount?: string;
    discountType?: 'percentage' | 'fixed';
    tags?: string[];
  };
  isEditing?: boolean;
  canSaveAsService?: boolean;
}

export function ItemCreationForm({ 
  onSubmit, 
  onCancel, 
  onDelete,
  cancelButtonText = "Cancel",
  initialData,
  isEditing = false,
  canSaveAsService = false
}: ItemCreationFormProps) {
  const [name, setName] = useState(initialData?.name || '');
  const [description, setDescription] = useState(initialData?.description || '');
  const [price, setPrice] = useState(initialData?.price || '');
  const [quantity, setQuantity] = useState(initialData?.quantity || '1');
  const [selectedUnit, setSelectedUnit] = useState(initialData?.unit || 'fixed');
  const [isCustomUnit, setIsCustomUnit] = useState(() => {
    const standardUnits = ['fixed', 'hour', 'day', 'week', 'month', 'project'];
    return initialData?.unit ? !standardUnits.includes(initialData.unit) : false;
  });
  const [saveAsService, setSaveAsService] = useState(false);
  const [discount, setDiscount] = useState(initialData?.discount || '');
  const [discountType, setDiscountType] = useState(initialData?.discountType || 'percentage');

  const handleUnitSelection = (unit: string) => {
    if (unit === 'custom') {
      setIsCustomUnit(true);
      setSelectedUnit('');
    } else {
      setIsCustomUnit(false);
      setSelectedUnit(unit);
    }
  };

  const handleSubmit = () => {
    if (name.trim() && price.trim() && quantity.trim()) {
      onSubmit({
        name: name.trim(),
        description: description.trim(),
        price: price.trim(),
        quantity: quantity.trim(),
        unit: selectedUnit,
        discount: discount.trim(),
        discountType: discountType,
        tags: [],
        saveAsService: saveAsService && !isEditing, // Only allow saving as service when creating new
      });
    }
  };
  const isValid = name.trim() !== '' && price.trim() !== '' && quantity.trim() !== '';

  return (
    <View style={styles.container}>
      <Input
        label="Item Name"
        placeholder="e.g., Website Setup, Logo Design"
        value={name}
        onChangeText={setName}
        containerStyle={styles.input}
      />
      
      <Input
        label="Description"
        placeholder="Enter a description for the item"
        value={description}
        onChangeText={setDescription}
        containerStyle={styles.input}
      />
      
      {/* Price and Quantity Row */}
      <View style={styles.priceQuantityContainer}>
        <View style={styles.priceContainer}>
          <Input
            label="Price"
            placeholder="0.00"
            value={price}
            onChangeText={setPrice}
            keyboardType="decimal-pad"
          />
        </View>
        
        <View style={styles.quantityContainer}>
          <Input
            label="Quantity"
            placeholder="1"
            value={quantity}
            onChangeText={setQuantity}
            keyboardType="numeric"
          />
        </View>
      </View>

      {/* Discount Section */}
      <View style={styles.discountSection}>
        <Typography variant="label" color="primary" style={styles.discountLabel}>
          Discount (Optional)
        </Typography>
        <View style={styles.discountRow}>
          <Input
            label={discountType === 'percentage' ? 'Discount %' : 'Discount Amount'}
            placeholder={discountType === 'percentage' ? '10' : '50.00'}
            value={discount}
            onChangeText={setDiscount}
            keyboardType="decimal-pad"
            containerStyle={styles.discountInput}
          />
          
          <View style={styles.discountTypeContainer}>
            <Typography variant="label" color="primary" style={styles.discountTypeLabel}>
              Type
            </Typography>
            <View style={styles.discountTypeOptions}>
              <TouchableOpacity
                style={[
                  styles.discountTypeOption,
                  discountType === 'percentage' && styles.discountTypeOptionSelected
                ]}
                onPress={() => setDiscountType('percentage')}
              >
                <Typography
                  variant="bodySmall"
                  color={discountType === 'percentage' ? 'primary' : 'secondary'}
                  bold={discountType === 'percentage'}
                >
                  %
                </Typography>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.discountTypeOption,
                  discountType === 'fixed' && styles.discountTypeOptionSelected
                ]}
                onPress={() => setDiscountType('fixed')}
              >
                <Typography
                  variant="bodySmall"
                  color={discountType === 'fixed' ? 'primary' : 'secondary'}
                  bold={discountType === 'fixed'}
                >
                  Fixed
                </Typography>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>
      
      {/* Unit Selection */}
      <View style={styles.unitContainer}>
        <Typography variant="label" color="primary" style={styles.unitLabel}>
          Unit
        </Typography>
        <View style={styles.unitOptions}>
          {['fixed', 'hour', 'day', 'week', 'month', 'project', 'custom'].map((unit) => (
            <TouchableOpacity
              key={unit}
              style={[
                styles.unitOption,
                (unit === 'custom' ? isCustomUnit : selectedUnit === unit) && styles.unitOptionSelected
              ]}
              onPress={() => handleUnitSelection(unit)}
            >
              <Typography
                variant="bodySmall"
                color={(unit === 'custom' ? isCustomUnit : selectedUnit === unit) ? 'primary' : 'secondary'}
                bold={(unit === 'custom' ? isCustomUnit : selectedUnit === unit)}
              >
                {unit.charAt(0).toUpperCase() + unit.slice(1)}
              </Typography>
            </TouchableOpacity>
          ))}
        </View>

        {/* Custom Unit Input */}
        {isCustomUnit && (
          <Input
            label="Custom Unit"
            placeholder="e.g., project, week, page"
            value={selectedUnit}
            onChangeText={setSelectedUnit}
            containerStyle={styles.customUnitInput}
          />
        )}
      </View>

      {/* Save as Service Option (only when creating new item) */}
      {canSaveAsService && (
        <TouchableOpacity 
          style={styles.saveServiceOption}
          onPress={() => setSaveAsService(!saveAsService)}
        >
          <View style={styles.saveServiceContent}>
            <View style={styles.saveServiceText}>
              <Typography variant="body" bold>
                Save Item as Service
              </Typography>
              <Typography variant="bodySmall" color="secondary">
                Add this item to your services library for future use
              </Typography>
            </View>
            <View style={[styles.checkbox, saveAsService && styles.checkboxChecked]}>
              {saveAsService && (
                <Ionicons name="checkmark" size={16} color={colors.primary} />
              )}
            </View>
          </View>
        </TouchableOpacity>
      )}
      
      <View style={styles.buttons}>
        {isEditing ? (
          <TouchableOpacity 
            style={styles.deleteBtn}
            onPress={onDelete}
          >
            <View style={styles.deleteButtonContent}>
              <Ionicons name="trash-outline" size={16} color={colors.error} />
              <Typography variant="body" style={styles.deleteButtonText}>
                Delete
              </Typography>
            </View>
          </TouchableOpacity>
        ) : (
          <Button
            title={cancelButtonText}
            variant="outline"
            onPress={onCancel}
            style={styles.cancelBtn}
          />
        )}
        <Button
          title={isEditing ? "Update Item" : "Create Item"}
          onPress={handleSubmit}
          disabled={!isValid}
          style={styles.submitBtn}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 6,
    paddingBottom: 24,
  },
  input: {
    marginBottom: 12,
  },
  priceQuantityContainer: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 12,
  },
  priceContainer: {
    flex: 1,
  },
  quantityContainer: {
    flex: 1,
  },
  discountSection: {
    marginBottom: 12,
  },
  discountLabel: {
    marginBottom: 8,
  },
  discountRow: {
    flexDirection: 'row',
    gap: 12,
  },
  discountInput: {
    flex: 1,
  },
  discountTypeContainer: {
    flex: 1,
  },
  discountTypeLabel: {
    marginBottom: 8,
  },
  discountTypeOptions: {
    flexDirection: 'row',
    gap: 4,
  },
  discountTypeOption: {
    flex: 1,
    paddingHorizontal: 8,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: colors.divider,
    borderRadius: 20,
    backgroundColor: colors.cardBackground,
    alignItems: 'center',
  },
  discountTypeOptionSelected: {
    backgroundColor: 'rgba(52, 144, 243, 0.1)',
    borderColor: colors.primary,
  },
  unitContainer: {
    marginBottom: 12,
  },
  unitLabel: {
    marginBottom: 8,
  },
  unitOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  unitOption: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: colors.divider,
    borderRadius: 20,
    backgroundColor: colors.cardBackground,
  },
  unitOptionSelected: {
    backgroundColor: 'rgba(52, 144, 243, 0.1)',
    borderColor: colors.primary,
  },
  customUnitInput: {
    marginTop: 8,
  },
  saveServiceOption: {
    marginBottom: 10,
    padding: 12,
    borderWidth: 1,
    borderColor: colors.divider,
    borderRadius: 12,
    backgroundColor: colors.cardBackground,
  },
  saveServiceContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  saveServiceText: {
    flex: 1,
    marginRight: 12,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderWidth: 2,
    borderColor: colors.divider,
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkboxChecked: {
    borderColor: colors.primary,
    backgroundColor: 'rgba(52, 144, 243, 0.1)',
  },
  buttons: {
    flexDirection: 'row',
    gap: 12
  },
  cancelBtn: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 12,
  },
  submitBtn: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 12,
  },
  deleteBtn: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.error,
    backgroundColor: 'rgba(255, 99, 99, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  deleteButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  deleteButtonText: {
    marginLeft: 8,
    color: colors.error,
  },
}); 