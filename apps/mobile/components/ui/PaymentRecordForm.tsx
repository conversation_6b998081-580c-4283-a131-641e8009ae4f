import { colors } from '@/constants/Colors';
import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import {
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import { PaymentMethod } from '../../defs/payment';
import { Dropdown } from './Dropdown';
import { Typography } from './Typography';

export interface PaymentRecordData {
  invoiceId: string;
  amount: number;
  method: PaymentMethod;
  paymentDate: Date;
  reference: string;
  notes: string;
  type: 'payment' | 'refund';
}

interface Invoice {
  id: string;
  invoiceNumber: string;
  clientName: string;
  amount: string;
  status: string;
}

interface PaymentRecordFormProps {
  onSubmit: (data: PaymentRecordData) => void;
  invoiceTotal: number;
  outstandingBalance: number;
  selectedInvoice?: Invoice;
  availableInvoices?: Invoice[];
  disableInvoiceSelection?: boolean;
  paymentStatus?: 'unpaid' | 'partially_paid' | 'paid' | 'overpaid' | 'refunded';
  overpaidAmount?: number;
}

const PAYMENT_METHODS = [
  { key: 'bank_transfer', title: 'Bank Transfer', icon: 'business-outline' },
  { key: 'credit_card', title: 'Credit Card', icon: 'card-outline' },
  { key: 'cash', title: 'Cash', icon: 'cash-outline' },
  { key: 'check', title: 'Check', icon: 'receipt-outline' },
  { key: 'paypal', title: 'PayPal', icon: 'logo-paypal' },
  { key: 'other', title: 'Other', icon: 'ellipsis-horizontal-outline' },
] as const;

const QUICK_AMOUNTS = [25, 50, 75, 100];

export function PaymentRecordForm({ 
  onSubmit, 
  invoiceTotal,
  outstandingBalance,
  selectedInvoice,
  availableInvoices = [],
  disableInvoiceSelection = false,
  paymentStatus = 'unpaid',
  overpaidAmount = 0
}: PaymentRecordFormProps) {
  const [selectedInvoiceId, setSelectedInvoiceId] = useState(selectedInvoice?.id || '');
  const [amount, setAmount] = useState('');
  const [selectedMethod, setSelectedMethod] = useState<PaymentMethod>('bank_transfer');
  const [reference, setReference] = useState('');
  const [notes, setNotes] = useState('');

  const handleSubmit = () => {
    const enteredAmount = parseFloat(amount) || 0;
    const isRefund = enteredAmount < 0;
    
    const paymentData: PaymentRecordData = {
      invoiceId: selectedInvoiceId,
      amount: Math.abs(enteredAmount), // Store as positive value
      method: selectedMethod,
      paymentDate: new Date(),
      reference,
      notes,
      type: isRefund ? 'refund' : 'payment',
    };
    
    onSubmit(paymentData);
    
    // Reset form
    if (!disableInvoiceSelection) {
      setSelectedInvoiceId('');
    }
    setAmount('');
    setSelectedMethod('bank_transfer');
    setReference('');
    setNotes('');
  };

  const setQuickAmount = (percentage: number) => {
    if (paymentStatus === 'overpaid') {
      // For overpaid invoices, calculate negative refund amounts
      const refundAmount = (overpaidAmount * percentage) / 100;
      setAmount((-refundAmount).toFixed(2));
    } else {
      // For other statuses, calculate positive payment amounts
      const quickAmount = (outstandingBalance * percentage) / 100;
      setAmount(quickAmount.toFixed(2));
    }
  };

  // Validation logic for payment amounts
  const isAmountValid = () => {
    const amountValue = parseFloat(amount);
    
    // Allow positive amounts for all statuses
    if (amountValue > 0) return true;
    
    // For overpaid invoices, allow negative amounts (refunds) up to the overpaid amount
    if (paymentStatus === 'overpaid' && amountValue < 0) {
      return Math.abs(amountValue) <= overpaidAmount;
    }
    
    return false;
  };

  const isValid = isAmountValid() && selectedMethod && selectedInvoiceId;

  // Convert invoices to dropdown options
  const invoiceOptions = availableInvoices.map(invoice => ({
    key: invoice.id,
    title: invoice.invoiceNumber,
    description: `${invoice.clientName} • ${invoice.amount}`,
    icon: 'document-text-outline' as keyof typeof Ionicons.glyphMap,
  }));

  // When disabled, add the selected invoice to options if not already there
  const dropdownOptions = disableInvoiceSelection && selectedInvoice 
    ? [
        {
          key: selectedInvoice.id,
          title: selectedInvoice.invoiceNumber,
          description: `${selectedInvoice.clientName} • ${selectedInvoice.amount}`,
          icon: 'document-text-outline' as keyof typeof Ionicons.glyphMap,
        }
      ]
    : invoiceOptions;

  return (
    <View style={styles.container}>
      {/* Invoice Selection */}
      <View style={styles.section}>
        <Typography variant="body" bold style={styles.sectionTitle}>
          Invoice
        </Typography>
        <Dropdown
          options={dropdownOptions}
          value={selectedInvoiceId}
          onSelect={setSelectedInvoiceId}
          placeholder="Select an invoice..."
          disabled={disableInvoiceSelection}
          variant="default"
          showDescriptions={true}
        />
      </View>

      {/* Amount Section */}
      <View style={styles.section}>
        <Typography variant="body" bold style={styles.sectionTitle}>
          Payment Amount
        </Typography>
        <TextInput
          style={styles.amountInput}
          value={amount}
          onChangeText={setAmount}
          placeholder={paymentStatus === 'overpaid' ? '-0.00 (for refund)' : '0.00'}
          placeholderTextColor={colors.text.secondary}
          keyboardType="decimal-pad"
        />
        
        {/* Quick Amount Buttons */}
        <View style={styles.quickAmounts}>
          {QUICK_AMOUNTS.map((percentage) => (
            <TouchableOpacity
              key={percentage}
              style={styles.quickAmountButton}
              onPress={() => setQuickAmount(percentage)}
            >
              <Typography variant="bodySmall" color="primary" bold>
                {percentage}%
              </Typography>
            </TouchableOpacity>
          ))}
        </View>

        <Typography variant="caption" color="secondary" style={styles.balanceText}>
          {paymentStatus === 'overpaid' 
            ? `Overpaid by: $${overpaidAmount.toFixed(2)} (negative amounts for refunds)`
            : `Outstanding balance: $${outstandingBalance.toFixed(2)}`
          }
        </Typography>
      </View>

      {/* Payment Method */}
      <View style={styles.section}>
        <Typography variant="body" bold style={styles.sectionTitle}>
          Payment Method
        </Typography>
        <View style={styles.methodGrid}>
          {PAYMENT_METHODS.map((method) => (
            <TouchableOpacity
              key={method.key}
              style={[
                styles.methodOption,
                selectedMethod === method.key && styles.methodOptionSelected
              ]}
              onPress={() => setSelectedMethod(method.key as PaymentMethod)}
            >
              <Ionicons 
                name={method.icon} 
                size={20} 
                color={selectedMethod === method.key ? colors.primary : colors.text.secondary}
              />
              <Typography 
                variant="bodySmall" 
                color={selectedMethod === method.key ? 'primary' : 'secondary'}
                style={styles.methodText}
              >
                {method.title}
              </Typography>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Reference */}
      <View style={styles.section}>
        <Typography variant="body" bold style={styles.sectionTitle}>
          Reference (Optional)
        </Typography>
        <TextInput
          style={styles.textInput}
          value={reference}
          onChangeText={setReference}
          placeholder="Transaction ID, check number, etc."
          placeholderTextColor={colors.text.secondary}
        />
      </View>

      {/* Notes */}
      <View style={styles.section}>
        <Typography variant="body" bold style={styles.sectionTitle}>
          Notes (Optional)
        </Typography>
        <TextInput
          style={[styles.textInput, styles.notesInput]}
          value={notes}
          onChangeText={setNotes}
          placeholder="Add any additional notes..."
          placeholderTextColor={colors.text.secondary}
          multiline
          numberOfLines={3}
          textAlignVertical="top"
        />
      </View>

      {/* Submit Button */}
      <TouchableOpacity
        style={[styles.submitButton, !isValid && styles.submitButtonDisabled]}
        onPress={handleSubmit}
        disabled={!isValid}
      >
        <Typography variant="body" bold color="white">
          {parseFloat(amount) < 0 ? 'Record Refund' : 'Record Payment'}
        </Typography>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    marginBottom: 12,
  },
  amountInput: {
    fontSize: 32,
    fontWeight: '700',
    color: colors.text.primary,
    textAlign: 'center',
    paddingVertical: 20,
    borderBottomWidth: 2,
    borderBottomColor: colors.primary,
    marginBottom: 16,
  },
  quickAmounts: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  quickAmountButton: {
    flex: 1,
    marginHorizontal: 4,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: colors.primary,
    borderRadius: 8,
    alignItems: 'center',
  },
  balanceText: {
    textAlign: 'center',
    marginTop: 8,
  },
  methodGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  methodOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderColor: colors.divider,
    borderRadius: 12,
    backgroundColor: colors.cardBackground,
    minWidth: '45%',
  },
  methodOptionSelected: {
    borderColor: colors.primary,
    backgroundColor: colors.primaryVeryLight,
  },
  methodText: {
    marginLeft: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: colors.divider,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: colors.text.primary,
    backgroundColor: colors.cardBackground,
  },
  notesInput: {
    minHeight: 80,
  },
  submitButton: {
    backgroundColor: colors.primary,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 20,
  },
  submitButtonDisabled: {
    backgroundColor: colors.disabled,
    marginBottom: 20,
  },
}); 