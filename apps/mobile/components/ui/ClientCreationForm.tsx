import { colors } from '@/constants/Colors';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import React, { useState } from 'react';
import { Image, StyleSheet, TouchableOpacity, View } from 'react-native';
import { Button } from './Button';
import { ConfirmationDialog } from './ConfirmationDialog';
import { Input } from './Input';
import { PhotoSelectionDialog } from './PhotoSelectionDialog';
import { Typography } from './Typography';

interface ClientCreationFormProps {
  onSubmit: (clientData: {
    name: string;
    displayName?: string;
    company?: string;
    email: string;
    phone?: string;
    address?: string;
    photo?: string;
    notes?: string;
  }) => void;
  onCancel: () => void;
  submitButtonText?: string;
  cancelButtonText?: string;
  initialData?: {
    name?: string;
    displayName?: string;
    company?: string;
    email?: string;
    phone?: string;
    address?: string;
    photo?: string;
    notes?: string;
  };
}

export function ClientCreationForm({
  onSubmit,
  onCancel,
  submitButtonText = 'Create Client',
  cancelButtonText = 'Cancel',
  initialData
}: ClientCreationFormProps) {
  const [clientName, setClientName] = useState(initialData?.name || '');
  const [displayName, setDisplayName] = useState(initialData?.displayName || '');
  const [company, setCompany] = useState(initialData?.company || '');
  const [email, setEmail] = useState(initialData?.email || '');
  const [phone, setPhone] = useState(initialData?.phone || '');
  const [address, setAddress] = useState(initialData?.address || '');
  const [photo, setPhoto] = useState<string | undefined>(initialData?.photo);
  const [notes, setNotes] = useState(initialData?.notes || '');
  const [displayNameTouched, setDisplayNameTouched] = useState(!!initialData?.displayName);

  // Dialog states
  const [showPhotoSelection, setShowPhotoSelection] = useState(false);
  const [showPermissionDialog, setShowPermissionDialog] = useState(false);
  const [permissionDialogConfig, setPermissionDialogConfig] = useState({
    title: '',
    message: '',
    onConfirm: () => {},
  });

  // Auto-populate display name with client name
  React.useEffect(() => {
    if (!displayNameTouched) {
      setDisplayName(clientName);
    }
  }, [clientName, displayNameTouched]);

  const handleDisplayNameChange = (text: string) => {
    setDisplayName(text);
    setDisplayNameTouched(true);
  };

  const handleDisplayNameBlur = () => {
    setDisplayNameTouched(true);
  };

  // Check if display name exceeds limit
  const isDisplayNameTooLong = displayName.length > 30;

  const handlePhotoUpload = async () => {
    try {
      // Show photo selection dialog
      setShowPhotoSelection(true);
    } catch (error) {
      console.error('Error in photo selection:', error);
      setPermissionDialogConfig({
        title: 'Error',
        message: 'Failed to open photo options. Please try again.',
        onConfirm: () => setShowPermissionDialog(false),
      });
      setShowPermissionDialog(true);
    }
  };

  const handleCameraPhoto = async () => {
    setShowPhotoSelection(false);
    
    try {
      // Request camera permissions
      const cameraPermission = await ImagePicker.requestCameraPermissionsAsync();
      
      if (cameraPermission.granted === false) {
        setPermissionDialogConfig({
          title: 'Camera Permission Required',
          message: 'Permission to access camera is required to take a photo. Please enable camera access in your device settings.',
          onConfirm: () => {
            setShowPermissionDialog(false);
            ImagePicker.requestCameraPermissionsAsync();
          },
        });
        setShowPermissionDialog(true);
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
        base64: true,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        if (asset.base64) {
          setPhoto(`data:image/jpeg;base64,${asset.base64}`);
        } else if (asset.uri) {
          setPhoto(asset.uri);
        }
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      setPermissionDialogConfig({
        title: 'Error',
        message: 'Failed to take photo. Please try again.',
        onConfirm: () => {
          setShowPermissionDialog(false);
          handleCameraPhoto();
        },
      });
      setShowPermissionDialog(true);
    }
  };

  const handleLibraryPhoto = async () => {
    setShowPhotoSelection(false);
    
    try {
      // Request media library permissions
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      if (permissionResult.granted === false) {
        setPermissionDialogConfig({
          title: 'Photo Library Permission Required',
          message: 'Permission to access photo library is required to select a photo. Please enable photo access in your device settings.',
          onConfirm: () => {
            setShowPermissionDialog(false);
            ImagePicker.requestMediaLibraryPermissionsAsync();
          },
        });
        setShowPermissionDialog(true);
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
        base64: true,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        if (asset.base64) {
          setPhoto(`data:image/jpeg;base64,${asset.base64}`);
        } else if (asset.uri) {
          setPhoto(asset.uri);
        }
      }
    } catch (error) {
      console.error('Error picking image:', error);
      setPermissionDialogConfig({
        title: 'Error',
        message: 'Failed to select image. Please try again.',
        onConfirm: () => {
          setShowPermissionDialog(false);
          handleLibraryPhoto();
        },
      });
      setShowPermissionDialog(true);
    }
  };

  const handleRemovePhoto = () => {
    setPhoto(undefined);
  };

  const handleSubmit = () => {
    if (clientName.trim() && email.trim() && !isDisplayNameTooLong) {
      onSubmit({
        name: clientName.trim(),
        displayName: displayName.trim() || undefined,
        company: company.trim() || undefined,
        email: email.trim(),
        phone: phone.trim() || undefined,
        address: address.trim() || undefined,
        photo: photo,
        notes: notes.trim() || undefined,
      });
      
      // Reset form
      setClientName('');
      setDisplayName('');
      setCompany('');
      setEmail('');
      setPhone('');
      setAddress('');
      setPhoto(undefined);
      setNotes('');
      setDisplayNameTouched(false);
    }
  };

  const isFormValid = clientName.trim() !== '' && email.trim() !== '' && !isDisplayNameTooLong;

  // Generate initials for photo placeholder
  const getInitials = () => {
    if (clientName.trim()) {
      return clientName.trim().split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
    }
    return 'CL';
  };

  return (
    <View style={styles.container}>
      {/* Photo Section */}
      <View style={styles.photoSection}>
        <Typography variant="bodySmall" color="secondary" style={styles.photoLabel}>
          Client Photo (Optional)
        </Typography>
        <View style={styles.photoContainer}>
          {photo ? (
            <View style={styles.photoWrapper}>
              <Image source={{ uri: photo }} style={styles.photoImage} />
              <TouchableOpacity style={styles.removePhotoButton} onPress={handleRemovePhoto}>
                <Ionicons name="close" size={16} color={colors.background} />
              </TouchableOpacity>
            </View>
          ) : (
            <TouchableOpacity style={styles.photoPlaceholder} onPress={handlePhotoUpload}>
              <View style={styles.photoInitials}>
                <Typography variant="body" color="primary" bold>
                  {getInitials()}
                </Typography>
              </View>
              <Ionicons name="camera" size={20} color={colors.primary} style={styles.cameraIcon} />
            </TouchableOpacity>
          )}
          {photo && (
            <TouchableOpacity style={styles.changePhotoButton} onPress={handlePhotoUpload}>
              <Typography variant="bodySmall" color="primary">
                Change Photo
              </Typography>
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Basic Information */}
      <Typography variant="bodySmall" color="secondary" style={styles.sectionLabel}>
        Basic Information
      </Typography>
      
      <Input
        label="Client Name *"
        placeholder="e.g., John Doe, Acme Inc."
        value={clientName}
        onChangeText={setClientName}
        containerStyle={styles.input}
      />
      
      <Input
        label="Display Name"
        placeholder="Short name for lists (max 30 chars)"
        value={displayName}
        onChangeText={handleDisplayNameChange}
        onBlur={handleDisplayNameBlur}
        maxLength={50}
        containerStyle={styles.input}
        error={isDisplayNameTooLong ? `Too long (${displayName.length}/30 characters)` : undefined}
      />
      
      <Input
        label="Company/Organization"
        placeholder="e.g., Acme Corporation"
        value={company}
        onChangeText={setCompany}
        containerStyle={styles.input}
      />

      {/* Contact Information */}
      <Typography variant="bodySmall" color="secondary" style={styles.sectionLabel}>
        Contact Information
      </Typography>
      
      <Input
        label="Email Address *"
        placeholder="<EMAIL>"
        value={email}
        onChangeText={setEmail}
        keyboardType="email-address"
        autoCapitalize="none"
        containerStyle={styles.input}
      />
      
      <Input
        label="Phone Number"
        placeholder="(*************"
        value={phone}
        onChangeText={setPhone}
        keyboardType="phone-pad"
        containerStyle={styles.input}
      />
      
      <Input
        label="Business Address"
        placeholder="123 Main St, City, State, ZIP"
        value={address}
        onChangeText={setAddress}
        multiline={true}
        numberOfLines={2}
        containerStyle={styles.input}
      />

      {/* Additional Information */}
      <Typography variant="bodySmall" color="secondary" style={styles.sectionLabel}>
        Additional Information
      </Typography>
      
      <Input
        label="Notes"
        placeholder="Any additional notes about the client..."
        value={notes}
        onChangeText={setNotes}
        multiline={true}
        numberOfLines={3}
        containerStyle={styles.input}
      />
      
      <View style={styles.buttons}>
        <Button
          title={cancelButtonText}
          variant="outline"
          onPress={onCancel}
          style={styles.cancelBtn}
        />
        <Button
          title={submitButtonText}
          onPress={handleSubmit}
          disabled={!isFormValid}
          style={{
            ...styles.submitBtn,
            backgroundColor: !isFormValid ? colors.disabled : colors.primary
          }}
        />
      </View>

      {/* Photo Selection Dialog */}
      <PhotoSelectionDialog
        visible={showPhotoSelection}
        onCamera={handleCameraPhoto}
        onLibrary={handleLibraryPhoto}
        onCancel={() => setShowPhotoSelection(false)}
      />

      {/* Permission Dialog */}
      <ConfirmationDialog
        visible={showPermissionDialog}
        title={permissionDialogConfig.title}
        message={permissionDialogConfig.message}
        confirmText="OK"
        cancelText="Cancel"
        onConfirm={permissionDialogConfig.onConfirm}
        onCancel={() => setShowPermissionDialog(false)}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  
  // Photo Section
  photoSection: {
    marginBottom: 24,
  },
  photoLabel: {
    fontSize: 12,
    marginBottom: 12,
    textTransform: 'uppercase',
    fontWeight: '500',
    letterSpacing: 0.5,
  },
  photoContainer: {
    alignItems: 'center',
  },
  photoWrapper: {
    position: 'relative',
  },
  photoImage: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.primaryVeryLight,
  },
  removePhotoButton: {
    position: 'absolute',
    top: -4,
    right: -4,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: colors.error,
    alignItems: 'center',
    justifyContent: 'center',
  },
  photoPlaceholder: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.primaryVeryLight,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    borderWidth: 2,
    borderColor: colors.primary,
    borderStyle: 'dashed',
  },
  photoInitials: {
    position: 'absolute',
    top: 8,
    left: 8,
    right: 8,
    bottom: 8,
    borderRadius: 32,
    backgroundColor: colors.avatarBackground.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cameraIcon: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    backgroundColor: colors.background,
    borderRadius: 10,
    padding: 2,
  },
  changePhotoButton: {
    marginTop: 8,
    padding: 4,
  },
  
  // Section Labels
  sectionLabel: {
    fontSize: 12,
    marginBottom: 12,
    marginTop: 16,
    textTransform: 'uppercase',
    fontWeight: '500',
    letterSpacing: 0.5,
  },
  
  input: {
    marginBottom: 12,
  },
  buttons: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 24,
  },
  cancelBtn: {
    flex: 1,
    paddingVertical: 8,
    borderRadius: 12,
  },
  submitBtn: {
    flex: 1,
    paddingVertical: 8,
    borderRadius: 12,
  },
}); 