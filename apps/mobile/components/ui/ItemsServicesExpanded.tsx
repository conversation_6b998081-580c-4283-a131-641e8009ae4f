import { colors } from '@/constants/Colors';
import { formatCurrency, useInvoiceStore } from '@/stores';
import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { InvoiceLineItem } from './InvoiceLineItem';
import { Typography } from './Typography';

interface ItemsServicesExpandedProps {
    onAddLineItem: () => void;
    onOpenServicesSheet: () => void;
    onEditLineItem: (id: string) => void;
}

export function ItemsServicesExpanded({ 
    onAddLineItem, 
    onOpenServicesSheet, 
    onEditLineItem 
}: ItemsServicesExpandedProps) {
    const invoiceStore = useInvoiceStore();
    
    // Calculate total discounts
    const activeItems = invoiceStore.lineItems.filter(item => item.description.trim() !== '');
    const totalDiscounts = activeItems.reduce((sum, item) => {
        const discountValue = parseFloat(item.discount || '0') || 0;
        if (discountValue > 0) {
            const quantity = parseFloat(item.quantity) || 0;
            const price = parseFloat(item.price) || 0;
            const subtotal = quantity * price;
            
            if (item.discountType === 'percentage') {
                return sum + (subtotal * (discountValue / 100));
            } else {
                return sum + discountValue;
            }
        }
        return sum;
    }, 0);
    
    return (
        <View style={styles.container}>
            {/* Header with add buttons */}
            <View style={styles.header}>
                <Typography variant="label" color="secondary">
                    {activeItems.length} {activeItems.length === 1 ? 'item' : 'items'}
                </Typography>
                <View style={styles.addButtonsRow}>
                    <TouchableOpacity 
                        style={styles.compactAddButton}
                        onPress={onAddLineItem}
                    >
                        <Ionicons name="add" size={16} color={colors.primary} />
                        <Typography variant="caption" color="primary" style={{ marginLeft: 4 }}>
                            New Item
                        </Typography>
                    </TouchableOpacity>
                    <TouchableOpacity 
                        style={styles.compactAddButton}
                        onPress={onOpenServicesSheet}
                    >
                        <Ionicons name="library" size={16} color={colors.primary} />
                        <Typography variant="caption" color="primary" style={{ marginLeft: 4 }}>
                            Add Saved Service
                        </Typography>
                    </TouchableOpacity>
                </View>
            </View>

            {/* Line Items */}
            <View style={styles.itemsList}>
                {activeItems.map((item, index) => (
                    <InvoiceLineItem
                        key={item.id}
                        item={item}
                        onEdit={onEditLineItem}
                        itemNumber={index + 1}
                    />
                ))}
                
                {/* Empty state when no items */}
                {activeItems.length === 0 && (
                    <View style={styles.emptyItemsContainer}>
                        <Typography variant="body" color="secondary" center>
                            No items added yet
                        </Typography>
                        <Typography variant="bodySmall" color="secondary" center style={{ marginTop: 4 }}>
                            Tap the + New Item button to add your first item
                        </Typography>
                    </View>
                )}
            </View>

            {/* Integrated Total */}
            <View style={styles.integratedTotal}>
                {totalDiscounts > 0 && (
                    <View style={styles.totalRow}>
                        <Typography variant="bodySmall" color="secondary">Total Discounts</Typography>
                        <Typography variant="bodySmall" color="success">-{formatCurrency(totalDiscounts)}</Typography>
                    </View>
                )}
                <View style={styles.totalRow}>
                    <Typography variant="body" color="secondary">Subtotal</Typography>
                    <Typography variant="body">{formatCurrency(invoiceStore.getSubtotal())}</Typography>
                </View>
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        marginTop: 8,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingBottom: 8,
    },
    addButtonsRow: {
        flexDirection: 'row',
        gap: 8,
    },
    compactAddButton: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 12,
        paddingVertical: 8,
        borderWidth: 1,
        borderColor: colors.primary,
        borderRadius: 8,
        backgroundColor: 'rgba(52, 144, 243, 0.1)',
    },
    itemsList: {
        marginTop: 8,
    },
    emptyItemsContainer: {
        padding: 24,
        alignItems: 'center',
    },
    integratedTotal: {
        marginTop: 8,
        paddingTop: 8,
        borderTopWidth: 1,
        borderTopColor: colors.divider,
    },
    totalRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingVertical: 3,
        paddingHorizontal: 2,
    },
}); 