import React from 'react';
import { ScrollViewProps, StyleSheet, ViewStyle } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-controller';

interface KeyboardAwareViewProps extends Omit<ScrollViewProps, 'keyboardShouldPersistTaps'> {
  children: React.ReactNode;
  style?: ViewStyle;
  contentContainerStyle?: ViewStyle;
  bottomOffset?: number;
  enabled?: boolean;
  extraKeyboardSpace?: number;
  showsVerticalScrollIndicator?: boolean;
  disableScrollOnKeyboardHide?: boolean;
}

/**
 * A wrapper around KeyboardAwareScrollView that provides consistent keyboard handling
 * across iOS and Android with smooth animations and proper keyboard avoidance.
 * 
 * Uses react-native-keyboard-controller for better performance and consistency.
 */
export function KeyboardAwareView({
  children,
  style,
  contentContainerStyle,
  bottomOffset = 20,
  enabled = true,
  extraKeyboardSpace = 0,
  showsVerticalScrollIndicator = false,
  disableScrollOnKeyboardHide = false,
  ...scrollViewProps
}: KeyboardAwareViewProps) {
  return (
    <KeyboardAwareScrollView
      style={[styles.container, style]}
      contentContainerStyle={[styles.contentContainer, contentContainerStyle]}
      keyboardShouldPersistTaps="handled"
      showsVerticalScrollIndicator={showsVerticalScrollIndicator}
      bottomOffset={bottomOffset}
      enabled={enabled}
      extraKeyboardSpace={extraKeyboardSpace}
      disableScrollOnKeyboardHide={disableScrollOnKeyboardHide}
      {...scrollViewProps}
    >
      {children}
    </KeyboardAwareScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    flexGrow: 1,
  },
}); 