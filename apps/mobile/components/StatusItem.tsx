import React from 'react';
import { StyleSheet, View, ViewStyle } from 'react-native';
import { ThemedText } from './ThemedText';
import { ThemedView } from './ThemedView';
import { IconSymbol } from './ui/IconSymbol';

type StatusItemProps = {
  title: string;
  count: number;
  amount: string;
  iconName: any; // We'll need to add more icons to the IconSymbol component
  iconColor: string;
  iconBackgroundColor: string;
  pillColor?: string;
  style?: ViewStyle;
};

export function StatusItem({ 
  title, 
  count, 
  amount, 
  iconName, 
  iconColor, 
  iconBackgroundColor,
  pillColor = '#F3F6FF',
  style 
}: StatusItemProps) {
  return (
    <ThemedView style={[styles.container, { backgroundColor: pillColor }, style]}>
      <View style={styles.leftContent}>
        <View style={[styles.iconContainer, { backgroundColor: iconBackgroundColor }]}>
          <IconSymbol name={iconName} size={20} color={iconColor} />
        </View>
        <View style={styles.textContainer}>
          <ThemedText style={styles.title}>{title}</ThemedText>
          <ThemedText style={styles.count}>{count} invoice{count !== 1 ? 's' : ''}</ThemedText>
        </View>
      </View>
      <ThemedText style={styles.amount}>{amount}</ThemedText>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderRadius: 16,
    marginBottom: 10,
  },
  leftContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 38,
    height: 38,
    borderRadius: 19,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  textContainer: {
    justifyContent: 'center',
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
    color: '#222222',
  },
  count: {
    fontSize: 13,
    color: '#8E8E93',
    fontWeight: '400',
  },
  amount: {
    fontSize: 17,
    fontWeight: '700',
    color: '#222222',
  },
}); 