# 🚀 Settings Implementation Roadmap

**Current Status: Phase 2 Complete - Ready for Phase 3**
- ✅ Phase 1: Core Settings Infrastructure - COMPLETE
- ✅ Phase 2: Account & Subscription Management - COMPLETE
- ✅ Phase 3: Invoice Design & Defaults - COMPLETE
- ✅ Phase 4: Navigation & Integration - COMPLETE
- ✅ Phase 5: Support & Feedback Systems - COMPLETE
- 🔄 Phase 6: Polish & Testing - READY

## 🎯 **GOAL**
Implement complete settings functionality following clean architecture patterns:
- **Domain-driven structure**: Separate services by settings domain
- **Clean hooks**: No parameters, computed selectors, elegant destructuring
- **Provider pattern**: Mock/API abstraction for all settings data
- **React Query**: Server state management with caching
- **Zustand**: UI preferences and cross-component state
- **Type safety**: Strict TypeScript with Zod schemas

---

## 📋 **Settings Analysis from settings.tsx**

### **Current Settings Items:**
1. **Account Details** - User profile management
2. **Subscription** - Billing and plan management  
3. **Invoice Design** - Template customization
4. **Defaults** - Currency, tax rates, payment terms
5. **Clients** - Client management (existing - navigation only)
6. **Items & Services** - Service management (existing - navigation only)
7. **Help & Support** - Documentation and support
8. **Send Feedback** - Feedback submission
9. **Rate App** - App store integration

### **Implementation Status:**
- ✅ **Clients** - Already exists, need navigation link
- ✅ **Items & Services** - Already exists, need navigation link
- ✅ **Account Details** - Complete with working screen
- ✅ **Subscription** - Complete with working screen
- 🔄 **Invoice Design** - Infrastructure ready, screen needed
- 🔄 **Defaults** - Infrastructure ready, screen needed
- 🔄 **Help & Support** - Infrastructure ready, screen needed
- 🔄 **Send Feedback** - Infrastructure ready, screen needed
- 🔄 **Rate App** - Infrastructure ready, integration needed

---

## 🏗️ **Architecture Requirements**

### **1. Data Layer (Following cursor_rules.md patterns)**
```
defs/
├── user.ts             # User profile schemas
├── subscription.ts     # Subscription/billing schemas
├── invoice-template.ts # Design template schemas
├── app-defaults.ts     # Default settings schemas
├── feedback.ts         # Feedback submission schemas
└── support.ts          # Help/support schemas

data/mock-database.ts   # Extended with settings data
core/providers/         # Extended with settings methods
```

### **2. Service Layer (Clean separation)**
```
services/
├── user/
│   ├── profile.ts      # fetchProfile() + useProfile()
│   ├── update.ts       # updateProfile() + useUpdateProfile()
│   └── avatar.ts       # uploadAvatar() + useUploadAvatar()
├── subscription/
│   ├── subscription.ts # fetchSubscription() + useSubscription()
│   ├── plans.ts        # fetchPlans() + usePlans()
│   └── billing.ts      # updateBilling() + useUpdateBilling()
├── design/
│   ├── templates.ts    # fetchTemplates() + useTemplates()
│   ├── create.ts       # createTemplate() + useCreateTemplate()
│   └── customize.ts    # updateTemplate() + useUpdateTemplate()
├── defaults/
│   ├── defaults.ts     # fetchDefaults() + useDefaults()
│   └── update.ts       # updateDefaults() + useUpdateDefaults()
├── feedback/
│   ├── submit.ts       # submitFeedback() + useSubmitFeedback()
│   └── categories.ts   # getFeedbackCategories()
└── support/
    ├── articles.ts     # fetchHelpArticles() + useHelpArticles()
    └── contact.ts      # contactSupport() + useContactSupport()
```

### **3. Store Layer (Zustand for UI state)**
```
stores/
├── settings.ts         # Settings navigation and UI state
├── design.ts           # Invoice design preferences
├── defaults.ts         # Default settings cache
└── user-preferences.ts # User UI preferences
```

### **4. Screen Layer (Clean components)**
```
app/settings/
├── account/
│   ├── profile.tsx           # Account details form
│   ├── avatar.tsx            # Avatar upload
│   └── security.tsx          # Password/security
├── subscription/
│   ├── overview.tsx          # Current subscription
│   ├── plans.tsx             # Available plans
│   ├── billing.tsx           # Billing details
│   └── history.tsx           # Payment history
├── design/
│   ├── templates.tsx         # Template selection
│   ├── customize.tsx         # Template customization
│   ├── preview.tsx           # Design preview
│   └── colors.tsx            # Color scheme
├── defaults/
│   ├── currency.tsx          # Currency settings
│   ├── taxes.tsx             # Default tax rates
│   ├── terms.tsx             # Payment terms
│   └── numbering.tsx         # Invoice numbering
├── support/
│   ├── help.tsx              # Help articles
│   ├── contact.tsx           # Contact support
│   └── feedback.tsx          # Send feedback
└── _layout.tsx               # Settings navigation
```

---

## 🔄 **IMPLEMENTATION PHASES**

## **Phase 1: Core Settings Infrastructure** ✅

**Status: COMPLETE** - Full settings infrastructure with type definitions, mock database, provider methods, service layer, and API interface updates.

### ✅ **Completed Phase 1 Work**
- **Type Definitions** (`defs/` directory) - ✅ **COMPLETE**
  - All Zod schemas for user, subscription, templates, defaults, feedback, support
  - Proper TypeScript types with validation and input/output schemas
  
- **Mock Database Extensions** (`data/mock-database.ts`) - ✅ **COMPLETE**
  - Extended with all settings data: user profiles, subscription plans, templates, defaults
  - Comprehensive sample data with realistic content and relationships
  
- **Mock Provider Extensions** (`core/providers/mock-provider.ts`) - ✅ **COMPLETE**
  - 20+ new methods covering user, subscription, template, defaults, feedback, support
  - Proper async simulation with delays and error handling
  
- **Settings Store** (`stores/settingsStore.ts`) - ✅ **COMPLETE**
  - Comprehensive Zustand store with UI state management and currency utilities
  - 40+ selectors for navigation, forms, modals, search, features
  - Currency formatting functions (formatCurrency, getCurrencyCode, getCurrencySymbol)
  - UI preferences management for invoice creation (setItemsCollapsed, setTaxCollapsed, setFooterPinned)
  - Complete integration with existing app components

### ✅ **Completed Phase 1 Work (Full Implementation)**
- **Service Layer** (`services/`) - ✅ **COMPLETE**
  - `services/user/profile.ts` - User profile fetching with React Query
  - `services/user/update.ts` - Profile updates and avatar upload with mutations
  - `services/user/security.ts` - Security settings management
  - `services/subscription/subscription.ts` - Subscription plans, changes, cancellations
  - `services/templates/templates.ts` - Invoice template CRUD operations
  - `services/defaults/app-defaults.ts` - App defaults and currency management
  - `services/feedback/feedback.ts` - Feedback submission and management
  - `services/support/support.ts` - Help articles, FAQs, knowledge base search
  - `services/settings/index.ts` - Clean service exports
  
- **API Provider Interface** - ✅ **COMPLETE**
  - Updated `core/providers/api-provider-interface.ts` with 20+ settings methods
  - User profile, security, subscription, templates, defaults, feedback, support
  - Proper TypeScript contracts for all settings operations
  
- **React Query Configuration** - ✅ **COMPLETE**
  - Enhanced `core/query-client.ts` with settings-specific defaults
  - Optimized stale times and garbage collection for different data types
  - Proper cache invalidation strategies in all mutations

### **1.1 Create Type Definitions** (Legacy documentation)

#### **defs/user.ts**
```typescript
import { z } from 'zod';

export const UserProfileSchema = z.object({
  id: z.string(),
  firstName: z.string().min(1, 'First name required'),
  lastName: z.string().min(1, 'Last name required'),
  email: z.string().email('Valid email required'),
  phone: z.string().optional(),
  avatar: z.string().url().optional(),
  timezone: z.string().default('UTC'),
  dateFormat: z.enum(['MM/DD/YYYY', 'DD/MM/YYYY', 'YYYY-MM-DD']).default('MM/DD/YYYY'),
  language: z.string().default('en'),
  notifications: z.object({
    email: z.boolean().default(true),
    push: z.boolean().default(true),
    marketing: z.boolean().default(false),
  }),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type UserProfile = z.infer<typeof UserProfileSchema>;

export const UpdateUserProfileSchema = UserProfileSchema.partial().omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export type UpdateUserProfileInput = z.infer<typeof UpdateUserProfileSchema>;
```

#### **defs/subscription.ts**
```typescript
import { z } from 'zod';

export const SubscriptionPlanSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  price: z.number(),
  currency: z.string(),
  interval: z.enum(['month', 'year']),
  features: z.array(z.string()),
  limits: z.object({
    organizations: z.number(),
    invoices: z.number(),
    clients: z.number(),
    storage: z.number(), // in MB
  }),
  isPopular: z.boolean().default(false),
  isActive: z.boolean().default(true),
});

export const SubscriptionSchema = z.object({
  id: z.string(),
  userId: z.string(),
  planId: z.string(),
  status: z.enum(['active', 'past_due', 'canceled', 'trial']),
  currentPeriodStart: z.date(),
  currentPeriodEnd: z.date(),
  trialEnd: z.date().optional(),
  cancelAtPeriodEnd: z.boolean().default(false),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type SubscriptionPlan = z.infer<typeof SubscriptionPlanSchema>;
export type Subscription = z.infer<typeof SubscriptionSchema>;
```

#### **defs/invoice-template.ts**
```typescript
import { z } from 'zod';

export const ColorSchemeSchema = z.object({
  primary: z.string(),
  secondary: z.string(),
  accent: z.string(),
  text: z.string(),
  background: z.string(),
});

export const InvoiceTemplateSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  thumbnail: z.string().url(),
  isPremium: z.boolean().default(false),
  colorScheme: ColorSchemeSchema,
  layout: z.object({
    headerStyle: z.enum(['minimal', 'classic', 'modern']),
    fontFamily: z.enum(['Inter', 'Roboto', 'Open Sans', 'Lato']),
    fontSize: z.enum(['small', 'medium', 'large']),
    logoPosition: z.enum(['left', 'center', 'right']),
    showCompanyDetails: z.boolean().default(true),
    showClientDetails: z.boolean().default(true),
    itemTableStyle: z.enum(['minimal', 'bordered', 'striped']),
  }),
  customization: z.object({
    logoUrl: z.string().url().optional(),
    footerText: z.string().optional(),
    thanksMessage: z.string().optional(),
    showPaymentInstructions: z.boolean().default(true),
  }),
  organizationId: z.string(),
  isDefault: z.boolean().default(false),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type InvoiceTemplate = z.infer<typeof InvoiceTemplateSchema>;
export type ColorScheme = z.infer<typeof ColorSchemeSchema>;
```

#### **defs/app-defaults.ts**
```typescript
import { z } from 'zod';

export const AppDefaultsSchema = z.object({
  id: z.string(),
  organizationId: z.string(),
  currency: z.object({
    code: z.string().length(3),
    symbol: z.string(),
    position: z.enum(['before', 'after']).default('before'),
  }),
  taxes: z.object({
    defaultRate: z.number().min(0).max(100),
    taxName: z.string().default('Tax'),
    inclusive: z.boolean().default(false),
    enabled: z.boolean().default(true),
  }),
  payment: z.object({
    defaultTerms: z.enum(['Due on receipt', 'Net 15', 'Net 30', 'Net 45', 'Net 60']).default('Net 30'),
    lateFeeEnabled: z.boolean().default(false),
    lateFeeAmount: z.number().min(0).default(0),
    lateFeeType: z.enum(['fixed', 'percentage']).default('fixed'),
  }),
  numbering: z.object({
    prefix: z.string().default('INV'),
    format: z.enum(['sequential', 'date-based']).default('sequential'),
    startNumber: z.number().min(1).default(1),
    padZeros: z.number().min(0).max(10).default(3),
  }),
  notifications: z.object({
    sendReminders: z.boolean().default(true),
    reminderDays: z.array(z.number()).default([7, 3, 1]),
    autoMarkOverdue: z.boolean().default(true),
    overdueDays: z.number().default(30),
  }),
  branding: z.object({
    companyLogo: z.string().url().optional(),
    websiteUrl: z.string().url().optional(),
    socialLinks: z.object({
      linkedin: z.string().url().optional(),
      twitter: z.string().url().optional(),
      facebook: z.string().url().optional(),
    }),
  }),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type AppDefaults = z.infer<typeof AppDefaultsSchema>;
```

#### **defs/feedback.ts & defs/support.ts**
```typescript
// feedback.ts
export const FeedbackSchema = z.object({
  id: z.string(),
  userId: z.string(),
  category: z.enum(['bug', 'feature', 'improvement', 'general']),
  subject: z.string().min(5, 'Subject too short'),
  message: z.string().min(20, 'Message too short'),
  priority: z.enum(['low', 'medium', 'high']).default('medium'),
  status: z.enum(['open', 'in_progress', 'resolved']).default('open'),
  attachments: z.array(z.string().url()).default([]),
  createdAt: z.date(),
  updatedAt: z.date(),
});

// support.ts  
export const HelpArticleSchema = z.object({
  id: z.string(),
  title: z.string(),
  content: z.string(),
  category: z.string(),
  tags: z.array(z.string()),
  views: z.number().default(0),
  helpful: z.number().default(0),
  featured: z.boolean().default(false),
  createdAt: z.date(),
  updatedAt: z.date(),
});
```

### **1.2 Extend Mock Database**

#### **Update data/mock-database.ts**
```typescript
// Add to MockDatabase class
static userProfiles: UserProfile[] = [
  {
    id: 'user_001',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+****************',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
    timezone: 'America/New_York',
    dateFormat: 'MM/DD/YYYY',
    language: 'en',
    notifications: {
      email: true,
      push: true,
      marketing: false,
    },
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
  }
];

static subscriptionPlans: SubscriptionPlan[] = [
  {
    id: 'plan_starter',
    name: 'Starter',
    description: 'Perfect for freelancers and small businesses',
    price: 9.99,
    currency: 'USD',
    interval: 'month',
    features: [
      'Up to 2 organizations',
      'Unlimited invoices',
      'Basic templates',
      'Email support',
    ],
    limits: {
      organizations: 2,
      invoices: -1, // unlimited
      clients: 100,
      storage: 1024, // 1GB
    },
    isPopular: false,
    isActive: true,
  },
  {
    id: 'plan_professional',
    name: 'Professional',
    description: 'For growing businesses with advanced needs',
    price: 19.99,
    currency: 'USD',
    interval: 'month',
    features: [
      'Up to 10 organizations',
      'Unlimited invoices',
      'Premium templates',
      'Priority support',
      'Custom branding',
      'Advanced reporting',
    ],
    limits: {
      organizations: 10,
      invoices: -1,
      clients: 1000,
      storage: 5120, // 5GB
    },
    isPopular: true,
    isActive: true,
  }
];

static invoiceTemplates: InvoiceTemplate[] = [
  {
    id: 'template_minimal',
    name: 'Minimal',
    description: 'Clean and simple design',
    thumbnail: 'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=300&h=400&fit=crop',
    isPremium: false,
    colorScheme: {
      primary: '#2563eb',
      secondary: '#64748b',
      accent: '#0ea5e9',
      text: '#1e293b',
      background: '#ffffff',
    },
    layout: {
      headerStyle: 'minimal',
      fontFamily: 'Inter',
      fontSize: 'medium',
      logoPosition: 'left',
      showCompanyDetails: true,
      showClientDetails: true,
      itemTableStyle: 'minimal',
    },
    customization: {
      footerText: 'Thank you for your business!',
      thanksMessage: 'We appreciate your prompt payment.',
      showPaymentInstructions: true,
    },
    organizationId: '1',
    isDefault: true,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
  }
];

static appDefaults: AppDefaults[] = [
  {
    id: 'defaults_1',
    organizationId: '1',
    currency: {
      code: 'USD',
      symbol: '$',
      position: 'before',
    },
    taxes: {
      defaultRate: 7.5,
      taxName: 'Sales Tax',
      inclusive: false,
      enabled: true,
    },
    payment: {
      defaultTerms: 'Net 30',
      lateFeeEnabled: false,
      lateFeeAmount: 0,
      lateFeeType: 'fixed',
    },
    numbering: {
      prefix: 'INV',
      format: 'sequential',
      startNumber: 1,
      padZeros: 3,
    },
    notifications: {
      sendReminders: true,
      reminderDays: [7, 3, 1],
      autoMarkOverdue: true,
      overdueDays: 30,
    },
    branding: {
      companyLogo: undefined,
      websiteUrl: undefined,
      socialLinks: {
        linkedin: undefined,
        twitter: undefined,
        facebook: undefined,
      },
    },
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
  }
];
```

### **1.3 Extend Mock Provider**

#### **Update core/providers/mock-provider.ts**
```typescript
// Add to MockProvider class methods:

// User Profile
async getUserProfile(userId: string): Promise<UserProfile> {
  await this.delay();
  const profile = MockDatabase.userProfiles.find(p => p.id === userId);
  if (!profile) throw new Error('User profile not found');
  return profile;
}

async updateUserProfile(userId: string, updates: UpdateUserProfileInput): Promise<UserProfile> {
  await this.delay(250);
  const index = MockDatabase.userProfiles.findIndex(p => p.id === userId);
  if (index === -1) throw new Error('User profile not found');
  
  MockDatabase.userProfiles[index] = {
    ...MockDatabase.userProfiles[index],
    ...updates,
    updatedAt: new Date(),
  };
  
  return MockDatabase.userProfiles[index];
}

// Subscription
async getUserSubscription(userId: string): Promise<Subscription | null> {
  await this.delay();
  return MockDatabase.subscriptions.find(s => s.userId === userId) || null;
}

async getSubscriptionPlans(): Promise<SubscriptionPlan[]> {
  await this.delay();
  return MockDatabase.subscriptionPlans.filter(p => p.isActive);
}

// Templates
async getInvoiceTemplates(organizationId: string): Promise<InvoiceTemplate[]> {
  await this.delay();
  return MockDatabase.invoiceTemplates.filter(t => 
    t.organizationId === organizationId || !t.organizationId // Global templates
  );
}

// App Defaults
async getAppDefaults(organizationId: string): Promise<AppDefaults> {
  await this.delay();
  const defaults = MockDatabase.appDefaults.find(d => d.organizationId === organizationId);
  if (!defaults) throw new Error('App defaults not found');
  return defaults;
}

async updateAppDefaults(organizationId: string, updates: Partial<AppDefaults>): Promise<AppDefaults> {
  await this.delay(250);
  const index = MockDatabase.appDefaults.findIndex(d => d.organizationId === organizationId);
  if (index === -1) throw new Error('App defaults not found');
  
  MockDatabase.appDefaults[index] = {
    ...MockDatabase.appDefaults[index],
    ...updates,
    updatedAt: new Date(),
  };
  
  return MockDatabase.appDefaults[index];
}
```

### **1.4 Create Settings Store**

#### **stores/settings.ts**
```typescript
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

type SettingsState = {
  activeSection: string | null;
  designPreviewMode: 'desktop' | 'mobile';
  unsavedChanges: boolean;
  currentTemplate: string | null;
};

type SettingsActions = {
  setActiveSection: (section: string | null) => void;
  setDesignPreviewMode: (mode: 'desktop' | 'mobile') => void;
  setUnsavedChanges: (hasChanges: boolean) => void;
  setCurrentTemplate: (templateId: string | null) => void;
  resetSettings: () => void;
};

export const useSettingsStore = create<SettingsState & SettingsActions>()(
  persist(
    (set) => ({
      activeSection: null,
      designPreviewMode: 'desktop',
      unsavedChanges: false,
      currentTemplate: null,
      
      setActiveSection: (section) => set({ activeSection: section }),
      setDesignPreviewMode: (mode) => set({ designPreviewMode: mode }),
      setUnsavedChanges: (hasChanges) => set({ unsavedChanges: hasChanges }),
      setCurrentTemplate: (templateId) => set({ currentTemplate: templateId }),
      resetSettings: () => set({
        activeSection: null,
        designPreviewMode: 'desktop',
        unsavedChanges: false,
        currentTemplate: null,
      }),
    }),
    { name: 'settings-ui' }
  ),
);

// Computed selectors
export const useActiveSection = () => 
  useSettingsStore((state) => state.activeSection);

export const useHasUnsavedChanges = () =>
  useSettingsStore((state) => state.unsavedChanges);
```

---

## **Phase 2: Account & Subscription Management** ✅

**Status: COMPLETE** - Full interactive account and subscription management with production-ready forms, validation, and real functionality.

### ✅ **Completed Phase 2 Work**
- **Interactive Account Management** (`app/settings/account.tsx`) - ✅ **COMPLETE**
  - Editable profile forms with real-time validation (name, email, phone)
  - Custom dropdown selectors for timezone, date format, and language
  - Interactive notification toggles with proper state management
  - Avatar upload functionality with image picker integration
  - Form validation using Zod schemas with contextual error display
  - Optimistic updates with proper error handling and rollback
  - Unsaved changes detection with save/reset actions

- **Interactive Subscription Management** (`app/settings/subscription.tsx`) - ✅ **COMPLETE**  
  - Real plan upgrade/downgrade functionality with confirmation dialogs
  - Smart pricing logic with immediate vs end-of-period timing
  - Visual usage tracking with color-coded progress bars
  - Expandable billing history with invoice download options
  - Complete subscription cancellation flow with proper confirmations
  - Plan comparison with clear current plan indication
  - Professional modal confirmations for all critical actions

- **Service Layer Integration** (`hooks/useSettingsQueries.ts`) - ✅ **COMPLETE**
  - Clean integration with existing service layer from Phase 1
  - Proper re-exports of service functions (useUserProfile, useUpdateUserProfile, etc.)
  - Combined hooks for common use cases (useAccountData, useSubscriptionData)
  - Consistent error handling and loading states across all components

- **Production-Ready Features** - ✅ **COMPLETE**
  - Full form validation with Zod schema integration
  - Real-time validation feedback for critical fields
  - Professional loading states and success/error feedback
  - Avatar upload with permission handling and file management
  - Usage visualization with smart color coding (green/yellow/red)
  - Expandable UI sections for better space utilization
  - Modal confirmations with proper accessibility

### **2.1 User Profile Services** (Legacy documentation)

#### **services/user/profile.ts**
```typescript
import { useQuery } from '@tanstack/react-query';
import { getApiProvider } from '@/core/providers/provider-factory';

const provider = getApiProvider();

export const fetchUserProfile = async (userId: string) => {
  return await provider.getUserProfile(userId);
};

export const useUserProfile = () => {
  const userId = 'user_001'; // From auth context
  
  const {
    error,
    isPending: loading,
    data: profile,
  } = useQuery({
    queryKey: ['user-profile', { userId }],
    queryFn: () => fetchUserProfile(userId),
    enabled: !!userId,
  });

  return { profile, loading, error };
};
```

#### **services/user/update.ts**
```typescript
import { useMutation } from '@tanstack/react-query';
import { queryClient } from '@/core/query-client';

export const updateUserProfile = async (data: UpdateUserProfileDto) => {
  const response = await provider.updateUserProfile(data.userId, data);
  
  // Update cache
  queryClient.setQueryData(['user-profile', { userId: data.userId }], response);
  
  return response;
};

export const useUpdateUserProfile = () => {
  const userId = 'user_001';
  
  const {
    error,
    isPending: loading,
    mutateAsync: updateProfileFn,
  } = useMutation({
    mutationFn: (data: UpdateUserProfileInput) => 
      updateUserProfile({ ...data, userId }),
  });

  return { updateProfile: updateProfileFn, loading, error };
};
```

### **2.2 Subscription Services**

#### **services/subscription/subscription.ts**
```typescript
export const fetchUserSubscription = async (userId: string) => {
  return await provider.getUserSubscription(userId);
};

export const fetchSubscriptionPlans = async () => {
  return await provider.getSubscriptionPlans();
};

export const useUserSubscription = () => {
  const userId = 'user_001';
  
  const {
    error,
    isPending: loading,
    data: subscription,
  } = useQuery({
    queryKey: ['user-subscription', { userId }],
    queryFn: () => fetchUserSubscription(userId),
    enabled: !!userId,
  });

  return { subscription, loading, error };
};

export const useSubscriptionPlans = () => {
  const {
    error,
    isPending: loading,
    data: plans,
  } = useQuery({
    queryKey: ['subscription-plans'],
    queryFn: fetchSubscriptionPlans,
  });

  return { plans, loading, error };
};
```

### **2.3 Account Settings Screens**

#### **app/settings/account/profile.tsx**
```typescript
import { useState } from 'react';
import { ScrollView, StyleSheet, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { PageHeader } from '@/components/PageHeader';
import { Input } from '@/components/ui/Input';
import { Button } from '@/components/ui/Button';
import { useUserProfile, useUpdateUserProfile } from '@/services/user/profile';

export default function AccountProfileScreen() {
  const { profile, loading } = useUserProfile();
  const { updateProfile, loading: updating } = useUpdateUserProfile();
  
  const [firstName, setFirstName] = useState(profile?.firstName || '');
  const [lastName, setLastName] = useState(profile?.lastName || '');
  const [email, setEmail] = useState(profile?.email || '');
  const [phone, setPhone] = useState(profile?.phone || '');
  
  const handleSave = async () => {
    try {
      await updateProfile({
        firstName,
        lastName,
        email,
        phone,
      });
      // Show success message
    } catch (error) {
      // Show error message
    }
  };
  
  if (loading) return <LoadingSpinner />;
  
  return (
    <SafeAreaView style={{ flex: 1 }}>
      <PageHeader title="Account Details" />
      
      <ScrollView style={styles.container}>
        <Input
          label="First Name"
          value={firstName}
          onChangeText={setFirstName}
        />
        
        <Input
          label="Last Name"
          value={lastName}
          onChangeText={setLastName}
        />
        
        <Input
          label="Email"
          value={email}
          onChangeText={setEmail}
          keyboardType="email-address"
        />
        
        <Input
          label="Phone"
          value={phone}
          onChangeText={setPhone}
          keyboardType="phone-pad"
        />
        
        <Button
          title="Save Changes"
          onPress={handleSave}
          disabled={updating}
          loading={updating}
        />
      </ScrollView>
    </SafeAreaView>
  );
}
```

---

## **Phase 3: Invoice Design & Defaults** ✅

### **📋 Implementation Complete!**

**✅ Created Comprehensive Invoice Design System:**
- Full template selection with filtering by category (business, creative, professional, minimal, modern)
- Live customization interface with tabbed navigation (Templates, Customize, Preview)
- Real-time color scheme editor with predefined palette library
- Interactive layout settings (header style, font family, logo positioning)
- Logo upload functionality with image picker integration
- Template creation and duplication features
- Professional template preview system

**✅ Created Complete App Defaults Management:**
- Sectioned interface for currency, taxes, payment, numbering, notifications, and branding
- Interactive currency selector with symbol positioning and decimal formatting
- Advanced tax configuration with rates, inclusive/exclusive options
- Comprehensive payment terms with late fee management
- Invoice numbering system with prefix, padding, and format options
- Smart notification settings with reminder scheduling
- Branding customization with footer text and website links

**✅ Advanced UI Features Implemented:**
- Unsaved changes tracking with save/reset functionality
- Smart dropdown components with search and selection
- Segmented controls for option selection
- Toggle switches for boolean settings
- Color palette modal with predefined schemes
- Professional loading states and error handling
- Form validation with real-time feedback

**✅ Service Layer Integration:**
- Complete integration with existing Phase 1 service layer
- React Query for server state management with optimistic updates
- Error handling with user-friendly feedback
- Type-safe form management with Zod validation
- Mock data provider for development testing

## **Phase 3: Invoice Design & Defaults** ✅

### **3.1 Design Services**

#### **services/design/templates.ts**
```typescript
export const fetchInvoiceTemplates = async (organizationId: string) => {
  return await provider.getInvoiceTemplates(organizationId);
};

export const useInvoiceTemplates = () => {
  const organizationId = useActiveOrganizationId();
  
  const {
    error,
    isPending: loading,
    data: templates,
  } = useQuery({
    queryKey: ['invoice-templates', { organizationId }],
    queryFn: () => fetchInvoiceTemplates(organizationId!),
    enabled: !!organizationId,
  });

  return { templates, loading, error };
};
```

### **3.2 Defaults Services**

#### **services/defaults/defaults.ts**
```typescript
export const fetchAppDefaults = async (organizationId: string) => {
  return await provider.getAppDefaults(organizationId);
};

export const updateAppDefaults = async (data: UpdateAppDefaultsDto) => {
  const response = await provider.updateAppDefaults(data.organizationId, data);
  
  // Update cache
  queryClient.setQueryData(['app-defaults', { organizationId: data.organizationId }], response);
  
  return response;
};

export const useAppDefaults = () => {
  const organizationId = useActiveOrganizationId();
  
  const {
    error,
    isPending: loading,
    data: defaults,
  } = useQuery({
    queryKey: ['app-defaults', { organizationId }],
    queryFn: () => fetchAppDefaults(organizationId!),
    enabled: !!organizationId,
  });

  return { defaults, loading, error };
};

export const useUpdateAppDefaults = () => {
  const organizationId = useActiveOrganizationId();
  
  const {
    error,
    isPending: loading,
    mutateAsync: updateDefaultsFn,
  } = useMutation({
    mutationFn: (data: Partial<AppDefaults>) => 
      updateAppDefaults({ ...data, organizationId: organizationId! }),
  });

  return { updateDefaults: updateDefaultsFn, loading, error };
};
```

---

## **Phase 4: Navigation & Integration** ✅

### **📋 Implementation Complete!**

**✅ Created Complete Navigation System:**
- Built `app/settings/_layout.tsx` with stack navigation for all settings screens
- Configured proper header styling with primary color theme
- Added navigation options for Account, Subscription, Design, Defaults, Support, and Feedback
- Implemented back navigation with clean header design

**✅ Enhanced Main Settings Screen:**
- Added router import and navigation functionality to `app/(tabs)/settings.tsx`
- Created navigation handlers for all settings sections:
  - Account Details → `/settings/account`
  - Subscription → `/settings/subscription`
  - Invoice Design → `/settings/design`
  - Defaults → `/settings/defaults`
  - Help & Support → `/settings/support` (placeholder)
  - Send Feedback → `/settings/feedback` (placeholder)
- Implemented app store rating integration with alert confirmation
- Added proper error handling for unsupported device features

**✅ Created Placeholder Screens for Phase 5:**
- Built `app/settings/support.tsx` with comprehensive feature preview
- Built `app/settings/feedback.tsx` with planned functionality overview
- Both screens display development status and planned features
- Professional styling consistent with existing design system

**✅ Navigation Flow Integration:**
- All Phase 2 and Phase 3 screens now accessible through main settings
- Seamless navigation between settings sections
- Proper back navigation to main settings screen
- Tab-based navigation preserved for main app sections

**✅ Technical Implementation:**
- 0 TypeScript compilation errors
- Proper type safety throughout navigation system
- Clean router-based navigation using expo-router
- Consistent styling with existing design system
- Professional loading states and error handling

## **Phase 4: Navigation & Integration** ✅

### **4.1 Settings Navigation Layout**

#### **app/settings/_layout.tsx**
```typescript
import { Stack } from 'expo-router';

export default function SettingsLayout() {
  return (
    <Stack
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen name="index" />
      <Stack.Screen name="account/profile" />
      <Stack.Screen name="subscription/overview" />
      <Stack.Screen name="design/templates" />
      <Stack.Screen name="defaults/currency" />
      <Stack.Screen name="support/help" />
      <Stack.Screen name="support/feedback" />
    </Stack>
  );
}
```

### **4.2 Update Main Settings Screen**

#### **Update app/(tabs)/settings.tsx**
```typescript
// Add onPress handlers for each setting item:

<SettingsItem 
  icon="person-outline"
  title="Account Details"
  subtitle="Manage your account details"
  iconBgColor={colors.avatarBackground.primary}
  iconColor={colors.primary}
  onPress={() => router.push('/settings/account/profile')}
/>

<SettingsItem 
  icon="card-outline"
  title="Subscription"
  subtitle="Manage your subscription"
  iconBgColor={colors.avatarBackground.primary}
  iconColor={colors.primary}
  onPress={() => router.push('/settings/subscription/overview')}
/>

// ... etc for all settings items
```

---

## **Phase 5: Support & Feedback Systems** ✅

### **📋 Implementation Complete!**

**✅ Created Comprehensive Support System:**
- Full help & support screen with tabbed interface (Help Articles, FAQ, Contact Us)
- Live search functionality across articles and FAQs
- Category filtering with horizontal scrollable chips
- Expandable article and FAQ content with tags
- Professional empty states and loading indicators
- Contact form with category selection and full validation

**✅ Created Complete Feedback Management:**
- Dual-tab interface (Send Feedback, My Feedback)
- Smart feedback form with category and priority dropdowns
- Feedback history with status tracking and priority badges
- Visual status indicators (open, in progress, resolved)
- Form validation with real-time feedback
- Success/error handling with user-friendly alerts

**✅ Advanced UI Features Implemented:**
- Tab-based navigation with active state indicators
- Category filtering system for content organization
- Real-time search with filtered results
- Professional card layouts with expandable content
- Smart form validation and submission handling
- Status tracking with color-coded indicators

**✅ Service Layer Integration:**
- Complete integration with existing Phase 1 service layer
- React Query for server state management with proper caching
- Error handling with user-friendly feedback and alerts
- Type-safe form management with proper TypeScript interfaces
- Production-ready contact form submission

### **5.1 Feedback Services** (Legacy documentation)

#### **services/feedback/submit.ts**
```typescript
export const submitFeedback = async (data: CreateFeedbackDto) => {
  const response = await provider.submitFeedback(data);
  
  // Add to cache if needed
  queryClient.setQueryData(['user-feedback', { userId: data.userId }], (cache: Feedback[]) => {
    if (!cache) return [response];
    return [...cache, response];
  });
  
  return response;
};

export const useSubmitFeedback = () => {
  const userId = 'user_001';
  
  const {
    error,
    isPending: loading,
    mutateAsync: submitFeedbackFn,
  } = useMutation({
    mutationFn: (data: CreateFeedbackInput) => 
      submitFeedback({ ...data, userId }),
  });

  return { submitFeedback: submitFeedbackFn, loading, error };
};
```

### **5.2 Support Services**

#### **services/support/articles.ts**
```typescript
export const fetchHelpArticles = async () => {
  return await provider.getHelpArticles();
};

export const useHelpArticles = () => {
  const {
    error,
    isPending: loading,
    data: articles,
  } = useQuery({
    queryKey: ['help-articles'],
    queryFn: fetchHelpArticles,
  });

  return { articles, loading, error };
};
```

---

## **Phase 6: Polish & Testing** 🔄

### **6.1 Navigation Integration**
- Link existing Clients and Items & Services screens
- Add breadcrumb navigation
- Handle deep linking

### **6.2 UI Polish**
- Consistent styling across all settings screens
- Loading states and error handling
- Form validation and user feedback

### **6.3 Testing**
- Test all settings functionality
- Verify navigation flows
- Test data persistence
- Verify cache invalidation

---

## ✅ **IMPLEMENTATION CHECKLIST**

### **📋 Phase 1: Core Settings Infrastructure** ✅
- [x] Create all type definitions in `defs/` directory
- [x] Extend `MockDatabase` with settings data
- [x] Extend `MockProvider` with settings methods
- [x] Create `stores/settings.ts` for UI state
- [x] Create computed selectors for settings store
- [x] Test basic settings data flow

### **📋 Phase 2: Account & Subscription Management** ✅
- [x] Create `services/user/profile.ts` with clean hooks
- [x] Create `services/user/update.ts` with mutations  
- [x] Create `services/subscription/subscription.ts`
- [x] Create interactive `app/settings/account.tsx` screen with full form functionality
- [x] Create interactive `app/settings/subscription.tsx` screen with plan management
- [x] Implement form validation with Zod schemas and real-time feedback
- [x] Add avatar upload functionality with image picker integration
- [x] Implement notification preference toggles and settings management
- [x] Add subscription plan change functionality with confirmations
- [x] Implement usage tracking with visual progress indicators
- [x] Add billing history and payment management features
- [x] Integrate with existing service layer from Phase 1
- [x] Test profile updates, subscription changes, and all interactive features

### **📋 Phase 3: Invoice Design & Defaults** ✅
- [x] Create `services/design/templates.ts` (Already existed from Phase 1)
- [x] Create `services/defaults/defaults.ts` (Already existed from Phase 1)
- [x] Create comprehensive `app/settings/design.tsx` screen with:
  - Template selection and filtering by category
  - Live customization interface with tabbed navigation
  - Color scheme editor with predefined palettes
  - Layout settings (header style, fonts, positioning)
  - Logo upload with image picker integration
  - Template creation and duplication
- [x] Create comprehensive `app/settings/defaults.tsx` screen with:
  - Sectioned interface for all business settings
  - Currency configuration with formatting options
  - Tax settings with rates and calculations
  - Payment terms and late fee management
  - Invoice numbering with preview
  - Notification preferences
  - Branding customization
- [x] Implement template preview functionality with live updates
- [x] Test complete design customization flow
- [x] Integrate with existing service layer from Phase 1
- [x] Resolve all TypeScript errors (0 compilation errors)

### **📋 Phase 4: Navigation & Integration** ✅
- [x] Create `app/settings/_layout.tsx` navigation with stack navigation
- [x] Update main `settings.tsx` with comprehensive navigation handlers
- [x] Add navigation handlers for all settings sections (Account, Subscription, Design, Defaults)
- [x] Create placeholder screens for Support and Feedback (Phase 5 preparation)
- [x] Implement app store rating integration with user confirmation
- [x] Test all navigation flows and ensure TypeScript compliance
- [x] Verify seamless navigation between all settings screens
- [x] Maintain existing Clients and Services navigation (tab-based system works well)

### **📋 Phase 5: Support & Feedback Systems** ✅
- [x] Create production-ready support screen with help articles, FAQ, and contact form
- [x] Create comprehensive feedback screen with submission and history tracking
- [x] Implement real-time search and category filtering
- [x] Add professional form validation and error handling
- [x] Integrate with existing service layer from Phase 1
- [x] Test complete support and feedback workflow

### **📋 Phase 6: Polish & Testing** 🔄
- [ ] Add consistent styling across all screens
- [ ] Implement loading states and error handling
- [ ] Add form validation throughout
- [ ] Test data persistence across app restarts
- [ ] Verify cache invalidation works correctly
- [ ] Add success/error user feedback
- [ ] Test complete settings workflow

### **📋 Final Verification** 🔄
- [ ] All settings screens implemented and functional
- [ ] Navigation flows work seamlessly
- [ ] Data persistence works correctly
- [ ] Clean architecture patterns followed throughout
- [ ] Type safety maintained across all components
- [ ] Cache management works properly
- [ ] User experience is smooth and intuitive

---

## 🎯 **SUCCESS CRITERIA**

### **✅ Architecture Compliance:**
- [ ] Service layer separation maintained
- [ ] Clean hooks with no parameters
- [ ] Computed selectors for store access
- [ ] Provider pattern for data abstraction
- [ ] Type safety with Zod schemas

### **✅ Functionality:**
- [ ] Complete account management
- [ ] Subscription display and management
- [ ] Invoice design customization
- [ ] App defaults configuration
- [ ] Feedback and support systems

### **✅ User Experience:**
- [ ] Intuitive navigation between settings
- [ ] Consistent design language
- [ ] Proper loading and error states
- [ ] Data persistence across sessions
- [ ] Responsive and performant interface

---

**🚀 READY TO START:** This roadmap provides comprehensive settings functionality following the project's clean architecture patterns. Each phase builds systematically toward a complete, polished settings system.

**🎯 IMMEDIATE NEXT STEP:** Start with Phase 1 - Create the core settings infrastructure with type definitions and mock data extensions. 