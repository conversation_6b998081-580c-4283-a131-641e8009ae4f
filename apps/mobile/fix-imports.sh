#!/bin/bash

# Fix import statements in mobile app after store refactoring

echo "Fixing import statements..."

# Fix organization-selectors imports
find . -name "*.tsx" -o -name "*.ts" | grep -v node_modules | xargs sed -i "s|from '@/stores/organization-selectors'|from '@/stores'|g"
find . -name "*.tsx" -o -name "*.ts" | grep -v node_modules | xargs sed -i "s|from '../../stores/organization-selectors'|from '@/stores'|g"

# Fix user-selectors imports  
find . -name "*.tsx" -o -name "*.ts" | grep -v node_modules | xargs sed -i "s|from '@/stores/user-selectors'|from '@/stores'|g"

# Fix organization store imports
find . -name "*.tsx" -o -name "*.ts" | grep -v node_modules | xargs sed -i "s|from '@/stores/organization'|from '@/stores'|g"

# Fix invoiceStore imports
find . -name "*.tsx" -o -name "*.ts" | grep -v node_modules | xargs sed -i "s|from '@/stores/invoiceStore'|from '@/stores'|g"

echo "Import fixes completed!"
