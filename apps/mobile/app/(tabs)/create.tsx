import { Button } from '@/components/ui';
import { colors } from '@/constants/Colors';
import { router } from 'expo-router';
import React from 'react';
import { StyleSheet, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

export default function CreateScreen() {
  const handleCreateInvoice = () => {
    router.push('/create-invoice-collapsible');
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Button
          title="Create Invoice"
          onPress={handleCreateInvoice}
          style={styles.button}
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  button: {
    width: '100%',
    maxWidth: 300,
  },
}); 