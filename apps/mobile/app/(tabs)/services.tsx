import { FloatingActionButton } from '@/components/FloatingActionButton';
import { PageHeader } from '@/components/PageHeader';
import type { ActionMenuItem } from '@/components/ui';
import { ActionMenu, Card, ConfirmationDialog, EmptyState, SearchInput, Typography } from '@/components/ui';
import { colors } from '@/constants/Colors';
import { useDeleteService } from '@repo/queries';
import { useItems as useServices } from '@repo/queries';
import { useHasActiveOrganization } from '@/stores';
import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useState } from 'react';
import { ActivityIndicator, Pressable, ScrollView, StyleSheet, View } from 'react-native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';
import { Service } from '@repo/schemas';

export default function ServicesScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const [deleteDialogVisible, setDeleteDialogVisible] = useState(false);
  const [serviceToDelete, setServiceToDelete] = useState<Service | null>(null);
  const insets = useSafeAreaInsets();
  
  // Clean organization guards
  const hasActiveOrganization = useHasActiveOrganization();

  // Use clean item hooks - no parameters, computed selectors
  const { 
    items: services = [], 
    loading, 
    error 
  } = useServices();
  
  const { deleteService } = useDeleteService();

  // Filter services based on search query
  const filteredServices = services.filter((service: Service) => 
    service.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (service.description && service.description.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const formatRate = (service: Service) => {
    const rate = service.pricing.rate;
    const unit = service.pricing.unit;
    const currency = service.pricing.currency;
    
    if (unit === 'fixed') {
      return `${currency} ${rate.toFixed(2)} fixed`;
    } else if (unit === 'hour') {
      return `${currency} ${rate.toFixed(2)}/hr`;
    } else if (unit === 'day') {
      return `${currency} ${rate.toFixed(2)}/day`;
    } else if (unit === 'month') {
      return `${currency} ${rate.toFixed(2)}/month`;
    } else if (unit === 'project') {
      return `${currency} ${rate.toFixed(2)}/project`;
    } else {
      return `${currency} ${rate.toFixed(2)}/${unit}`;
    }
  };

  const handleCreateService = () => {
    router.push('/create-service');
  };

  const handleEditService = (serviceId: string) => {
    router.push(`/edit-service?serviceId=${serviceId}`);
  };

  const handleViewService = (serviceId: string) => {
    router.push(`/service-detail?serviceId=${serviceId}`);
  };

  const handleDeleteService = async (serviceId: string) => {
    if (!hasActiveOrganization) {
      return;
    }
    
    const service = services.find((s: Service) => s.id === serviceId);
    if (!service) return;

    setServiceToDelete(service);
    setDeleteDialogVisible(true);
  };

  const confirmDelete = async () => {
    if (!serviceToDelete) return;

    try {
      await deleteService(serviceToDelete.id);
      setDeleteDialogVisible(false);
      setServiceToDelete(null);
      // Success handled by React Query cache invalidation
    } catch (error: any) {
      setDeleteDialogVisible(false);
      setServiceToDelete(null);
      console.error('Delete service error:', error);
      // TODO: Add toast notification for errors
    }
  };

  const cancelDelete = () => {
    setDeleteDialogVisible(false);
    setServiceToDelete(null);
  };

  const getServiceActions = (serviceId: string): ActionMenuItem[] => [
    {
      id: 'view',
      title: 'View Details',
      icon: 'document-text-outline',
      onPress: () => handleViewService(serviceId),
    },
    {
      id: 'edit',
      title: 'Edit',
      icon: 'create-outline',
      onPress: () => handleEditService(serviceId),
    },
    {
      id: 'delete',
      title: 'Delete',
      icon: 'trash-outline',
      onPress: () => handleDeleteService(serviceId),
      destructive: true,
    },
  ];

  // Organization selection guard
  if (!hasActiveOrganization) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: colors.primary }}>
        <StatusBar style="light" />
        <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
          <Typography variant="h3" color="secondary" style={{ marginBottom: 8 }}>
            Select an Organization
          </Typography>
          <Typography variant="body" color="secondary" center>
            Please select an organization to view services
          </Typography>
        </View>
      </SafeAreaView>
    );
  }

  // Loading state
  if (loading) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: colors.primary }}>
        <StatusBar style="light" />
        <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
          <ActivityIndicator size="large" color={colors.background} />
          <Typography variant="body" color="secondary" style={{ marginTop: 16 }}>
            Loading services...
          </Typography>
        </View>
      </SafeAreaView>
    );
  }

  // Error state
  if (error) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: colors.primary }}>
        <StatusBar style="light" />
        <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
          <Typography variant="h3" color="error" style={{ marginBottom: 8 }}>
            Error Loading Services
          </Typography>
          <Typography variant="body" color="secondary" center>
            {error?.message || 'Something went wrong'}
          </Typography>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.primary }}>
      <StatusBar style="light" />
      
      <ScrollView 
        style={styles.container}
        contentContainerStyle={[
          styles.contentContainer, 
          { paddingBottom: Math.max(32, insets.bottom + 70) }
        ]}
        showsVerticalScrollIndicator={false}
      >
        {/* Header with Organization Selector */}
        <PageHeader 
          title="Services"
          subtitle="Manage your service offerings"
        />
        
        {/* Search Bar */}
        <SearchInput
          placeholder="Search services..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        
        {/* Services List */}
        <View style={styles.servicesSection}>
          {filteredServices.length === 0 ? (
            <EmptyState
              iconName="briefcase-outline"
              title={searchQuery.length > 0 ? 'No services match your search' : 'No services added yet'}
              message="Add your first service using the + button below"
            />
          ) : (
            filteredServices.map((service: Service) => (
              <Pressable 
                key={service.id} 
                onPress={() => handleViewService(service.id)}
                style={({ pressed }) => [
                  styles.serviceCardContainer,
                  pressed && styles.serviceCardPressed
                ]}
              >
                <Card style={styles.serviceCard}>
                  <View style={styles.cardHeader}>
                    <Typography variant="body" bold color="primary" style={styles.serviceName}>
                      {service.name}
                    </Typography>
                    <ActionMenu 
                      items={getServiceActions(service.id)}
                      style={styles.actionMenu}
                    />
                  </View>
                  
                  <Typography variant="bodySmall" color="secondary" style={styles.serviceDescription}>
                    {service.description}
                  </Typography>
                  
                  <View style={styles.cardFooter}>
                    <View style={styles.leftSection}>
                      <Typography variant="caption" color="secondary" style={styles.label}>
                        Rate
                      </Typography>
                      <Typography variant="body" bold style={styles.serviceRate}>
                        {formatRate(service)}
                      </Typography>
                    </View>
                    
                    <View style={styles.rightSection}>
                      <Typography variant="caption" color="secondary" style={styles.label}>
                        Status
                      </Typography>
                      <Typography variant="bodySmall" style={{
                        ...styles.status,
                        color: service.isActive ? colors.success : colors.text.secondary
                      }}>
                        {service.isActive ? 'Active' : 'Inactive'}
                      </Typography>
                    </View>
                  </View>
                </Card>
              </Pressable>
            ))
          )}
        </View>
      </ScrollView>
      
      {/* Using reusable FloatingActionButton component */}
      <FloatingActionButton
        onPress={handleCreateService}
        iconName="add"
        secondaryIconName="briefcase"
        color={colors.primary}
        size={65}
        bottom={90}
        right={20}
      />

      <ConfirmationDialog
        visible={deleteDialogVisible}
        onCancel={cancelDelete}
        onConfirm={confirmDelete}
        title="Delete Service"
        message={`Are you sure you want to delete "${serviceToDelete?.name}"? This action cannot be undone.`}
        confirmText="Delete"
        destructive={true}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  contentContainer: {
    paddingBottom: 32,
  },
  servicesSection: {
    paddingHorizontal: 20,
    marginTop: 20,
  },
  serviceCardContainer: {
    marginBottom: 12,
  },
  serviceCardPressed: {
    opacity: 0.7,
  },
  serviceCard: {
    padding: 16,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  serviceName: {
    flex: 1,
  },
  actionMenu: {
    padding: 4,
  },
  serviceDescription: {
    marginBottom: 16,
    lineHeight: 20,
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  leftSection: {
    flexDirection: 'column',
    alignItems: 'flex-start',
  },
  rightSection: {
    flexDirection: 'column',
    alignItems: 'flex-end',
  },
  label: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.text.secondary,
    marginBottom: 4,
    textTransform: 'uppercase',
  },
  serviceRate: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text.primary,
  },
  status: {
    fontSize: 14,
    fontWeight: '500',
  },
}); 