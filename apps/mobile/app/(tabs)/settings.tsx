import { PageHeader } from '@/components/PageHeader';
import { ThemedText } from '@/components/ThemedText';
import { colors } from '@/constants/Colors';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React from 'react';
import { Alert, Linking, Pressable, ScrollView, StyleSheet, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

// Icon type for TypeScript
type IoniconsName = React.ComponentProps<typeof Ionicons>['name'];

// Settings item interface
interface SettingsItemProps {
  icon: IoniconsName;
  title: string;
  subtitle: string;
  iconBgColor: string;
  iconColor: string;
  onPress?: () => void;
}

// Settings section header props
interface SectionHeaderProps {
  title: string;
}

// Settings item component
const SettingsItem = ({ icon, title, subtitle, iconBgColor, iconColor, onPress }: SettingsItemProps) => {
  return (
    <Pressable 
      style={styles.settingsItem} 
      onPress={onPress}
      android_ripple={{ color: colors.divider }}
    >
      <View style={styles.itemLeftSection}>
        <View style={[styles.iconContainer]}>
          <Ionicons name={icon} size={24} color={iconColor} />
        </View>
        <View style={styles.textContainer}>
          <ThemedText style={styles.itemTitle}>{title}</ThemedText>
          <ThemedText style={styles.itemSubtitle}>{subtitle}</ThemedText>
        </View>
      </View>
    </Pressable>
  );
};

// Section header component
const SectionHeader = ({ title }: SectionHeaderProps) => {
  return (
    <View style={styles.sectionHeader}>
      <ThemedText style={styles.sectionTitle}>{title}</ThemedText>
    </View>
  );
};

export default function SettingsScreen() {
  // Navigation handlers
  const handleAccountPress = () => {
    router.push('/settings/account');
  };

  const handleSubscriptionPress = () => {
    router.push('/settings/subscription');
  };

  const handleInvoiceDesignPress = () => {
    router.push('/settings/design');
  };

  const handleDefaultsPress = () => {
    router.push('/settings/defaults');
  };

  const handleHelpPress = () => {
    router.push('/settings/support');
  };

  const handleFeedbackPress = () => {
    router.push('/settings/feedback');
  };

  const handleRateAppPress = () => {
    // Open app store for rating
    const appStoreUrl = 'https://apps.apple.com/app/id123456789'; // Replace with actual app ID
    const playStoreUrl = 'https://play.google.com/store/apps/details?id=com.yourapp.id'; // Replace with actual app ID
    
    Alert.alert(
      'Rate This App',
      'Would you like to rate this app in the App Store?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Rate Now',
          onPress: () => {
            // Try to open appropriate store
            Linking.openURL(appStoreUrl).catch(() => {
              Alert.alert('Error', 'Unable to open app store');
            });
          }
        }
      ]
    );
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.primary }}>
      <StatusBar style="light" />
      
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Header with Organization Selector */}
        <PageHeader 
          title="Settings"
          subtitle="Manage your app settings"
        />
        
        {/* Account Section */}
        <SectionHeader title="ACCOUNT" />
        
        <SettingsItem 
          icon="person-outline"
          title="Account Details"
          subtitle="Manage your account details"
          iconBgColor={colors.avatarBackground.primary}
          iconColor={colors.primary}
          onPress={handleAccountPress}
        />
        
        <SettingsItem 
          icon="card-outline"
          title="Subscription"
          subtitle="Manage your subscription"
          iconBgColor={colors.avatarBackground.primary}
          iconColor={colors.primary}
          onPress={handleSubscriptionPress}
        />
        
        {/* Preferences Section */}
        <SectionHeader title="PREFERENCES" />
        
        <SettingsItem 
          icon="brush-outline"
          title="Invoice Design"
          subtitle="Customize your invoice appearance"
          iconBgColor={colors.avatarBackground.primary}
          iconColor={colors.primary}
          onPress={handleInvoiceDesignPress}
        />
        
        <SettingsItem 
          icon="settings-outline"
          title="Defaults"
          subtitle="Set default currency and tax rates"
          iconBgColor={colors.avatarBackground.primary}
          iconColor={colors.primary}
          onPress={handleDefaultsPress}
        />
        
        {/* Support Section */}
        <SectionHeader title="SUPPORT" />
        
        <SettingsItem 
          icon="help-circle-outline"
          title="Help & Support"
          subtitle="Get help and support"
          iconBgColor={colors.avatarBackground.primary}
          iconColor={colors.primary}
          onPress={handleHelpPress}
        />
        
        <SettingsItem 
          icon="megaphone-outline"
          title="Send Feedback"
          subtitle="Send feedback"
          iconBgColor={colors.avatarBackground.primary}
          iconColor={colors.primary}
          onPress={handleFeedbackPress}
        />
        
        <SettingsItem 
          icon="thumbs-up-outline"
          title="Rate App"
          subtitle="Rate the app"
          iconBgColor={colors.avatarBackground.primary}
          iconColor={colors.primary}
          onPress={handleRateAppPress}
        />
        
        {/* App Version */}
        <View style={styles.versionContainer}>
          <ThemedText style={styles.versionText}>Version 1.0.0</ThemedText>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    padding: 20,
    paddingBottom: 30,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  title: {
    fontSize: 30,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255,255,255,0.85)',
    fontWeight: '400',
  },
  sectionHeader: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    marginTop: 10,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text.secondary,
    letterSpacing: 0.5,
  },
  settingsItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.cardBackground,
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: colors.divider,
  },
  itemLeftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  textContainer: {
    flex: 1,
  },
  itemTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: 4,
  },
  itemSubtitle: {
    fontSize: 14,
    color: colors.text.secondary,
  },
  versionContainer: {
    alignItems: 'center',
    paddingVertical: 20,
    marginTop: 30,
  },
  versionText: {
    fontSize: 14,
    color: colors.text.secondary,
  },
}); 