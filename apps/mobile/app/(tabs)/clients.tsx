import { FloatingActionButton } from '@/components/FloatingActionButton';
import { PageHeader } from '@/components/PageHeader';
import type { ActionMenuItem } from '@/components/ui';
import { ActionMenu, Avatar, Card, ConfirmationDialog, EmptyState, SearchInput, Toast, Typography } from '@/components/ui';
import { colors } from '@/constants/Colors';
import { useClients } from '@repo/queries';
import { useHasActiveOrganization } from '@/stores';
import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useState } from 'react';
import { ActivityIndicator, Linking, Pressable, ScrollView, StyleSheet, View } from 'react-native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';

export default function ClientsScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const [deleteDialogVisible, setDeleteDialogVisible] = useState(false);
  const [clientToDelete, setClientToDelete] = useState<any>(null);
  const [toastVisible, setToastVisible] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastType, setToastType] = useState<'success' | 'error' | 'info'>('success');
  const insets = useSafeAreaInsets();
  
  // Clean organization guards
  const hasActiveOrganization = useHasActiveOrganization();
  
  // Use clean service hooks - no parameters, computed selectors
  const { clients = [], loading, error } = useClients();

  // Filter clients based on search query
  const filteredClients = clients.filter(client => 
    client.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (client.contact?.email && client.contact.email.toLowerCase().includes(searchQuery.toLowerCase())) ||
    (client.company && client.company.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  // Get client display name
  const getClientDisplayName = (client: any) => {
    return client.displayName || client.name || 'Unknown Client';
  };

  // Format join date
  const formatJoinDate = (client: any) => {
    if (!client.createdAt) return 'Recently';
    try {
      const date = typeof client.createdAt === 'string' ? new Date(client.createdAt) : client.createdAt;
      return date.toLocaleDateString('en-US', {
        month: 'short',
        year: 'numeric'
      });
    } catch {
      return 'Recently';
    }
  };

  const handleCreateClient = () => {
    router.push('/create-client');
  };

  const handleViewClient = (clientId: string) => {
    router.push(`/client-detail?clientId=${clientId}`);
  };

  const handleEditClient = (clientId: string) => {
    router.push(`/edit-client?clientId=${clientId}`);
  };

  const handleDeleteClient = (clientId: string) => {
    const client = clients.find(c => c.id === clientId);
    if (!client) return;

    setClientToDelete(client);
    setDeleteDialogVisible(true);
  };

  const confirmDeleteClient = async () => {
    if (!clientToDelete) return;

    try {
      // TODO: Implement actual delete functionality when delete hook is available
      console.log('Delete client:', clientToDelete.id);
      setDeleteDialogVisible(false);
      setClientToDelete(null);
      setToastMessage('Client deletion functionality will be implemented soon.');
      setToastType('info');
      setToastVisible(true);
    } catch (error: any) {
      setDeleteDialogVisible(false);
      setClientToDelete(null);
      setToastMessage('Failed to delete client. Please try again.');
      setToastType('error');
      setToastVisible(true);
      console.error('Delete client error:', error);
    }
  };

  const cancelDeleteClient = () => {
    setDeleteDialogVisible(false);
    setClientToDelete(null);
  };

  const handleCallClient = async (phone: string) => {
    try {
      const phoneUrl = `tel:${phone}`;
      const supported = await Linking.canOpenURL(phoneUrl);
      
      if (supported) {
        await Linking.openURL(phoneUrl);
      } else {
        setToastMessage('Phone calling is not supported on this device');
        setToastType('error');
        setToastVisible(true);
      }
    } catch (error) {
      setToastMessage('Failed to initiate phone call');
      setToastType('error');
      setToastVisible(true);
      console.error('Phone call error:', error);
    }
  };

  const handleEmailClient = async (email: string) => {
    try {
      const emailUrl = `mailto:${email}`;
      const supported = await Linking.canOpenURL(emailUrl);
      
      if (supported) {
        await Linking.openURL(emailUrl);
      } else {
        setToastMessage('Email is not supported on this device');
        setToastType('error');
        setToastVisible(true);
      }
    } catch (error) {
      setToastMessage('Failed to open email client');
      setToastType('error');
      setToastVisible(true);
      console.error('Email error:', error);
    }
  };

  const getClientActions = (client: any): ActionMenuItem[] => {
    const actions: ActionMenuItem[] = [
      {
        id: 'view',
        title: 'View Details',
        icon: 'person-outline',
        onPress: () => handleViewClient(client.id),
      },
      {
        id: 'edit',
        title: 'Edit',
        icon: 'create-outline',
        onPress: () => handleEditClient(client.id),
      },
    ];

    // Add call action if phone exists
    if (client.contact?.phone) {
      actions.splice(1, 0, {
        id: 'call',
        title: 'Call',
        icon: 'call-outline',
        onPress: () => handleCallClient(client.contact.phone),
      });
    }

    // Add email action if email exists
    if (client.contact?.email) {
      actions.splice(client.contact?.phone ? 2 : 1, 0, {
        id: 'email',
        title: 'Email',
        icon: 'mail-outline',
        onPress: () => handleEmailClient(client.contact.email),
      });
    }

    // Add delete action at the end
    actions.push({
      id: 'delete',
      title: 'Delete',
      icon: 'trash-outline',
      onPress: () => handleDeleteClient(client.id),
      destructive: true,
    });

    return actions;
  };

  // Organization selection guard
  if (!hasActiveOrganization) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: colors.primary }}>
        <StatusBar style="light" />
        <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
          <Typography variant="h3" color="secondary" style={{ marginBottom: 8 }}>
            Select an Organization
          </Typography>
          <Typography variant="body" color="secondary" center>
            Please select an organization to view clients
          </Typography>
        </View>
      </SafeAreaView>
    );
  }

  // Loading state
  if (loading) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: colors.primary }}>
        <StatusBar style="light" />
        <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
          <ActivityIndicator size="large" color={colors.background} />
          <Typography variant="body" color="secondary" style={{ marginTop: 16 }}>
            Loading clients...
          </Typography>
        </View>
      </SafeAreaView>
    );
  }

  // Error state
  if (error) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: colors.primary }}>
        <StatusBar style="light" />
        <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
          <Typography variant="h3" color="error" style={{ marginBottom: 8 }}>
            Error Loading Clients
          </Typography>
          <Typography variant="body" color="secondary" center>
            {error?.message || 'Something went wrong'}
          </Typography>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.primary }}>
      <StatusBar style="light" />
      
      <ScrollView 
        style={styles.container}
        contentContainerStyle={[
          styles.contentContainer, 
          { paddingBottom: Math.max(32, insets.bottom + 70) }
        ]}
        showsVerticalScrollIndicator={false}
      >
        {/* Header with Organization Selector */}
        <PageHeader 
          title="Clients"
          subtitle="Manage your client relationships"
        />
        
        {/* Search Bar */}
        <SearchInput
          placeholder="Search clients..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        
        {/* Client List */}
        <View style={styles.clientList}>
          {filteredClients.length === 0 ? (
            <EmptyState 
              iconName="people-outline"
              title={searchQuery.length > 0 ? 'No clients match your search' : 'No clients added yet'}
              message="Add your first client using the + button below"
            />
          ) : (
            filteredClients.map(client => (
              <Pressable
                key={client.id}
                onPress={() => handleViewClient(client.id)}
                style={({ pressed }) => [
                  styles.clientCardContainer,
                  pressed && styles.clientCardPressed
                ]}
              >
                <Card style={styles.clientCard}>
                  <View style={styles.cardHeader}>
                    <View style={styles.clientNameSection}>
                      <Avatar
                        name={client.name}
                        photo={client.photo}
                        size={48}
                      />
                      <Typography variant="body" bold color="primary" style={styles.clientName}>
                        {getClientDisplayName(client)}
                      </Typography>
                    </View>
                    <ActionMenu 
                      items={getClientActions(client)}
                      style={styles.actionMenu}
                    />
                  </View>
                  
                  <View style={styles.clientContent}>
                    {client.contact?.email && (
                      <Typography variant="bodySmall" color="secondary" style={styles.clientEmail}>
                        {client.contact.email}
                      </Typography>
                    )}
                    {client.company && (
                      <Typography variant="bodySmall" color="secondary" style={styles.clientCompany}>
                        {client.company}
                      </Typography>
                    )}
                  </View>
                  
                  <View style={styles.cardFooter}>
                    <View style={styles.leftSection}>
                      <Typography variant="caption" color="secondary" style={styles.label}>
                        Contact
                      </Typography>
                      <Typography variant="bodySmall" style={styles.contactInfo}>
                        {client.contact?.phone || 'No phone'}
                      </Typography>
                    </View>
                    
                    <View style={styles.rightSection}>
                      <Typography variant="caption" color="secondary" style={styles.label}>
                        Added
                      </Typography>
                      <Typography variant="bodySmall" style={styles.status}>
                        {formatJoinDate(client)}
                      </Typography>
                    </View>
                  </View>
                </Card>
              </Pressable>
            ))
          )}
        </View>
      </ScrollView>
      
      <FloatingActionButton
        onPress={handleCreateClient}
        iconName="add"
        secondaryIconName="person"
        color={colors.primary}
        size={65}
        bottom={90}
        right={20}
      />
      
      <ConfirmationDialog
        visible={deleteDialogVisible}
        title="Delete Client"
        message={`Are you sure you want to delete "${clientToDelete?.name}"? This action cannot be undone.`}
        confirmText="Delete"
        destructive={true}
        onConfirm={confirmDeleteClient}
        onCancel={cancelDeleteClient}
      />

      <Toast
        visible={toastVisible}
        message={toastMessage}
        type={toastType}
        onHide={() => setToastVisible(false)}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  contentContainer: {
    paddingBottom: 32,
  },
  clientList: {
    backgroundColor: colors.background,
    padding: 20,
    flex: 1,
  },
  clientCardContainer: {
    borderRadius: 16,
    marginBottom: 12,
  },
  clientCardPressed: {
    opacity: 0.7,
    transform: [{ scale: 0.98 }],
  },
  clientCard: {
    borderRadius: 16,
    marginBottom: 12,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  clientNameSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  clientName: {
    marginLeft: 16,
  },
  actionMenu: {
    marginLeft: 'auto',
  },
  clientContent: {
    marginTop: 8,
  },
  clientEmail: {
    marginBottom: 4,
  },
  clientCompany: {
    marginBottom: 4,
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginTop: 16,
  },
  leftSection: {
    flexDirection: 'column',
    alignItems: 'flex-start',
  },
  rightSection: {
    flexDirection: 'column',
    alignItems: 'flex-end',
  },
  label: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.text.secondary,
    marginBottom: 4,
    textTransform: 'uppercase',
  },
  contactInfo: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text.primary,
  },
  status: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.success,
  },
}); 