import { PageHeader } from '@/components/PageHeader';
import { StatusCard } from '@/components/StatusCard';
import { EmptyState, Typography } from '@/components/ui';
import { Avatar } from '@/components/ui/Avatar';
import { Card } from '@/components/ui/Card';
import { colors } from '@/constants/Colors';
import { dashboardData } from '@/constants/data';
import { useClients, useInvoices } from '@repo/queries';
import { Client, Invoice, InvoiceStatus } from '@repo/schemas';
import { useActiveOrganizationId, useHasActiveOrganization } from '@/stores';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useMemo, useCallback } from 'react';
import { ActivityIndicator, Image, Pressable, ScrollView, StyleSheet, View } from 'react-native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';

// Types
interface ActivityItem {
  id: string;
  title: string;
  date: string;
  amount: string;
  type: 'payment' | 'invoice';
  vendor: string;
  avatar: string | null;
}

interface DashboardStats {
  invoiceStats: {
    pending: { count: number; amount: string };
    paid: { count: number; amount: string };
    overdue: { count: number; amount: string };
  };
  summary: {
    revenue: { value: string; percentChange: number };
    totalInvoices: { value: string; percentChange: number };
  };
  recentActivity: ActivityItem[];
}

interface UIConfig {
  activityIcons: {
    payment: {
      iconName: React.ComponentProps<typeof Ionicons>['name'];
      iconColor: string;
      avatarBg: string;
    };
    invoice: {
      iconName: React.ComponentProps<typeof Ionicons>['name'];
      iconColor: string;
      avatarBg: string;
    };
  };
}

// Constants
const UI_CONFIG: UIConfig = {
  activityIcons: {
    payment: {
      iconName: 'checkmark-circle-outline',
      iconColor: colors.success,
      avatarBg: colors.avatarBackground.success,
    },
    invoice: {
      iconName: 'document-text-outline',
      iconColor: colors.primary,
      avatarBg: colors.avatarBackground.primary,
    },
  },
} as const;

// Helper function to format currency
const formatCurrency = (amount: number): string => {
  return `$${amount.toFixed(2)}`;
};

// Helper function to format summary values - display dash if zero
const formatSummaryValue = (value: string): string => {
  return value === '$0' || value === '0' ? '—' : value;
};

// Main Dashboard Component
const DashboardScreen: React.FC = () => {
  console.log('DashboardScreen: Component starting to render');
  
  const insets = useSafeAreaInsets();
  console.log('DashboardScreen: useSafeAreaInsets successful');
  
  // Organization state
  const hasActiveOrganization = useHasActiveOrganization();
  console.log('DashboardScreen: useHasActiveOrganization successful', hasActiveOrganization);
  
  const organizationId = useActiveOrganizationId();
  console.log('DashboardScreen: useActiveOrganizationId successful', organizationId);
  
  // Data fetching
  const { invoices = [], loading: invoicesLoading, error: invoicesError } = useInvoices();
  const { clients = [], loading: clientsLoading, error: clientsError } = useClients();
  
  // Overall loading and error state
  const loading = invoicesLoading || clientsLoading;
  const error = invoicesError || clientsError;
  
  // Get user data from static source (TODO: Replace with real user data from shared store)
  const { user } = dashboardData;
  
  // Memoized client lookup function
  const getClientDisplayName = useCallback((clientId: string, fallbackName: string): string => {
    const client = clients.find((c: Client) => c.id === clientId);
    return client?.displayName || client?.name || fallbackName;
  }, [clients]);
  
  // Compute dashboard statistics from real data
  const dashboardStats: DashboardStats = useMemo(() => {
    const pending = invoices.filter((inv: Invoice) => 
      inv.status === 'draft' || inv.status === 'pending'
    );
    const paid = invoices.filter((inv: Invoice) => inv.status === 'paid');
    const overdue = invoices.filter((inv: Invoice) => inv.status === 'overdue');
    
    // Calculate amounts
    const totalRevenue = paid.reduce((sum, inv) => sum + (inv.totals?.total || 0), 0);
    const pendingAmount = pending.reduce((sum, inv) => sum + (inv.totals?.total || 0), 0);
    const overdueAmount = overdue.reduce((sum, inv) => sum + (inv.totals?.total || 0), 0);
    
    // Generate recent activity from invoice data
    const recentActivity: ActivityItem[] = [];
    
    // Add recent payments (paid invoices)
    const recentPaidInvoices = paid
      .filter(invoice => invoice.updatedAt)
      .sort((a, b) => new Date(b.updatedAt!).getTime() - new Date(a.updatedAt!).getTime())
      .slice(0, 3);
    
    recentPaidInvoices.forEach((invoice) => {
      recentActivity.push({
        id: `payment-${invoice.id}`,
        title: `${getClientDisplayName(invoice.clientId, invoice.clientName)} paid invoice ${invoice.invoiceNumber}`,
        date: new Date(invoice.updatedAt!).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
        amount: formatCurrency(invoice.totals?.total || 0),
        type: 'payment',
        vendor: getClientDisplayName(invoice.clientId, invoice.clientName),
        avatar: null,
      });
    });
    
    // Add recent invoice creation (recent pending/draft invoices)
    const recentNewInvoices = [...pending, ...overdue]
      .filter(invoice => invoice.createdAt)
      .sort((a, b) => new Date(b.createdAt!).getTime() - new Date(a.createdAt!).getTime())
      .slice(0, 3);
    
    recentNewInvoices.forEach((invoice) => {
      const statusText = invoice.status === 'draft' ? 'created' : 'sent to';
      recentActivity.push({
        id: `invoice-${invoice.id}`,
        title: `Invoice ${invoice.invoiceNumber} ${statusText} ${getClientDisplayName(invoice.clientId, invoice.clientName)}`,
        date: new Date(invoice.createdAt!).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
        amount: formatCurrency(invoice.totals?.total || 0),
        type: 'invoice',
        vendor: getClientDisplayName(invoice.clientId, invoice.clientName),
        avatar: null,
      });
    });
    
    // Sort all activities by date (most recent first) and limit to 6 items
    const sortedActivity = recentActivity
      .sort((a, b) => {
        // Parse dates for sorting
        const dateA = new Date(a.date + ', 2024');
        const dateB = new Date(b.date + ', 2024');
        return dateB.getTime() - dateA.getTime();
      })
      .slice(0, 6);
    
    return {
      invoiceStats: {
        pending: { 
          count: pending.length, 
          amount: pending.length > 0 ? formatCurrency(pendingAmount) : '$0'
        },
        paid: { 
          count: paid.length, 
          amount: paid.length > 0 ? formatCurrency(totalRevenue) : '$0'
        },
        overdue: { 
          count: overdue.length, 
          amount: overdue.length > 0 ? formatCurrency(overdueAmount) : '$0'
        }
      },
      summary: {
        revenue: { 
          value: totalRevenue > 0 ? formatCurrency(totalRevenue) : '$0', 
          percentChange: 15 // TODO: Calculate from previous period data
        },
        totalInvoices: { 
          value: invoices.length.toString(), 
          percentChange: 8 // TODO: Calculate from previous period data
        }
      },
      recentActivity: sortedActivity
    };
  }, [invoices, clients, getClientDisplayName]);
  
  // Data availability checks
  const hasRevenueData = dashboardStats.summary.revenue.value !== '$0';
  const hasInvoiceData = dashboardStats.summary.totalInvoices.value !== '0';
  const hasActivityData = dashboardStats.recentActivity.length > 0;
  
  // Handle activity item press - navigate to invoice detail
  const handleActivityPress = useCallback((activity: ActivityItem) => {
    // Extract invoice ID from activity ID
    const invoiceId = activity.id.replace(/^(payment-|invoice-)/, '');
    
    if (invoiceId) {
      router.push(`/invoice-detail?invoiceId=${invoiceId}`);
    }
  }, []);

  console.log("Dashboard screen: Organization changed to", organizationId);

  // Organization selection guard
  if (!hasActiveOrganization) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: colors.primary }}>
        <StatusBar style="light" />
        <View style={[styles.container, styles.centerContent]}>
          <Typography variant="h3" color="secondary" style={{ marginBottom: 8 }}>
            Select an Organization
          </Typography>
          <Typography variant="body" color="secondary" center>
            Please select an organization to view dashboard
          </Typography>
        </View>
      </SafeAreaView>
    );
  }

  // Loading state
  if (loading) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: colors.primary }}>
        <StatusBar style="light" />
        <View style={[styles.container, styles.centerContent]}>
          <ActivityIndicator size="large" color={colors.background} />
          <Typography variant="body" color="secondary" style={{ marginTop: 16 }}>
            Loading dashboard...
          </Typography>
        </View>
      </SafeAreaView>
    );
  }

  // Error state
  if (error) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: colors.primary }}>
        <StatusBar style="light" />
        <View style={[styles.container, styles.centerContent]}>
          <Typography variant="h3" color="error" style={{ marginBottom: 8 }}>
            Error Loading Dashboard
          </Typography>
          <Typography variant="body" color="secondary" center>
            {error?.message || 'Something went wrong'}
          </Typography>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.primary }}>
      <StatusBar style="light" />
      
      <ScrollView 
        style={styles.container}
        contentContainerStyle={[
          styles.contentContainer, 
          { paddingBottom: Math.max(32, insets.bottom + 70) }
        ]}
        showsVerticalScrollIndicator={false}
      >
        {/* Header with Status Cards */}
        <PageHeader 
          title="InvoiceGo" 
          subtitle={`Welcome back, ${user.name}`}
          paddingTop={20}
          hideOrganizationSelector={false}
        >
          <View style={styles.statusCardsContainer}>
            <StatusCard 
              title="Pending" 
              amount={dashboardStats.invoiceStats.pending.count === 0 ? '—' : dashboardStats.invoiceStats.pending.amount} 
              bgColor={colors.primary} 
            />
            <StatusCard 
              title="Paid" 
              amount={dashboardStats.invoiceStats.paid.count === 0 ? '—' : dashboardStats.invoiceStats.paid.amount} 
              bgColor={colors.primary} 
            />
            <StatusCard 
              title="Overdue" 
              amount={dashboardStats.invoiceStats.overdue.count === 0 ? '—' : dashboardStats.invoiceStats.overdue.amount} 
              bgColor={colors.primary} 
            />
          </View>
        </PageHeader>
        
        {/* Summary Card */}
        <View style={styles.summaryCardWrapper}>
          <Card style={styles.summaryCard} noBorder>
            <View style={styles.summaryColumn}>
              <Typography variant="label" color="secondary">Total Revenue</Typography>
              <Typography variant="h2">{formatSummaryValue(dashboardStats.summary.revenue.value)}</Typography>
              {hasRevenueData && (
                <View style={styles.percentageRow}>
                  <Typography style={styles.upArrow} color="success">↑</Typography>
                  <Typography color="success" style={styles.percentageText}>
                    +{dashboardStats.summary.revenue.percentChange}%
                  </Typography>
                </View>
              )}
            </View>
            <View style={styles.divider} />
            <View style={styles.summaryColumn}>
              <Typography variant="label" color="secondary">Total Invoices</Typography>
              <Typography variant="h2">{formatSummaryValue(dashboardStats.summary.totalInvoices.value)}</Typography>
              {hasInvoiceData && (
                <View style={styles.percentageRow}>
                  <Typography style={styles.upArrow} color="success">↑</Typography>
                  <Typography color="success" style={styles.percentageText}>
                    +{dashboardStats.summary.totalInvoices.percentChange}%
                  </Typography>
                </View>
              )}
            </View>
          </Card>
        </View>
        
        {/* Recent Activity Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Typography variant="h3">Recent Activity</Typography>
          </View>
          
          {hasActivityData ? (
            dashboardStats.recentActivity.map((activity) => {
              const activityType = activity.type as keyof typeof UI_CONFIG.activityIcons;
              const activityIcon = UI_CONFIG.activityIcons[activityType];
              
              return (
                <ActivityItemComponent
                  key={activity.id}
                  activity={activity}
                  activityIcon={activityIcon}
                  onPress={handleActivityPress}
                />
              );
            })
          ) : (
            <EmptyState
              iconName="time-outline"
              title="Your recent activity will appear here"
              message="Receive payments and send invoices to see your activity feed"
            />
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

// Memoized Activity Item Component for better performance
interface ActivityItemProps {
  activity: ActivityItem;
  activityIcon: UIConfig['activityIcons']['payment'] | UIConfig['activityIcons']['invoice'];
  onPress: (activity: ActivityItem) => void;
}

const ActivityItemComponent: React.FC<ActivityItemProps> = React.memo(({ activity, activityIcon, onPress }) => {
  const handlePress = useCallback(() => {
    onPress(activity);
  }, [onPress, activity]);

  return (
    <Pressable 
      onPress={handlePress}
      style={({ pressed }) => [
        styles.activityPressable,
        pressed && styles.activityPressed
      ]}
    >
      <Card style={styles.activityItem}>
        <View style={styles.avatarContainer}>
          {activity.avatar ? (
            <Image source={{ uri: activity.avatar }} style={styles.avatar} />
          ) : (
            <Avatar 
              name={activity.vendor} 
              backgroundColor={activityIcon.avatarBg}
              textColor={activity.type === 'payment' ? colors.success : colors.primary}
            />
          )}
        </View>
        <View style={styles.activityContentContainer}>
          <View style={styles.titleRow}>
            <Ionicons name={activityIcon.iconName} size={16} color={activityIcon.iconColor} style={styles.activityIcon} />
            <Typography 
              variant="body" 
              bold 
              numberOfLines={2}
              style={styles.activityTitle}
            >
              {activity.title}
            </Typography>
          </View>
          <View style={styles.detailsRow}>
            <Typography variant="caption" color="secondary" style={styles.date}>
              {activity.date}
            </Typography>
            <Typography variant="caption" color="tertiary" style={styles.dot}>
              •
            </Typography>
            <Typography variant="caption" color="primary" style={styles.activityAmount}>
              {activity.amount}
            </Typography>
          </View>
        </View>
      </Card>
    </Pressable>
  );
});

ActivityItemComponent.displayName = 'ActivityItemComponent';

export default DashboardScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  contentContainer: {
    paddingBottom: 32, // This will be dynamically replaced with insets.bottom + additional space
  },
  statusCardsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
    marginBottom: 8,
  },
  summaryCardWrapper: {
    marginTop: -20,
    marginHorizontal: 20,
    marginBottom: 24,
  },
  summaryCard: {
    flexDirection: 'row',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  summaryColumn: {
    flex: 1,
    paddingHorizontal: 16,
  },
  divider: {
    width: 1,
    backgroundColor: colors.divider,
    marginVertical: 16,
  },
  percentageRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 6,
  },
  upArrow: {
    fontSize: 14,
    marginRight: 2,
  },
  percentageText: {
    fontSize: 14,
    fontWeight: '600',
  },
  section: {
    marginHorizontal: 20,
    marginBottom: 28,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  activityItem: {
    flexDirection: 'row',
    padding: 16,
  },
  avatarContainer: {
    marginRight: 14,
  },
  avatar: {
    width: 44,
    height: 44,
    borderRadius: 22,
  },
  activityContentContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 5,
  },
  activityIcon: {
    marginRight: 6,
    marginTop: 2,
  },
  activityTitle: {
    flex: 1,
    flexWrap: 'wrap',
  },
  detailsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 2,
  },
  date: {
    marginRight: 6,
  },
  dot: {
    marginHorizontal: 6,
  },
  activityAmount: {
    fontWeight: '500',
  },
  activityPressable: {
    flex: 1,
  },
  activityPressed: {
    opacity: 0.7,
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
});
