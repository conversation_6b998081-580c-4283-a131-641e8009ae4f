import { ConfirmationDialog, Toast, Typography } from '@/components/ui';
import { ActionMenu, ActionMenuItem } from '@/components/ui/ActionMenu';
import { colors } from '@/constants/Colors';
import { useDeleteService } from '@repo/queries';
import { useService, useServiceActivities, useUpdateService } from '@repo/queries';
import { useHasActiveOrganization } from '@/stores';
import { formatCurrency } from '@/stores/settingsStore';
import { Ionicons } from '@expo/vector-icons';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useState } from 'react';
import {
    ScrollView,
    StyleSheet,
    TouchableOpacity,
    View
} from 'react-native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';

type TabType = 'details' | 'activity';

export default function ServiceDetailScreen() {
  const insets = useSafeAreaInsets();
  const { serviceId } = useLocalSearchParams<{ serviceId: string }>();
  const [activeTab, setActiveTab] = useState<TabType>('details');
  const [deleteDialogVisible, setDeleteDialogVisible] = useState(false);
  const [toastVisible, setToastVisible] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastType, setToastType] = useState<'success' | 'error'>('success');

  // Clean organization guards
  const hasActiveOrganization = useHasActiveOrganization();

  // Use clean service hooks - no parameters, computed selectors
  const { item: service, loading, error } = useService(serviceId || '');
  const { data: activities = [], isLoading: activitiesLoading } = useServiceActivities(serviceId || '');
  const { updateService } = useUpdateService();
  const { deleteService } = useDeleteService();

  // Organization selection guard
  if (!hasActiveOrganization) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar style="dark" />
        <View style={styles.centerContainer}>
          <Typography variant="h3" color="secondary" style={{ marginBottom: 8 }}>
            Select an Organization
          </Typography>
          <Typography variant="body" color="secondary" center>
            Please select an organization to view service details
          </Typography>
        </View>
      </SafeAreaView>
    );
  }

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar style="dark" />
        <View style={styles.centerContainer}>
          <Typography variant="body" color="secondary">Loading service...</Typography>
        </View>
      </SafeAreaView>
    );
  }

  if (error || !service) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar style="dark" />
        <View style={styles.centerContainer}>
          <Typography variant="body" color="secondary">Service not found</Typography>
        </View>
      </SafeAreaView>
    );
  }

  // Actions
  const handleBack = () => {
    router.back();
  };

  const handleToggleActive = async () => {
    const newStatus = !service.isActive;
    try {
      await updateService({
        serviceId: service.id,
        updates: { isActive: newStatus }
      });
      setToastMessage(`Service has been ${newStatus ? 'activated' : 'deactivated'}.`);
      setToastType('success');
      setToastVisible(true);
    } catch (error: any) {
      setToastMessage('Failed to update service status. Please try again.');
      setToastType('error');
      setToastVisible(true);
      console.error('Update service error:', error);
    }
  };

  const handleDelete = async () => {
    setDeleteDialogVisible(true);
  };

  const handleEdit = () => {
    router.push(`/edit-service?serviceId=${service.id}`);
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'details':
        return (
          <View style={styles.tabContent}>
            {/* Basic Service Information */}
            <View style={styles.section}>
              <Typography variant="h4" bold style={styles.sectionTitle}>
                Service Information
              </Typography>
              <View style={styles.serviceInfoCard}>
                <Typography variant="h3" bold style={styles.serviceName}>
                  {service.name}
                </Typography>
                {service.description && (
                  <Typography variant="body" color="secondary" style={styles.serviceDescription}>
                    {service.description}
                  </Typography>
                )}
              </View>
            </View>

            {/* Pricing, Properties & Tags Combined */}
            <View style={styles.section}>
              <View style={styles.pricingCard}>
                {/* Main Pricing Display */}
                <View style={styles.pricingMainSection}>
                  <View style={styles.pricingInfo}>
                    <Typography variant="h2" color="primary" bold>
                      {formatCurrency(service.pricing.rate)}
                    </Typography>
                    <Typography variant="body" color="secondary" style={styles.pricingUnit}>
                      per {service.pricing.unit}
                    </Typography>
                  </View>
                  <View style={styles.statusIndicator}>
                    <Ionicons 
                      name={service.isActive ? "checkmark-circle" : "pause-circle"} 
                      size={20} 
                      color={service.isActive ? colors.success : colors.error} 
                    />
                    <Typography 
                      variant="bodySmall" 
                      color={service.isActive ? 'success' : 'error'}
                      bold
                      style={styles.statusText}
                    >
                      {service.isActive ? 'Active' : 'Inactive'}
                    </Typography>
                  </View>
                </View>

                {/* Divider */}
                <View style={styles.sectionDivider} />

                {/* Properties Row */}
                <View style={styles.propertiesRow}>
                  <View style={styles.propertyItem}>
                    <Typography variant="bodySmall" color="secondary" style={styles.propertyLabel}>
                      Currency
                    </Typography>
                    <Typography variant="body" color="primary" bold>
                      {service.pricing.currency.toUpperCase()}
                    </Typography>
                  </View>
                  
                  <View style={styles.propertyItemRight}>
                    <Typography variant="bodySmall" color="secondary" style={styles.propertyLabelRight}>
                      Rate Type
                    </Typography>
                    <Typography variant="body" color="primary" bold style={styles.propertyValueRight}>
                      {service.pricing.unit.charAt(0).toUpperCase() + service.pricing.unit.slice(1)}
                    </Typography>
                  </View>
                </View>


              </View>
            </View>
          </View>
        );

      case 'activity':
        return (
          <View style={styles.tabContent}>
            <View style={styles.section}>
              <Typography variant="h4" bold style={styles.sectionTitle}>
                Recent Activity
              </Typography>
              
              {activitiesLoading ? (
                <View style={styles.activityCard}>
                  <Typography variant="body" color="secondary" center>
                    Loading activity...
                  </Typography>
                </View>
              ) : activities.length === 0 ? (
              <View style={styles.activityCard}>
                <Typography variant="body" color="secondary" center style={styles.emptyActivity}>
                  No activity recorded yet
                </Typography>
                <Typography variant="bodySmall" color="secondary" center style={styles.emptyActivitySubtext}>
                  Activity will appear here when available
                </Typography>
              </View>
              ) : (
                <View style={styles.activityTimeline}>
                  {activities.map((activity, index) => {
                    const getActivityIcon = () => {
                      switch (activity.type) {
                        case 'created':
                          return 'add-circle';
                        case 'updated':
                          return 'create';
                        case 'used_in_invoice':
                          return 'document-text';
                        case 'activated':
                          return 'play-circle';
                        case 'deactivated':
                          return 'pause-circle';
                        case 'pricing_changed':
                          return 'pricetag';
                        default:
                          return 'information-circle';
                      }
                    };

                    const getActivityColor = () => {
                      switch (activity.type) {
                        case 'created':
                          return colors.success;
                        case 'updated':
                          return colors.primary;
                        case 'used_in_invoice':
                          return colors.primary;
                        case 'activated':
                          return colors.success;
                        case 'deactivated':
                          return colors.error;
                        case 'pricing_changed':
                          return colors.warning;
                        default:
                          return colors.text.secondary;
                      }
                    };

                    const formatDate = (date: Date) => {
                      return date.toLocaleDateString('en-US', {
                        month: 'short',
                        day: 'numeric',
                        year: 'numeric',
                        hour: 'numeric',
                        minute: '2-digit'
                      });
                    };

                    return (
                      <View key={activity.id} style={styles.activityItem}>
                        <View style={styles.activityIconContainer}>
                          <View style={[styles.activityIcon, { backgroundColor: getActivityColor() }]}>
                            <Ionicons 
                              name={getActivityIcon()} 
                              size={16} 
                              color={colors.background} 
                            />
                          </View>
                          {index < activities.length - 1 && <View style={styles.activityLine} />}
                        </View>
                        
                        <View style={styles.activityContent}>
                          <Typography variant="body" bold style={styles.activityMessage}>
                            {activity.description}
                          </Typography>
                          
                          {activity.metadata?.amount && (
                            <Typography variant="bodySmall" color="primary" style={styles.activityAmount}>
                              {formatCurrency(activity.metadata.amount)}
                            </Typography>
                          )}
                          
                          <Typography variant="caption" color="secondary" style={styles.activityDate}>
                            {formatDate(activity.createdAt)}
                          </Typography>
                        </View>
                      </View>
                    );
                  })}
                </View>
              )}
            </View>
          </View>
        );

      default:
        return null;
    }
  };

  // Action menu items
  const actionMenuItems: ActionMenuItem[] = [
    {
      id: 'toggle-active',
      title: service.isActive ? 'Pause Service' : 'Activate Service',
      icon: service.isActive ? 'pause' : 'play',
      onPress: handleToggleActive,
    },
    {
      id: 'edit',
      title: 'Edit Service',
      icon: 'create',
      onPress: handleEdit,
    },
    {
      id: 'delete',
      title: 'Delete Service',
      icon: 'trash',
      onPress: handleDelete,
      destructive: true,
    },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen options={{ headerShown: false }} />
      <StatusBar style="dark" />
      
      {/* Header */}
      <View style={[styles.header, { paddingTop: insets.top }]}>
        <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          <Ionicons name="arrow-back" size={24} color={colors.text.primary} />
        </TouchableOpacity>
        <View style={styles.headerCenter}>
          <Typography variant="body" style={styles.headerTitle}>
            Service Details
          </Typography>
        </View>
        <ActionMenu
          items={actionMenuItems}
          horizontalOffset={18}
          verticalOffset={-8}
        />
      </View>

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        <View style={styles.tabNavigation}>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'details' && styles.activeTab]}
            onPress={() => setActiveTab('details')}
          >
            <Ionicons 
              name="information-circle-outline" 
              size={18} 
              color={activeTab === 'details' ? colors.primary : colors.text.secondary}
              style={styles.tabIcon}
            />
            <Typography 
              variant="bodySmall" 
              color={activeTab === 'details' ? 'primary' : 'secondary'}
              style={styles.tabLabel}
            >
              Details
            </Typography>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.tab, activeTab === 'activity' && styles.activeTab]}
            onPress={() => setActiveTab('activity')}
          >
            <Ionicons 
              name="time-outline" 
              size={18} 
              color={activeTab === 'activity' ? colors.primary : colors.text.secondary}
              style={styles.tabIcon}
            />
            <Typography 
              variant="bodySmall" 
              color={activeTab === 'activity' ? 'primary' : 'secondary'}
              style={styles.tabLabel}
            >
              Activity
            </Typography>
          </TouchableOpacity>
        </View>
      </View>

      {/* Tab Content */}
      <ScrollView 
        style={styles.contentContainer}
        contentContainerStyle={[
          styles.scrollContent,
          { paddingBottom: insets.bottom + 20 }
        ]}
        showsVerticalScrollIndicator={false}
      >
        {renderTabContent()}
      </ScrollView>

      <ConfirmationDialog
        visible={deleteDialogVisible}
        title="Delete Service"
        message={`Are you sure you want to delete "${service.name}"? This action cannot be undone.`}
        confirmText="Delete"
        destructive={true}
        onConfirm={async () => {
          setDeleteDialogVisible(false);
          try {
            await deleteService(service.id);
            setToastMessage('Service has been deleted.');
            setToastType('success');
            setToastVisible(true);
            // Wait a moment before navigating back to show the toast
            setTimeout(() => router.back(), 1500);
          } catch (error: any) {
            setToastMessage('Failed to delete service. Please try again.');
            setToastType('error');
            setToastVisible(true);
            console.error('Delete service error:', error);
          }
        }}
        onCancel={() => setDeleteDialogVisible(false)}
      />

      <Toast
        visible={toastVisible}
        message={toastMessage}
        type={toastType}
        onHide={() => setToastVisible(false)}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 12,
    backgroundColor: colors.cardBackground,
  },
  backButton: {
    padding: 4,
    marginRight: 16,
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text.primary,
  },
  
  // Tab Navigation
  tabContainer: {
    backgroundColor: colors.cardBackground,
    borderBottomWidth: 1,
    borderBottomColor: colors.divider,
  },
  tabNavigation: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 8,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 12,
    borderRadius: 10,
    backgroundColor: 'transparent',
  },
  activeTab: {
    backgroundColor: colors.primaryVeryLight,
  },
  tabIcon: {
    marginRight: 6,
  },
  tabLabel: {
    fontSize: 13,
    fontWeight: '500',
  },

  // Content
  contentContainer: {
    flex: 1,
  },
  scrollContent: {
    padding: 12,
  },
  tabContent: {
    flex: 1,
  },
  
  // Sections
  section: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: colors.text.primary,
    marginBottom: 8,
  },
  
  // Service Card
  serviceCard: {
    backgroundColor: colors.cardBackground,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: colors.divider,
  },
  serviceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },

  // Pricing Card
  pricingCard: {
    backgroundColor: colors.cardBackground,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: colors.divider,
  },
  pricingMainSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  pricingInfo: {
    flex: 1,
  },
  pricingUnit: {
    marginTop: 4,
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    marginLeft: 6,
  },
  sectionDivider: {
    height: 1,
    backgroundColor: colors.divider,
    marginVertical: 12,
  },
  propertiesRow: {
    flexDirection: 'row',
    gap: 24,
  },
  propertyItem: {
    flex: 1,
  },
  propertyItemRight: {
    flex: 1,
    alignItems: 'flex-end',
  },
  propertyLabel: {
    marginBottom: 4,
    fontSize: 12,
  },
  propertyLabelRight: {
    marginBottom: 4,
    fontSize: 12,
    textAlign: 'right',
  },
  propertyValueRight: {
    textAlign: 'right',
  },


  // Activity
  activityCard: {
    backgroundColor: colors.cardBackground,
    borderRadius: 12,
    padding: 24,
    borderWidth: 1,
    borderColor: colors.divider,
    alignItems: 'center',
  },
  emptyActivity: {
    fontSize: 16,
    marginBottom: 8,
  },
  emptyActivitySubtext: {
    fontSize: 14,
  },

  // Service Info Card
  serviceInfoCard: {
    backgroundColor: colors.cardBackground,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: colors.divider,
  },
  serviceName: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.text.primary,
    marginBottom: 6,
  },
  serviceDescription: {
    fontSize: 14,
    color: colors.text.secondary,
    lineHeight: 20,
  },

  // Activity Timeline
  activityTimeline: {
    flex: 1,
  },
  activityItem: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  activityIconContainer: {
    alignItems: 'center',
    marginRight: 12,
    width: 32,
  },
  activityIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  activityLine: {
    width: 2,
    flex: 1,
    backgroundColor: colors.divider,
    position: 'absolute',
    top: 40,
    left: 15,
    bottom: -16,
  },
  activityContent: {
    flex: 1,
    paddingTop: 4,
  },
  activityMessage: {
    fontSize: 15,
    fontWeight: '500',
    color: colors.text.primary,
    marginBottom: 4,
  },
  activityAmount: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.primary,
    marginBottom: 4,
  },
  activityDate: {
    fontSize: 13,
    color: colors.text.secondary,
  },
}); 