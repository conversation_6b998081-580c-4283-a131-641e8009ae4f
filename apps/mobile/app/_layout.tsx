import { queryClient } from '@/core/query-client';
import { initializeAppData } from '@/core/data-initialization';
import { DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { QueryClientProvider } from '@tanstack/react-query';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useEffect, useState } from 'react';
import { KeyboardProvider } from 'react-native-keyboard-controller';
import 'react-native-reanimated';
import { SafeAreaProvider } from 'react-native-safe-area-context';

export default function RootLayout() {
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  const [dataInitialized, setDataInitialized] = useState(false);

  // Initialize app data when component mounts
  useEffect(() => {
    const initData = async () => {
      const result = await initializeAppData();
      if (result.success) {
        console.log(`✅ Data initialized: ${result.organizationsCount} organizations loaded`);
      } else {
        console.error('❌ Data initialization failed:', result.error);
      }
      setDataInitialized(true);
    };

    initData();
  }, []);

  if (!loaded || !dataInitialized) {
    // Wait for both fonts and data to load
    return null;
  }

  return (
    <QueryClientProvider client={queryClient}>
      <SafeAreaProvider>
        <KeyboardProvider>
          <ThemeProvider value={DefaultTheme}>
            <Stack screenOptions={{ headerShown: false }}>
              <Stack.Screen name="(tabs)" />
              <Stack.Screen name="+not-found" />
            </Stack>
            <StatusBar style="auto" />
          </ThemeProvider>
        </KeyboardProvider>
      </SafeAreaProvider>
    </QueryClientProvider>
  );
}
