import { Button, Card, Dropdown, Input, Typography } from '@/components/ui';
import { colors } from '@/constants/Colors';
import { CreateFeedbackInput } from '@repo/schemas';
import { useFeedbackCategories, useSubmitFeedback, useUserFeedback } from '@repo/queries';
import { Ionicons } from '@expo/vector-icons';
import { Stack } from 'expo-router';
import React, { useState } from 'react';
import { ActivityIndicator, FlatList, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

export default function FeedbackScreen() {
  const [activeTab, setActiveTab] = useState<'submit' | 'history'>('submit');
  
  // Form state
  const [feedbackForm, setFeedbackForm] = useState({
    category: 'general' as CreateFeedbackInput['category'],
    subject: '',
    message: '',
    priority: 'medium' as CreateFeedbackInput['priority'],
    attachments: [] as CreateFeedbackInput['attachments'],
  });

  // Hooks
  const { categories, loading: categoriesLoading } = useFeedbackCategories();
  const { submitFeedback, loading: submitting } = useSubmitFeedback();
  const { feedback: userFeedback, loading: feedbackLoading } = useUserFeedback();

  // Form submission
  const handleSubmit = async () => {
    if (!feedbackForm.subject || !feedbackForm.message) {
      return;
    }

    try {
      await submitFeedback({
        category: feedbackForm.category,
        subject: feedbackForm.subject,
        message: feedbackForm.message,
        priority: feedbackForm.priority,
        tags: [],
        attachments: feedbackForm.attachments,
      });
      
      // Reset form on success
      setFeedbackForm({
        category: 'general',
        subject: '',
        message: '',
        priority: 'medium',
        attachments: [],
      });
      
      // Switch to history tab to show submission
      setActiveTab('history');
    } catch (error) {
      console.error('Failed to submit feedback:', error);
    }
  };

  const renderTabButtons = () => (
    <View style={styles.tabContainer}>
      <TouchableOpacity
        style={[styles.tab, activeTab === 'submit' && styles.activeTab]}
        onPress={() => setActiveTab('submit')}
      >
        <Ionicons 
          name="create-outline" 
          size={20} 
          color={activeTab === 'submit' ? colors.primary : colors.text.secondary} 
        />
        <Typography 
          variant="body" 
          color={activeTab === 'submit' ? 'primary' : 'secondary'}
          style={styles.tabText}
        >
          Send Feedback
        </Typography>
      </TouchableOpacity>
      
      <TouchableOpacity
        style={[styles.tab, activeTab === 'history' && styles.activeTab]}
        onPress={() => setActiveTab('history')}
      >
        <Ionicons 
          name="time-outline" 
          size={20} 
          color={activeTab === 'history' ? colors.primary : colors.text.secondary} 
        />
        <Typography 
          variant="body" 
          color={activeTab === 'history' ? 'primary' : 'secondary'}
          style={styles.tabText}
        >
          My Feedback
        </Typography>
      </TouchableOpacity>
    </View>
  );

  const renderSubmitForm = () => {
    const categoryOptions = categories?.map(cat => ({
      key: cat.id,
      title: cat.name,
      description: cat.description,
      icon: 'folder-outline' as const,
    })) || [];

    const priorityOptions = [
      { key: 'low', title: 'Low', description: 'Minor issue or suggestion', icon: 'flag-outline' as const },
      { key: 'medium', title: 'Medium', description: 'Important feedback', icon: 'flag' as const },
      { key: 'high', title: 'High', description: 'Critical issue or urgent request', icon: 'warning' as const },
    ];

    return (
      <ScrollView showsVerticalScrollIndicator={false}>
        <Card style={styles.formCard}>
          <Typography variant="h4" bold style={styles.formTitle}>
            Share Your Feedback
          </Typography>
          <Typography variant="body" color="secondary" style={styles.formSubtitle}>
            Help us improve by sharing your thoughts, reporting bugs, or suggesting new features.
          </Typography>
          
          <View style={styles.formSection}>
            <Dropdown
              label="Category"
              options={categoryOptions}
              value={feedbackForm.category}
                             onSelect={(category) => setFeedbackForm(prev => ({ ...prev, category: category as CreateFeedbackInput['category'] }))}
              placeholder="Select feedback category..."
              showDescriptions={true}
            />
          </View>
          
          <View style={styles.formSection}>
            <Dropdown
              label="Priority"
              options={priorityOptions}
              value={feedbackForm.priority}
                             onSelect={(priority) => setFeedbackForm(prev => ({ ...prev, priority: priority as CreateFeedbackInput['priority'] }))}
              placeholder="Select priority level..."
              showDescriptions={true}
              variant="compact"
            />
          </View>
          
          <Input
            label="Subject"
            value={feedbackForm.subject}
            onChangeText={(subject) => setFeedbackForm(prev => ({ ...prev, subject }))}
            placeholder="Brief description of your feedback"
          />
          
          <Input
            label="Message"
            value={feedbackForm.message}
            onChangeText={(message) => setFeedbackForm(prev => ({ ...prev, message }))}
            placeholder="Please provide detailed feedback, including steps to reproduce if reporting a bug..."
            multiline
            numberOfLines={6}
            style={styles.messageInput}
          />
          
          <View style={styles.submitSection}>
            <Button
              title="Submit Feedback"
              onPress={handleSubmit}
              disabled={submitting || !feedbackForm.subject || !feedbackForm.message}
              style={styles.submitButton}
            />
            
            <View style={styles.submitNote}>
              <Ionicons name="information-circle-outline" size={16} color={colors.text.secondary} />
              <Typography variant="caption" color="secondary" style={styles.noteText}>
                We typically respond within 24-48 hours. Thank you for helping us improve!
              </Typography>
            </View>
          </View>
        </Card>
      </ScrollView>
    );
  };

  const renderFeedbackHistory = () => {
    if (feedbackLoading) {
      return <ActivityIndicator size="small" color={colors.primary} style={styles.loader} />;
    }

    if (!userFeedback || userFeedback.length === 0) {
      return (
        <View style={styles.emptyState}>
          <Ionicons name="chatbubbles-outline" size={48} color={colors.text.tertiary} />
          <Typography variant="body" color="secondary" style={styles.emptyTitle}>
            No feedback submitted yet
          </Typography>
          <Typography variant="bodySmall" color="tertiary" style={styles.emptySubtitle}>
            Switch to the "Send Feedback" tab to share your thoughts with us.
          </Typography>
        </View>
      );
    }

    return (
      <FlatList
        data={userFeedback}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => {
          const getStatusColor = () => {
            switch (item.status) {
              case 'open': return colors.warning;
              case 'in_progress': return colors.primary;
              case 'resolved': return colors.success;
              default: return colors.text.secondary;
            }
          };

          const getStatusIcon = () => {
            switch (item.status) {
              case 'open': return 'time-outline';
              case 'in_progress': return 'sync-outline';
              case 'resolved': return 'checkmark-circle-outline';
              default: return 'help-circle-outline';
            }
          };

          const getPriorityColor = () => {
            switch (item.priority) {
              case 'high': return colors.error;
              case 'medium': return colors.warning;
              case 'low': return colors.success;
              default: return colors.text.secondary;
            }
          };

          return (
            <Card style={styles.feedbackCard}>
              <View style={styles.feedbackHeader}>
                <View style={styles.feedbackTitleRow}>
                  <Typography variant="body" bold style={styles.feedbackSubject}>
                    {item.subject}
                  </Typography>
                  <View style={[styles.priorityBadge, { backgroundColor: getPriorityColor() }]}>
                    <Typography variant="caption" color="white">
                      {item.priority.toUpperCase()}
                    </Typography>
                  </View>
                </View>
                
                <View style={styles.feedbackMeta}>
                  <View style={styles.statusContainer}>
                    <Ionicons 
                      name={getStatusIcon()} 
                      size={16} 
                      color={getStatusColor()} 
                    />
                                         <Typography 
                       variant="bodySmall" 
                       style={[styles.statusText, { color: getStatusColor() }] as any}
                     >
                      {item.status.replace('_', ' ').toUpperCase()}
                    </Typography>
                  </View>
                  
                  <Typography variant="caption" color="tertiary">
                    {new Date(item.createdAt).toLocaleDateString()}
                  </Typography>
                </View>
              </View>
              
              <Typography variant="bodySmall" color="secondary" style={styles.feedbackMessage}>
                {item.message.length > 150 
                  ? item.message.substring(0, 150) + '...' 
                  : item.message
                }
              </Typography>
              
              <View style={styles.feedbackFooter}>
                <View style={styles.categoryTag}>
                  <Typography variant="caption" color="primary">
                    {item.category}
                  </Typography>
                </View>
                
                {item.followUpRequired && (
                  <View style={styles.followUpIndicator}>
                    <Ionicons name="mail-outline" size={14} color={colors.primary} />
                    <Typography variant="caption" color="primary">
                      Follow-up required
                    </Typography>
                  </View>
                )}
              </View>
            </Card>
          );
        }}
        showsVerticalScrollIndicator={false}
      />
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen 
        options={{
          title: 'Send Feedback',
          headerShown: true,
          headerStyle: { backgroundColor: colors.primary },
          headerTintColor: 'white',
          headerTitleStyle: { color: 'white', fontWeight: '600' },
        }}
      />
      
      <View style={styles.content}>
        {renderTabButtons()}
        
        <View style={styles.contentArea}>
          {activeTab === 'submit' && renderSubmitForm()}
          {activeTab === 'history' && renderFeedbackHistory()}
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 4,
    marginBottom: 16,
    marginTop: 8,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    borderRadius: 8,
  },
  activeTab: {
    backgroundColor: colors.primary + '10',
  },
  tabText: {
    marginLeft: 6,
    fontSize: 14,
    fontWeight: '500',
  },
  contentArea: {
    flex: 1,
  },
  loader: {
    marginTop: 32,
  },
  
  // Submit form styles
  formCard: {
    padding: 20,
  },
  formTitle: {
    marginBottom: 8,
  },
  formSubtitle: {
    marginBottom: 24,
    lineHeight: 20,
  },
  formSection: {
    marginBottom: 16,
  },
  messageInput: {
    height: 120,
    textAlignVertical: 'top',
  },
  submitSection: {
    marginTop: 8,
  },
  submitButton: {
    marginBottom: 12,
  },
  submitNote: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 12,
    backgroundColor: colors.primary + '05',
    borderRadius: 8,
  },
  noteText: {
    marginLeft: 8,
    flex: 1,
    lineHeight: 18,
  },
  
  // Empty state styles
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 32,
  },
  emptyTitle: {
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtitle: {
    textAlign: 'center',
    lineHeight: 20,
  },
  
  // Feedback history styles
  feedbackCard: {
    marginBottom: 12,
  },
  feedbackHeader: {
    marginBottom: 12,
  },
  feedbackTitleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  feedbackSubject: {
    flex: 1,
    marginRight: 8,
  },
  priorityBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  feedbackMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    marginLeft: 4,
    fontWeight: '500',
  },
  feedbackMessage: {
    lineHeight: 20,
    marginBottom: 12,
  },
  feedbackFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  categoryTag: {
    backgroundColor: colors.primary + '10',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
  },
  followUpIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
}); 