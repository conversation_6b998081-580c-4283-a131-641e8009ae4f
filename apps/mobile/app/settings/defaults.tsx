import React, { useEffect, useState } from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

// UI Components
import {
  Button,
  EmptyState,
  FormSection,
  Input,
  KeyboardAwareView,
  Switch,
  Toast,
  Typography
} from '@/components/ui';
import { colors } from '@/constants/Colors';
import { Ionicons } from '@expo/vector-icons';
import { router, Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';

// Stores and Hooks
import { useActiveOrganizationId, useHasActiveOrganization } from '@/stores';

// Types
interface BusinessDefaults {
  taxRate: number;
  taxInclusive: boolean;
  paymentTerms: number;
  allowPartialPayments: boolean;
  invoicePrefix: string;
  invoiceNumber: number;
  resetAnnually: boolean;
  emailCopy: boolean;
  emailReminders: boolean;
  brandingEnabled: boolean;
  brandingText: string;
}

export default function DefaultsScreen() {
  // Organization context
  const hasActiveOrganization = useHasActiveOrganization();
  const organizationId = useActiveOrganizationId();

  // Mock loading and error state
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Toast state
  const [toastVisible, setToastVisible] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastType, setToastType] = useState<'success' | 'error'>('success');

  // Form state with mock defaults
  const [formData, setFormData] = useState<BusinessDefaults>({
    taxRate: 10,
    taxInclusive: false,
    paymentTerms: 30,
    allowPartialPayments: true,
    invoicePrefix: 'INV-{ORG_ID}',
    invoiceNumber: 1001,
    resetAnnually: false,
    emailCopy: true,
    emailReminders: true,
    brandingEnabled: false,
    brandingText: '',
  });

  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Organization selection guard
  if (!hasActiveOrganization) {
    return (
      <View style={styles.container}>
        <Stack.Screen options={{ headerShown: false }} />
        <StatusBar style="dark" />
        <EmptyState
          iconName="business-outline"
          title="Select an Organization"
          message="Please select an organization to access default settings"
        />
      </View>
    );
  }

  // Mock loading effect
  useEffect(() => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  }, [organizationId]);

  const showToast = (message: string, type: 'success' | 'error' = 'success') => {
    setToastMessage(message);
    setToastType(type);
    setToastVisible(true);
  };

  // Update form field
  const updateField = (field: keyof BusinessDefaults, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setHasUnsavedChanges(true);
  };

  // Save changes
  const handleSave = async () => {
    try {
      setUpdating(true);
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setHasUnsavedChanges(false);
      showToast('Default settings updated successfully!');
    } catch (error) {
      showToast('Failed to update settings. Please try again.', 'error');
    } finally {
      setUpdating(false);
    }
  };

  // Reset form
  const handleReset = () => {
    setFormData({
      taxRate: 10,
      taxInclusive: false,
      paymentTerms: 30,
      allowPartialPayments: true,
      invoicePrefix: 'INV',
      invoiceNumber: 1001,
      resetAnnually: false,
      emailCopy: true,
      emailReminders: true,
      brandingEnabled: false,
      brandingText: '',
    });
    setHasUnsavedChanges(false);
    showToast('Settings reset to defaults');
  };

  const handleBack = () => {
    router.back();
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <Stack.Screen options={{ headerShown: false }} />
        <StatusBar style="dark" />
        <EmptyState
          iconName="refresh"
          title="Loading Defaults"
          message="Please wait while we load your default settings..."
        />
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.container}>
        <Stack.Screen options={{ headerShown: false }} />
        <StatusBar style="dark" />
        <EmptyState
          iconName="alert-circle-outline"
          title="Error Loading Defaults"
          message="Unable to load default settings. Please try again."
        />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Stack.Screen options={{ headerShown: false }} />
      <StatusBar style="dark" />
      
      {/* Header */}
      <SafeAreaView edges={['top']}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={handleBack}
          >
            <Ionicons name="arrow-back" size={24} color={colors.text.primary} />
          </TouchableOpacity>
          <Typography variant="body" style={styles.headerTitle}>
            Default Settings
          </Typography>
          <View style={styles.headerRightPlaceholder} />
        </View>
      </SafeAreaView>

      {/* Content */}
      <KeyboardAwareView style={styles.keyboardView} contentContainerStyle={styles.contentContainer}>
        <View style={styles.formContainer}>
          <Typography variant="h3" style={styles.title}>
            Business Defaults
          </Typography>
          <Typography variant="body" color="secondary" style={styles.subtitle}>
            Configure default settings for your invoices and business operations
          </Typography>

          {/* General Settings */}
          <FormSection title="General Settings" noBorder>
            <Input
              label="Organization Name"
              value="Sample Organization"
              onChangeText={() => { }}
              placeholder="Enter organization name"
            />

            <Input
              label="Business Email"
              value="<EMAIL>"
              onChangeText={() => { }}
              placeholder="Enter business email"
              keyboardType="email-address"
            />

            <Input
              label="Phone Number"
              value="+****************"
              onChangeText={() => { }}
              placeholder="Enter phone number"
              keyboardType="phone-pad"
            />
          </FormSection>

          {/* Tax Settings */}
          <FormSection title="Tax Settings" noBorder>
            <Input
              label="Default Tax Rate (%)"
              value={formData.taxRate.toString()}
              onChangeText={(value) => updateField('taxRate', parseFloat(value) || 0)}
              placeholder="10"
              keyboardType="numeric"
            />
            
            <View style={styles.switchRow}>
              <View style={styles.switchContent}>
                <Typography variant="body" style={styles.switchLabel}>Include Tax in Item Prices</Typography>
                <Typography variant="caption" color="secondary">
                  When enabled, item prices include tax
                </Typography>
              </View>
              <Switch
                value={formData.taxInclusive}
                onValueChange={(value) => updateField('taxInclusive', value)}
              />
            </View>
          </FormSection>

          {/* Payment Terms */}
          <FormSection title="Payment Terms" noBorder>
            <Input
              label="Default Payment Terms (days)"
              value={formData.paymentTerms.toString()}
              onChangeText={(value) => updateField('paymentTerms', parseInt(value) || 30)}
              placeholder="30"
              keyboardType="numeric"
            />

            <View style={styles.switchRow}>
              <View style={styles.switchContent}>
                <Typography variant="body" style={styles.switchLabel}>Allow Partial Payments</Typography>
                <Typography variant="caption" color="secondary">
                  Allow clients to make partial payments on invoices
                </Typography>
              </View>
              <Switch
                value={formData.allowPartialPayments}
                onValueChange={(value) => updateField('allowPartialPayments', value)}
              />
            </View>
          </FormSection>

          {/* Invoice Numbering */}
          <FormSection title="Invoice Numbering" noBorder>
            <Input
              label="Invoice Prefix"
              value={formData.invoicePrefix}
              onChangeText={(value) => updateField('invoicePrefix', value)}
              placeholder="INV"
            />

            <Input
              label="Next Invoice Number"
              value={formData.invoiceNumber.toString()}
              onChangeText={(value) => updateField('invoiceNumber', parseInt(value) || 1)}
              placeholder="1001"
              keyboardType="numeric"
            />
          </FormSection>

          {/* Email Settings */}
          <FormSection title="Email Settings" noBorder>
            <Input
              label="Email Template Subject"
              value="Invoice from {organization}"
              onChangeText={() => { }}
              placeholder="Invoice from {organization}"
              editable={false}
            />

            <View style={styles.switchRow}>
              <View style={styles.switchContent}>
                <Typography variant="body" style={styles.switchLabel}>Send Copy to Self</Typography>
                <Typography variant="caption" color="secondary">
                  Send a copy of invoices to your email
                </Typography>
              </View>
              <Switch
                value={formData.emailCopy}
                onValueChange={(value) => updateField('emailCopy', value)}
              />
            </View>

            <View style={styles.switchRow}>
              <View style={styles.switchContent}>
                <Typography variant="body" style={styles.switchLabel}>Auto-send Reminders</Typography>
                <Typography variant="caption" color="secondary">
                  Automatically send payment reminders
                </Typography>
              </View>
              <Switch
                value={formData.emailReminders}
                onValueChange={(value) => updateField('emailReminders', value)}
              />
            </View>
          </FormSection>

          {/* Branding */}
          <FormSection title="Branding" noBorder>
            <View style={styles.switchRow}>
              <View style={styles.switchContent}>
                <Typography variant="body" style={styles.switchLabel}>Custom Branding</Typography>
                <Typography variant="caption" color="secondary">
                  Enable custom branding on invoices
                </Typography>
              </View>
              <Switch
                value={formData.brandingEnabled}
                onValueChange={(value) => updateField('brandingEnabled', value)}
              />
            </View>

            {formData.brandingEnabled && (
              <Input
                label="Branding Text"
                value={formData.brandingText}
                onChangeText={(value) => updateField('brandingText', value)}
                placeholder="Thank you for your business!"
                multiline
                numberOfLines={3}
              />
            )}
          </FormSection>

          {/* Action Buttons */}
          {hasUnsavedChanges && (
            <View style={styles.actionContainer}>
              <Button
                title="Reset"
                onPress={handleReset}
                variant="outline"
                style={styles.actionButton}
              />
              <Button
                title={updating ? "Saving..." : "Save Changes"}
                onPress={handleSave}
                disabled={updating}
                style={styles.actionButton}
              />
            </View>
          )}
        </View>
      </KeyboardAwareView>

      {/* Toast */}
      <Toast
        visible={toastVisible}
        message={toastMessage}
        type={toastType}
        onHide={() => setToastVisible(false)}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.cardBackground,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: colors.cardBackground,
    borderBottomWidth: 1,
    borderBottomColor: colors.divider,
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text.primary,
  },
  backButton: {
    padding: 4,
    marginLeft: -4,
  },
  headerRightPlaceholder: {
    width: 24 + 8,
  },
  keyboardView: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  formContainer: {
    flex: 1,
  },
  title: {
    marginBottom: 8,
  },
  subtitle: {
    marginBottom: 24,
  },

  // Switch Components
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    marginBottom: 8,
  },
  switchContent: {
    flex: 1,
    marginRight: 16,
  },
  switchLabel: {
    fontWeight: '500',
    marginBottom: 4,
  },

  // Actions
  actionContainer: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 24,
  },
  actionButton: {
    flex: 1,
  },
}); 