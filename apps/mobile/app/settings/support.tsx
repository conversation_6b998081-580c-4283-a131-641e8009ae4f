import { Button, Card, Input, SearchInput, Typography } from '@/components/ui';
import { colors } from '@/constants/Colors';
import { useFAQs, useHelpArticles, useHelpCategories, useSubmitContactForm } from '@repo/queries';
import { Ionicons } from '@expo/vector-icons';
import { Stack, useRouter } from 'expo-router';
import React, { useState } from 'react';
import { ActivityIndicator, Alert, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

export default function SupportScreen() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<'help' | 'faq' | 'contact'>('help');
  const [selectedCategory, setSelectedCategory] = useState<string | undefined>(undefined);
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedFAQ, setExpandedFAQ] = useState<string | null>(null);
  const [expandedArticle, setExpandedArticle] = useState<string | null>(null);
  
  // Contact form state
  const [contactForm, setContactForm] = useState({
    name: '',
    email: '',
    subject: '',
    message: '',
    category: 'support' as 'sales' | 'support' | 'billing' | 'partnership' | 'media' | 'other',
  });

  // Hooks
  const { categories, loading: categoriesLoading } = useHelpCategories();
  const { articles, loading: articlesLoading } = useHelpArticles(selectedCategory);
  const { faqs, loading: faqsLoading } = useFAQs(selectedCategory);
  const { submitContact, loading: submittingContact } = useSubmitContactForm();

  // Simple markdown to text converter for display
  const renderMarkdownText = (text: string) => {
    // Convert markdown headers to styled text
    const lines = text.split('\n');
    return lines.map((line, index) => {
      if (line.startsWith('### ')) {
        return (
          <Typography key={index} variant="h4" bold style={styles.markdownH3}>
            {line.replace('### ', '')}
          </Typography>
        );
      } else if (line.startsWith('## ')) {
        return (
          <Typography key={index} variant="h3" bold style={styles.markdownH2}>
            {line.replace('## ', '')}
          </Typography>
        );
      } else if (line.startsWith('# ')) {
        return (
          <Typography key={index} variant="h2" bold style={styles.markdownH1}>
            {line.replace('# ', '')}
          </Typography>
        );
      } else if (line.startsWith('- ')) {
        return (
          <Typography key={index} variant="body" style={styles.markdownBullet}>
            • {line.replace('- ', '')}
          </Typography>
        );
      } else if (line.trim() === '') {
        return <View key={index} style={styles.markdownBreak} />;
      } else {
        return (
          <Typography key={index} variant="body" style={styles.markdownParagraph}>
            {line}
          </Typography>
        );
      }
    });
  };

  // Contact form submission
  const handleContactSubmit = async () => {
    if (!contactForm.name || !contactForm.email || !contactForm.subject || !contactForm.message) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    try {
      await submitContact({
        name: contactForm.name,
        email: contactForm.email,
        subject: contactForm.subject,
        message: contactForm.message,
        category: contactForm.category,
        urgency: 'medium',
        preferredContactMethod: 'email',
        gdprConsent: true,
        subscribeToNewsletter: false,
      });
      
      // Reset form on success
      setContactForm({
        name: '',
        email: '',
        subject: '',
        message: '',
        category: 'support',
      });
      
      Alert.alert('Success', 'Your message has been sent. We\'ll get back to you soon!');
    } catch (error) {
      console.error('Failed to submit contact form:', error);
      Alert.alert('Error', 'Failed to send message. Please try again.');
    }
  };

  const renderTabButtons = () => (
    <View style={styles.tabContainer}>
      <TouchableOpacity
        style={[styles.tab, activeTab === 'help' && styles.activeTab]}
        onPress={() => setActiveTab('help')}
      >
        <Ionicons 
          name="help-circle-outline" 
          size={20} 
          color={activeTab === 'help' ? colors.primary : colors.text.secondary} 
        />
        <Typography 
          variant="body" 
          color={activeTab === 'help' ? 'primary' : 'secondary'}
          style={styles.tabText}
        >
          Help Articles
        </Typography>
      </TouchableOpacity>
      
      <TouchableOpacity
        style={[styles.tab, activeTab === 'faq' && styles.activeTab]}
        onPress={() => setActiveTab('faq')}
      >
        <Ionicons 
          name="chatbubble-ellipses-outline" 
          size={20} 
          color={activeTab === 'faq' ? colors.primary : colors.text.secondary} 
        />
        <Typography 
          variant="body" 
          color={activeTab === 'faq' ? 'primary' : 'secondary'}
          style={styles.tabText}
        >
          FAQ
        </Typography>
      </TouchableOpacity>
      
      <TouchableOpacity
        style={[styles.tab, activeTab === 'contact' && styles.activeTab]}
        onPress={() => setActiveTab('contact')}
      >
        <Ionicons 
          name="mail-outline" 
          size={20} 
          color={activeTab === 'contact' ? colors.primary : colors.text.secondary} 
        />
        <Typography 
          variant="body" 
          color={activeTab === 'contact' ? 'primary' : 'secondary'}
          style={styles.tabText}
        >
          Contact Us
        </Typography>
      </TouchableOpacity>
    </View>
  );

  const renderCategoryFilter = () => (
    <View style={styles.categoryContainer}>
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.categoryScrollContent}
        style={styles.categoryScrollView}
      >
        <TouchableOpacity
          style={[styles.categoryChip, !selectedCategory && styles.activeCategoryChip]}
          onPress={() => setSelectedCategory(undefined)}
        >
          <Typography 
            variant="bodySmall" 
            color={!selectedCategory ? 'white' : 'secondary'}
          >
            All
          </Typography>
        </TouchableOpacity>
        
        {categories?.map((category) => (
          <TouchableOpacity
            key={category.id}
            style={[styles.categoryChip, selectedCategory === category.id && styles.activeCategoryChip]}
            onPress={() => setSelectedCategory(category.id)}
          >
            <Typography 
              variant="bodySmall" 
              color={selectedCategory === category.id ? 'white' : 'secondary'}
            >
              {category.name}
            </Typography>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  const renderHelpArticles = () => {
    if (articlesLoading) {
      return <ActivityIndicator size="small" color={colors.primary} style={styles.loader} />;
    }

    if (!articles || articles.length === 0) {
      return (
        <View style={styles.emptyState}>
          <Ionicons name="document-text-outline" size={48} color={colors.text.tertiary} />
          <Typography variant="body" color="secondary" style={styles.emptyTitle}>
            No articles found
          </Typography>
          <Typography variant="bodySmall" color="tertiary" style={styles.emptySubtitle}>
            Try changing your search or category filter.
          </Typography>
        </View>
      );
    }

    const filteredArticles = searchQuery.length > 2 
      ? articles.filter(article => 
          article.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          article.content.toLowerCase().includes(searchQuery.toLowerCase())
        )
      : articles;

    if (filteredArticles.length === 0 && searchQuery.length > 2) {
      return (
        <View style={styles.emptyState}>
          <Ionicons name="search-outline" size={48} color={colors.text.tertiary} />
          <Typography variant="body" color="secondary" style={styles.emptyTitle}>
            No search results
          </Typography>
          <Typography variant="bodySmall" color="tertiary" style={styles.emptySubtitle}>
            Try different keywords or check your spelling.
          </Typography>
        </View>
      );
    }

    return (
      <View>
        {filteredArticles.map((item) => {
          const isExpanded = expandedArticle === item.id;

          return (
            <Card key={item.id} style={styles.articleCard}>
              <TouchableOpacity
                onPress={() => setExpandedArticle(isExpanded ? null : item.id)}
              >
                <View style={styles.articleHeader}>
                  <View style={styles.articleTitleContainer}>
                    <Typography variant="body" bold>
                      {item.title}
                    </Typography>
                    {item.difficulty && (
                      <View style={[styles.difficultyBadge, styles[`${item.difficulty}Badge`]]}>
                        <Typography variant="caption" color="white">
                          {item.difficulty}
                        </Typography>
                      </View>
                    )}
                  </View>
                  <Ionicons 
                    name={isExpanded ? "chevron-up" : "chevron-down"} 
                    size={20} 
                    color={colors.text.secondary} 
                  />
                </View>
                
                {!isExpanded && item.excerpt && (
                  <Typography variant="bodySmall" color="secondary" style={styles.articleExcerpt}>
                    {item.excerpt}
                  </Typography>
                )}
              </TouchableOpacity>
              
              {isExpanded && (
                <View style={styles.articleContent}>
                  <View style={styles.articleText}>
                    {renderMarkdownText(item.content)}
                  </View>
                  {item.tags && item.tags.length > 0 && (
                    <View style={styles.tagsContainer}>
                      {item.tags.map((tag) => (
                        <View key={`${item.id}-${tag}`} style={styles.tag}>
                          <Typography variant="caption" color="primary">
                            {tag}
                          </Typography>
                        </View>
                      ))}
                    </View>
                  )}
                </View>
              )}
            </Card>
          );
        })}
      </View>
    );
  };

  const renderFAQs = () => {
    if (faqsLoading) {
      return <ActivityIndicator size="small" color={colors.primary} style={styles.loader} />;
    }

    if (!faqs || faqs.length === 0) {
      return (
        <View style={styles.emptyState}>
          <Ionicons name="chatbubble-ellipses-outline" size={48} color={colors.text.tertiary} />
          <Typography variant="body" color="secondary" style={styles.emptyTitle}>
            No FAQs found
          </Typography>
          <Typography variant="bodySmall" color="tertiary" style={styles.emptySubtitle}>
            Try changing your search or category filter.
          </Typography>
        </View>
      );
    }

    const filteredFAQs = searchQuery.length > 2 
      ? faqs.filter(faq => 
          faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
          faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
        )
      : faqs;

    if (filteredFAQs.length === 0 && searchQuery.length > 2) {
      return (
        <View style={styles.emptyState}>
          <Ionicons name="search-outline" size={48} color={colors.text.tertiary} />
          <Typography variant="body" color="secondary" style={styles.emptyTitle}>
            No search results
          </Typography>
          <Typography variant="bodySmall" color="tertiary" style={styles.emptySubtitle}>
            Try different keywords or check your spelling.
          </Typography>
        </View>
      );
    }

    return (
      <View>
        {filteredFAQs.map((item) => {
          const isExpanded = expandedFAQ === item.id;

          return (
            <Card key={item.id} style={styles.faqCard}>
              <TouchableOpacity
                onPress={() => setExpandedFAQ(isExpanded ? null : item.id)}
              >
                <View style={styles.faqHeader}>
                  <Typography variant="body" bold style={styles.faqQuestion}>
                    {item.question}
                  </Typography>
                  <Ionicons 
                    name={isExpanded ? "chevron-up" : "chevron-down"} 
                    size={20} 
                    color={colors.text.secondary} 
                  />
                </View>
              </TouchableOpacity>
              
              {isExpanded && (
                <View style={styles.faqContent}>
                  <View style={styles.faqAnswer}>
                    {renderMarkdownText(item.answer)}
                  </View>
                  {item.tags && item.tags.length > 0 && (
                    <View style={styles.tagsContainer}>
                      {item.tags.map((tag) => (
                        <View key={`${item.id}-${tag}`} style={styles.tag}>
                          <Typography variant="caption" color="primary">
                            {tag}
                          </Typography>
                        </View>
                      ))}
                    </View>
                  )}
                </View>
              )}
            </Card>
          );
        })}
      </View>
    );
  };

  const renderContactForm = () => (
    <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={styles.scrollContent}>
      <Card style={styles.contactCard}>
        <Typography variant="h4" bold style={styles.contactTitle}>
          Get in Touch
        </Typography>
        <Typography variant="body" color="secondary" style={styles.contactSubtitle}>
          Can't find what you're looking for? Send us a message and we'll get back to you soon.
        </Typography>
        
        <Input
          label="Name"
          value={contactForm.name}
          onChangeText={(name) => setContactForm(prev => ({ ...prev, name }))}
          placeholder="Your full name"
        />
        
        <Input
          label="Email"
          value={contactForm.email}
          onChangeText={(email) => setContactForm(prev => ({ ...prev, email }))}
          placeholder="<EMAIL>"
          keyboardType="email-address"
          autoCapitalize="none"
        />
        
        <View style={styles.inputContainer}>
          <Typography variant="label" color="primary" style={styles.label}>
            Category
          </Typography>
          <View style={styles.categoryRow}>
            {[
              { key: 'support', label: 'Support' },
              { key: 'billing', label: 'Billing' },
              { key: 'sales', label: 'Sales' },
              { key: 'other', label: 'Other' },
            ].map((category) => (
              <TouchableOpacity
                key={category.key}
                style={[
                  styles.categoryOption,
                  contactForm.category === category.key && styles.activeCategoryOption
                ]}
                onPress={() => setContactForm(prev => ({ ...prev, category: category.key as any }))}
              >
                <Typography 
                  variant="bodySmall" 
                  color={contactForm.category === category.key ? 'white' : 'secondary'}
                >
                  {category.label}
                </Typography>
              </TouchableOpacity>
            ))}
          </View>
        </View>
        
        <Input
          label="Subject"
          value={contactForm.subject}
          onChangeText={(subject) => setContactForm(prev => ({ ...prev, subject }))}
          placeholder="Brief description of your inquiry"
        />
        
        <Input
          label="Message"
          value={contactForm.message}
          onChangeText={(message) => setContactForm(prev => ({ ...prev, message }))}
          placeholder="Please provide details about your question or issue..."
          multiline
          numberOfLines={4}
          style={styles.messageInput}
        />
        
        <Button
          title="Send Message"
          onPress={handleContactSubmit}
          disabled={submittingContact || !contactForm.name || !contactForm.email || !contactForm.subject || !contactForm.message}
          style={styles.submitButton}
        />
      </Card>
    </ScrollView>
  );

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen 
        options={{
          title: 'Help & Support',
          headerShown: true,
          headerStyle: { backgroundColor: colors.primary },
          headerTintColor: 'white',
          headerTitleStyle: { color: 'white', fontWeight: '600' },
        }}
      />
      
      <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          {renderTabButtons()}
          
          {(activeTab === 'help' || activeTab === 'faq') && (
            <>
              {renderCategoryFilter()}
              <SearchInput
                value={searchQuery}
                onChangeText={setSearchQuery}
                placeholder={`Search ${activeTab === 'help' ? 'articles' : 'FAQs'}...`}
                style={styles.searchInput}
              />
            </>
          )}
          
          <View style={styles.contentArea}>
            {activeTab === 'help' && renderHelpArticles()}
            {activeTab === 'faq' && renderFAQs()}
            {activeTab === 'contact' && renderContactForm()}
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollContainer: {
    flex: 1,
  },
  content: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 4,
    marginBottom: 16,
    marginTop: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    borderRadius: 8,
  },
  activeTab: {
    backgroundColor: colors.primary + '10',
  },
  tabText: {
    marginLeft: 6,
    fontSize: 14,
    fontWeight: '500',
  },
  categoryContainer: {
    marginBottom: 12,
    height: 40,
  },
  categoryScrollView: {
    flexGrow: 0,
  },
  categoryScrollContent: {
    paddingHorizontal: 0,
    alignItems: 'center',
    paddingVertical: 4,
  },
  categoryChip: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: 'white',
    borderRadius: 16,
    marginRight: 8,
    borderWidth: 1,
    borderColor: colors.divider,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 1,
    elevation: 1,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  activeCategoryChip: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  searchInput: {
    marginBottom: 16,
  },
  contentArea: {
    flex: 1,
  },
  listContent: {
    paddingBottom: 20,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 20,
  },
  loader: {
    marginTop: 32,
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 32,
    paddingVertical: 48,
  },
  emptyTitle: {
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtitle: {
    textAlign: 'center',
    lineHeight: 20,
  },
  articleCard: {
    marginBottom: 12,
  },
  articleHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  articleTitleContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 12,
  },
  difficultyBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
    marginLeft: 8,
  },
  beginnerBadge: {
    backgroundColor: colors.success,
  },
  intermediateBadge: {
    backgroundColor: '#FF8C00',
  },
  advancedBadge: {
    backgroundColor: colors.error,
  },
  articleExcerpt: {
    marginTop: 8,
    lineHeight: 20,
  },
  articleContent: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: colors.divider,
  },
  articleText: {
    // Container for markdown content
  },
  markdownH1: {
    marginTop: 16,
    marginBottom: 8,
  },
  markdownH2: {
    marginTop: 12,
    marginBottom: 6,
  },
  markdownH3: {
    marginTop: 8,
    marginBottom: 4,
  },
  markdownParagraph: {
    lineHeight: 22,
    marginBottom: 4,
  },
  markdownBullet: {
    lineHeight: 20,
    marginLeft: 8,
    marginBottom: 2,
  },
  markdownBreak: {
    height: 8,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 12,
  },
  tag: {
    backgroundColor: colors.primary + '10',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
    marginRight: 6,
    marginBottom: 4,
  },
  faqCard: {
    marginBottom: 12,
  },
  faqHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  faqQuestion: {
    flex: 1,
    marginRight: 12,
  },
  faqContent: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: colors.divider,
  },
  faqAnswer: {
    // Container for markdown content
  },
  contactCard: {
    padding: 20,
  },
  contactTitle: {
    marginBottom: 8,
  },
  contactSubtitle: {
    marginBottom: 24,
    lineHeight: 20,
  },
  inputContainer: {
    marginBottom: 16,
  },
  label: {
    marginBottom: 8,
  },
  categoryRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  categoryOption: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: 'white',
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: colors.divider,
  },
  activeCategoryOption: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  messageInput: {
    height: 80,
    textAlignVertical: 'top',
  },
  submitButton: {
    marginTop: 8,
  },
}); 