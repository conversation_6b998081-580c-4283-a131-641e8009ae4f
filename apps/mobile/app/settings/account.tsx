import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { router, Stack } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { Alert, Image, Pressable, StyleSheet, TouchableOpacity, View } from 'react-native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';

// UI Components
import { Input, KeyboardAwareView, Switch, Typography } from '@/components/ui';
import { colors } from '@/constants/Colors';

// Services
import { useUserProfile, useUpdateUserProfile } from '@repo/queries';

// Stores and Hooks
import { useActiveOrganizationId, useHasActiveOrganization } from '@/stores';

// Types
import { UpdateUserProfileInput } from '@repo/schemas';

export default function AccountScreen() {
  const insets = useSafeAreaInsets();
  
  // Organization context
  const hasActiveOrganization = useHasActiveOrganization();
  const organizationId = useActiveOrganizationId();

  // Data hooks
  const { profile, loading, error } = useUserProfile();
  const { updateProfile, loading: updating } = useUpdateUserProfile();

  // Form state
  const [formData, setFormData] = useState<UpdateUserProfileInput>({});
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Toast state
  const [toastVisible, setToastVisible] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastType, setToastType] = useState<'success' | 'error'>('success');

  // Show toast helper
  const showToast = (message: string, type: 'success' | 'error' = 'success') => {
    setToastMessage(message);
    setToastType(type);
    setToastVisible(true);
  };

  // Organization selection guard
  if (!hasActiveOrganization) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen options={{ headerShown: false }} />
        <View style={styles.centerContainer}>
          <Typography variant="h3" color="secondary" style={{ marginBottom: 8 }}>
            Select an Organization
          </Typography>
          <Typography variant="body" color="secondary" center>
            Please select an organization to access account settings
          </Typography>
        </View>
      </SafeAreaView>
    );
  }

  // Initialize form data when profile loads
  useEffect(() => {
    if (profile) {
      setFormData({
        firstName: profile.firstName,
        lastName: profile.lastName,
        email: profile.email,
        phone: profile.phone,
        avatar: profile.avatar,
        timezone: profile.timezone,
        dateFormat: profile.dateFormat,
        language: profile.language,
        notifications: profile.notifications,
        preferences: profile.preferences,
      });
    }
  }, [profile]);

  // Update form field
  const updateField = (field: keyof UpdateUserProfileInput, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setHasUnsavedChanges(true);
  };

  // Update notification preferences
  const updateNotification = (key: string, value: boolean) => {
    setFormData(prev => ({
      ...prev,
      notifications: {
        email: true,
        push: true,
        marketing: false,
        invoiceReminders: true,
        paymentUpdates: true,
        ...prev.notifications,
        [key]: value,
      }
    }));
    setHasUnsavedChanges(true);
  };

  // Avatar upload
  const handleAvatarUpload = async () => {
    const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
    
    if (!permissionResult.granted) {
      showToast('Please grant camera roll permissions to upload an avatar.', 'error');
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    if (!result.canceled && result.assets[0]) {
      updateField('avatar', result.assets[0].uri);
      showToast('Avatar updated successfully!');
    }
  };

  // Save changes
  const handleSave = async () => {
    try {
      await updateProfile(formData);
      setHasUnsavedChanges(false);
      showToast('Profile updated successfully!');
    } catch (error) {
      showToast('Failed to update profile. Please try again.', 'error');
    }
  };

  // Reset form
  const handleReset = () => {
    if (profile) {
      setFormData({
        firstName: profile.firstName,
        lastName: profile.lastName,
        email: profile.email,
        phone: profile.phone,
        avatar: profile.avatar,
        timezone: profile.timezone,
        dateFormat: profile.dateFormat,
        language: profile.language,
        notifications: profile.notifications,
        preferences: profile.preferences,
      });
      setHasUnsavedChanges(false);
    }
  };

  // Generate initials for photo placeholder
  const getInitials = () => {
    const firstName = formData.firstName?.trim() || '';
    const lastName = formData.lastName?.trim() || '';
    
    if (firstName && lastName) {
      return `${firstName[0]}${lastName[0]}`.toUpperCase();
    } else if (firstName) {
      return firstName[0].toUpperCase();
    } else if (lastName) {
      return lastName[0].toUpperCase();
    }
    return 'ME';
  };

  const handleBack = () => {
    router.back();
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen options={{ headerShown: false }} />
        <View style={styles.centerContainer}>
          <Typography variant="body" color="secondary">Loading profile...</Typography>
        </View>
      </SafeAreaView>
    );
  }

  if (error || !profile) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen options={{ headerShown: false }} />
        <View style={styles.centerContainer}>
          <Typography variant="h3" color="error" style={{ marginBottom: 8 }}>
            Error Loading Profile
          </Typography>
          <Typography variant="body" color="secondary" center>
            Unable to load user profile
          </Typography>
        </View>
      </SafeAreaView>
    );
  }

  const renderSettingRow = (icon: string, title: string, value: string, onPress: () => void, showChevron = true) => (
    <Pressable style={styles.settingRow} onPress={onPress}>
      <View style={styles.settingLeft}>
        <View style={styles.settingIcon}>
          <Ionicons name={icon as any} size={18} color={colors.primary} />
        </View>
        <Typography variant="body" style={styles.settingTitle}>{title}</Typography>
      </View>
      <View style={styles.settingRight}>
        <Typography variant="bodySmall" color="secondary" style={styles.settingValue}>
          {value}
        </Typography>
        {showChevron && (
          <Ionicons name="chevron-forward" size={16} color={colors.text.secondary} />
        )}
      </View>
    </Pressable>
  );

  const renderSwitchRow = (icon: string, title: string, subtitle: string, value: boolean, onValueChange: (value: boolean) => void) => (
    <View style={styles.switchRow}>
      <View style={styles.settingLeft}>
        <View style={styles.settingIcon}>
          <Ionicons name={icon as any} size={18} color={colors.primary} />
        </View>
        <View style={styles.switchContent}>
          <Typography variant="body" style={styles.settingTitle}>{title}</Typography>
          <Typography variant="bodySmall" color="secondary" style={styles.switchSubtitle}>
            {subtitle}
          </Typography>
        </View>
      </View>
      <Switch
        value={value}
        onValueChange={onValueChange}
      />
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen options={{ headerShown: false }} />
      
      <KeyboardAwareView 
        style={{ flex: 1 }}
        contentContainerStyle={{ paddingBottom: insets.bottom + 20 }}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={[styles.header, { paddingTop: insets.top }]}>
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <Ionicons name="arrow-back" size={24} color={colors.text.primary} />
          </TouchableOpacity>
          <View style={styles.headerCenter}>
            <Typography variant="body" style={styles.headerTitle}>
              Account Settings
            </Typography>
          </View>
          <View style={styles.headerRight}>
            {hasUnsavedChanges && (
              <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
                <Typography variant="bodySmall" color="primary" bold>
                  {updating ? 'Saving...' : 'Save'}
                </Typography>
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* Profile Photo Section */}
        <View style={styles.photoSection}>
          <Typography variant="bodySmall" color="secondary" style={styles.photoLabel}>
            Profile Photo
          </Typography>
          <View style={styles.photoContainer}>
            {formData.avatar ? (
              <View style={styles.photoWrapper}>
                <Image source={{ uri: formData.avatar }} style={styles.photoImage} />
                <TouchableOpacity style={styles.removePhotoButton} onPress={() => updateField('avatar', undefined)}>
                  <Ionicons name="close" size={16} color={colors.background} />
                </TouchableOpacity>
              </View>
            ) : (
              <TouchableOpacity style={styles.photoPlaceholder} onPress={handleAvatarUpload}>
                <View style={styles.photoInitials}>
                  <Typography variant="body" color="primary" bold>
                    {getInitials()}
                  </Typography>
                </View>
                <Ionicons name="camera" size={20} color={colors.primary} style={styles.cameraIcon} />
              </TouchableOpacity>
            )}
            {formData.avatar && (
              <TouchableOpacity style={styles.changePhotoButton} onPress={handleAvatarUpload}>
                <Typography variant="bodySmall" color="primary">
                  Change Photo
                </Typography>
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* Personal Information */}
        <Typography variant="bodySmall" color="secondary" style={styles.sectionLabel}>
          Personal Information
        </Typography>
        
        <Input
          label="First Name"
          value={formData.firstName || ''}
          onChangeText={(value) => updateField('firstName', value)}
          placeholder="Enter first name"
          containerStyle={styles.input}
        />
        
        <Input
          label="Last Name"
          value={formData.lastName || ''}
          onChangeText={(value) => updateField('lastName', value)}
          placeholder="Enter last name"
          containerStyle={styles.input}
        />
        
        <Input
          label="Email Address"
          value={formData.email || ''}
          onChangeText={(value) => updateField('email', value)}
          placeholder="Enter email address"
          keyboardType="email-address"
          autoCapitalize="none"
          containerStyle={styles.input}
        />
        
        <Input
          label="Phone Number"
          value={formData.phone || ''}
          onChangeText={(value) => updateField('phone', value)}
          placeholder="Enter phone number"
          keyboardType="phone-pad"
          containerStyle={styles.input}
        />

        {/* Preferences */}
        <Typography variant="bodySmall" color="secondary" style={styles.sectionLabel}>
          Preferences
        </Typography>
        
        <View style={styles.sectionCard}>
          {renderSettingRow(
            'time-outline',
            'Timezone',
            formData.timezone || 'UTC',
            () => Alert.alert('Timezone', 'Timezone selection will be implemented soon.')
          )}
          
          <View style={styles.settingSeparator} />
          
          {renderSettingRow(
            'calendar-outline',
            'Date Format',
            formData.dateFormat || 'MM/DD/YYYY',
            () => Alert.alert('Date Format', 'Date format selection will be implemented soon.')
          )}
          
          <View style={styles.settingSeparator} />
          
          {renderSettingRow(
            'language-outline',
            'Language',
            formData.language === 'en' ? 'English' : formData.language || 'English',
            () => Alert.alert('Language', 'Language selection will be implemented soon.')
          )}
        </View>

        {/* Notifications */}
        <Typography variant="bodySmall" color="secondary" style={styles.sectionLabel}>
          Notifications
        </Typography>
        
        <View style={styles.sectionCard}>
            {renderSwitchRow(
              'mail-outline',
              'Email Notifications',
              'Receive notifications via email',
              formData.notifications?.email ?? true,
              (value) => updateNotification('email', value)
            )}
            
            <View style={styles.settingSeparator} />
            
            {renderSwitchRow(
              'notifications-outline',
              'Push Notifications',
              'Receive push notifications on mobile',
              formData.notifications?.push ?? true,
              (value) => updateNotification('push', value)
            )}
            
            <View style={styles.settingSeparator} />
            
            {renderSwitchRow(
              'megaphone-outline',
              'Marketing Emails',
              'Receive marketing and promotional emails',
              formData.notifications?.marketing ?? false,
              (value) => updateNotification('marketing', value)
            )}
            
            <View style={styles.settingSeparator} />
            
            {renderSwitchRow(
              'receipt-outline',
              'Invoice Reminders',
              'Get reminded about unpaid invoices',
              formData.notifications?.invoiceReminders ?? true,
              (value) => updateNotification('invoiceReminders', value)
            )}
            
            <View style={styles.settingSeparator} />
            
            {renderSwitchRow(
              'card-outline',
              'Payment Updates',
              'Get notified about payment status changes',
              formData.notifications?.paymentUpdates ?? true,
              (value) => updateNotification('paymentUpdates', value)
            )}
          </View>

        {/* Account Actions */}
        <Typography variant="bodySmall" color="secondary" style={styles.sectionLabel}>
          Account Actions
        </Typography>
          
          <View style={styles.sectionCard}>
            <Pressable 
              style={styles.dangerRow}
              onPress={() => {
                if (hasUnsavedChanges) {
                  Alert.alert(
                    'Unsaved Changes',
                    'You have unsaved changes. Do you want to reset them?',
                    [
                      { text: 'Cancel', style: 'cancel' },
                      { text: 'Reset', style: 'destructive', onPress: handleReset }
                    ]
                  );
                } else {
                  Alert.alert('No Changes', 'No unsaved changes to reset.');
                }
              }}
            >
              <View style={styles.settingLeft}>
                <View style={[styles.settingIcon, styles.dangerIcon]}>
                  <Ionicons name="refresh-outline" size={18} color={colors.error} />
                </View>
                <Typography variant="body" color="error" style={styles.settingTitle}>
                  Reset Changes
                </Typography>
              </View>
              <Ionicons name="chevron-forward" size={16} color={colors.error} />
            </Pressable>
          </View>
      </KeyboardAwareView>

      {/* Loading Overlay */}
      {updating && (
        <View style={styles.loadingOverlay}>
          <View style={styles.loadingCard}>
            <Typography variant="body" color="secondary">Saving changes...</Typography>
          </View>
        </View>
      )}

      {/* Toast Notifications - TODO: Fix import issue */}
      {/* <Toast
        visible={toastVisible}
        message={toastMessage}
        type={toastType}
        onHide={() => setToastVisible(false)}
      /> */}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },

  // Header
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 12,
    backgroundColor: colors.cardBackground,
  },
  backButton: {
    padding: 4,
    marginRight: 16,
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text.primary,
  },
  headerRight: {
    minWidth: 60,
    alignItems: 'flex-end',
  },
  saveButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: colors.primaryVeryLight,
    borderRadius: 8,
  },

  // Profile Header
  profileHeaderCard: {
    backgroundColor: colors.cardBackground,
    paddingHorizontal: 20,
    paddingVertical: 24,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: colors.divider,
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  avatarPlaceholder: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: colors.divider,
  },
  avatarEditButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: colors.cardBackground,
  },
  profileInfo: {
    alignItems: 'center',
  },
  profileName: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.text.primary,
    marginBottom: 4,
    textAlign: 'center',
  },
  profileEmail: {
    fontSize: 15,
    color: colors.text.secondary,
    textAlign: 'center',
  },

  // Photo Section
  photoSection: {
    marginTop: 20,
    marginBottom: 24,
    paddingHorizontal: 20,
  },
  photoLabel: {
    fontSize: 12,
    marginBottom: 12,
    textTransform: 'uppercase',
    fontWeight: '500',
    letterSpacing: 0.5,
  },
  photoContainer: {
    alignItems: 'center',
  },
  photoWrapper: {
    position: 'relative',
  },
  photoImage: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.primaryVeryLight,
  },
  removePhotoButton: {
    position: 'absolute',
    top: -4,
    right: -4,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: colors.error,
    alignItems: 'center',
    justifyContent: 'center',
  },
  photoPlaceholder: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.primaryVeryLight,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    borderWidth: 2,
    borderColor: colors.primary,
    borderStyle: 'dashed',
  },
  photoInitials: {
    position: 'absolute',
    top: 8,
    left: 8,
    right: 8,
    bottom: 8,
    borderRadius: 32,
    backgroundColor: colors.avatarBackground?.primary || colors.primaryVeryLight,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cameraIcon: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    backgroundColor: colors.background,
    borderRadius: 10,
    padding: 2,
  },
  changePhotoButton: {
    marginTop: 8,
    padding: 4,
  },

  // Section Labels
  sectionLabel: {
    fontSize: 12,
    marginBottom: 12,
    marginTop: 16,
    textTransform: 'uppercase',
    fontWeight: '500',
    letterSpacing: 0.5,
    paddingHorizontal: 20,
  },

  // Sections
  section: {
    marginTop: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.text.primary,
    marginBottom: 12,
    paddingHorizontal: 20,
  },
  sectionCard: {
    backgroundColor: colors.cardBackground,
    marginHorizontal: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.divider,
    overflow: 'hidden',
  },

  // Input Fields
  input: {
    marginBottom: 12,
    marginHorizontal: 16,
  },

  // Input Fields
  inputContainer: {
    marginBottom: 0,
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  inputField: {
    backgroundColor: 'transparent',
    borderWidth: 0,
    paddingHorizontal: 0,
    paddingVertical: 6,
    fontSize: 16,
  },
  inputSeparator: {
    height: 1,
    backgroundColor: colors.divider,
    marginLeft: 20,
  },

  // Setting Rows
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 18,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIcon: {
    width: 32,
    height: 32,
    borderRadius: 8,
    backgroundColor: colors.primaryVeryLight,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  dangerIcon: {
    backgroundColor: '#FFE5E5',
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text.primary,
    flex: 1,
  },
  settingRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  settingValue: {
    fontSize: 14,
    color: colors.text.secondary,
  },
  settingSeparator: {
    height: 1,
    backgroundColor: colors.divider,
    marginLeft: 64,
  },

  // Switch Rows
  switchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 18,
  },
  switchContent: {
    flex: 1,
  },
  switchSubtitle: {
    fontSize: 13,
    color: colors.text.secondary,
    marginTop: 2,
    lineHeight: 18,
  },

  // Danger Zone
  dangerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 18,
  },

  // Loading Overlay
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingCard: {
    backgroundColor: colors.cardBackground,
    borderRadius: 12,
    padding: 20,
    marginHorizontal: 40,
    alignItems: 'center',
  },
}); 