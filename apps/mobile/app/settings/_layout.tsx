import { colors } from '@/constants/Colors';
import { Stack } from 'expo-router';

export default function SettingsLayout() {
  return (
    <Stack
      screenOptions={{
        headerShown: true,
        headerStyle: {
          backgroundColor: colors.primary,
        },
        headerTintColor: 'white',
        headerTitleStyle: {
          fontWeight: '600',
          fontSize: 18,
        },
        headerBackTitle: '',
        headerShadowVisible: false,
      }}
    >
      {/* Account Settings */}
      <Stack.Screen 
        name="account" 
        options={{
          title: 'Account Settings',
          headerStyle: {
            backgroundColor: colors.primary,
          },
        }} 
      />
      
      {/* Subscription Management */}
      <Stack.Screen 
        name="subscription" 
        options={{
          title: 'Subscription',
          headerStyle: {
            backgroundColor: colors.primary,
          },
        }} 
      />
      
      {/* Invoice Design Templates */}
      <Stack.Screen 
        name="design" 
        options={{
          title: 'Invoice Design',
          headerStyle: {
            backgroundColor: colors.primary,
          },
        }} 
      />
      
      {/* App Defaults */}
      <Stack.Screen 
        name="defaults" 
        options={{
          title: 'Default Settings',
          headerStyle: {
            backgroundColor: colors.primary,
          },
        }} 
      />
      
      {/* Support & Help */}
      <Stack.Screen 
        name="support" 
        options={{
          title: 'Help & Support',
          headerStyle: {
            backgroundColor: colors.primary,
          },
        }} 
      />
      
      {/* Feedback */}
      <Stack.Screen 
        name="feedback" 
        options={{
          title: 'Send Feedback',
          headerStyle: {
            backgroundColor: colors.primary,
          },
        }} 
      />
    </Stack>
  );
} 