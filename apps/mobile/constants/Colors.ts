/**
 * Below are the colors that are used in the app. The colors are defined in the light and dark mode.
 * There are many other ways to style your app. For example, [Nativewind](https://www.nativewind.dev/), [Tamagui](https://tamagui.dev/), [unistyles](https://reactnativeunistyles.vercel.app), etc.
 */

/**
 * App color definitions
 */

// App theme color
const tintColorLight = '#3490f3';

// App theme colors
export const colors = {
  // Primary colors - Updated to blue theme
  primary: '#3490f3',
  primaryLight: '#4da3f5',
  primaryLighter: '#66b6f7',
  primaryVeryLight: '#e6f3ff',
  
  // Status colors
  success: '#4CAF50',
  error: '#F44336',
  warning: '#FFC107',
  disabled: '#C7C7CC',
  
  // Background colors
  background: '#F7F8FA',
  cardBackground: '#FFFFFF',
  
  // Text colors
  text: {
    primary: '#222222',
    secondary: '#8E8E93',
    tertiary: '#C7C7CC',
    white: '#FFFFFF',
    dark: '#333333',
  },
  
  // Status backgrounds - updated with blue theme and subtle colors
  statusBackground: {
    pending: '#e6f3ff',       // Very light blue for pending
    pendingIcon: '#cce7ff',   // Slightly darker blue for pending icon
    paid: '#EEFBF0',          // Keep green for paid status
    paidIcon: '#D5F2DD',      // Keep green for paid icon
    overdue: '#FEEEEE',       // Keep red for overdue status
    overdueIcon: '#FADADA',   // Keep red for overdue icon
  },
  
  // UI elements
  divider: '#F0F0F0',
  shadow: '#000000',
  transparentWhite: 'rgba(255,255,255,0.18)',
  buttonBackground: 'rgba(255,255,255,0.25)',
  avatarPlaceholder: '#F2F2F7',
  
  // Avatar backgrounds - updated with blue theme
  avatarBackground: {
    primary: '#cce7ff',       // Light blue for primary avatars
    success: '#D5F2DD',       // Keep green for success
    error: '#FADADA',         // Keep red for error
    default: '#e6f3ff',       // Light blue for default
  },
};

// Theme colors object - updated with blue theme
export const Colors = {
  light: {
    text: '#11181C',
    background: '#fff',
    tint: tintColorLight,
    icon: '#687076',
    tabIconDefault: '#687076',
    tabIconSelected: tintColorLight,
  }
};
