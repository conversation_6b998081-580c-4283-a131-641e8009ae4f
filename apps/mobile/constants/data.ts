import { Client } from '../defs/client';
import { InvoiceListItem } from '../defs/invoice';
import { Service } from '../defs/service';
import { TaxOption } from '../defs/tax';

// Company-specific data for dashboard
const companyData = {
  '1': { // Moonlight Design - rich data (5-6 items)
  invoiceStats: {
    pending: {
      count: 8,
      amount: '$4,250',
    },
    paid: {
      count: 12,
      amount: '$8,800',
    },
    overdue: {
      count: 3,
      amount: '$1,750',
    },
  },
  summary: {
    revenue: {
      value: '$24,780',
      percentChange: 12.5,
    },
    totalInvoices: {
      value: '187',
      percentChange: 8.2,
    },
  },
  recentActivity: [
    {
      id: '1',
      title: 'Acme Inc. paid invoice INV-MOON-001',
      date: 'May 15',
      amount: '$1,250.00',
      type: 'payment',
      vendor: 'Acme Inc.',
      avatar: null,
    },
    {
      id: '2',
      title: 'Invoice INV-MOON-002 sent to Globex Corporation',
      date: 'May 18',
      amount: '$850.50',
      type: 'invoice',
      vendor: 'Globex Corporation',
      avatar: null,
    },
      {
        id: '3',
        title: 'TechCorp paid invoice INV-MOON-003',
        date: 'May 20',
        amount: '$3,200.00',
        type: 'payment',
        vendor: 'TechCorp',
        avatar: null,
      },
      {
        id: '4',
        title: 'Invoice INV-MOON-005 sent to Initech',
        date: 'May 22',
        amount: '$1,500.00',
        type: 'invoice',
        vendor: 'Initech',
        avatar: null,
      },
      {
        id: '5',
        title: 'Invoice INV-MOON-006 sent to Massive Dynamic',
        date: 'May 25',
        amount: '$4,800.00',
        type: 'invoice',
        vendor: 'Massive Dynamic',
        avatar: null,
      },
      {
        id: '6',
        title: 'DevCorp paid invoice INV-MOON-004',
        date: 'May 26',
        amount: '$2,240.00',
        type: 'payment',
        vendor: 'DevCorp',
        avatar: null,
      },
    ],
  },
  '2': { // Stellar Web Services - moderate data (2-3 items)
    invoiceStats: {
      pending: {
        count: 5,
        amount: '$3,200',
      },
      paid: {
        count: 8,
        amount: '$5,400',
      },
      overdue: {
        count: 1,
        amount: '$750',
      },
    },
    summary: {
      revenue: {
        value: '$18,350',
        percentChange: 9.7,
      },
      totalInvoices: {
        value: '124',
        percentChange: 7.1,
      },
    },
    recentActivity: [
      {
        id: '1',
        title: 'Wayne Enterprises paid invoice INV-SWS-001',
        date: 'May 20',
        amount: '$1,800.00',
        type: 'payment',
        vendor: 'Wayne Enterprises',
        avatar: null,
      },
      {
        id: '2',
        title: 'Invoice INV-SWS-002 sent to Stark Industries',
        date: 'May 22',
        amount: '$1,200.00',
        type: 'invoice',
        vendor: 'Stark Industries',
        avatar: null,
      },
    ],
  },
  '3': { // Green Tech Solutions - empty data (0 items)
    invoiceStats: {
      pending: {
        count: 0,
        amount: '$0',
      },
      paid: {
        count: 0,
        amount: '$0',
      },
      overdue: {
        count: 0,
        amount: '$0',
      },
    },
    summary: {
      revenue: {
        value: '$0',
        percentChange: 0,
      },
      totalInvoices: {
        value: '0',
        percentChange: 0,
      },
    },
    recentActivity: [],
  }
};

// Mock services data per organization - using proper Service type
export const servicesData: Record<string, Service[]> = {
  '1': [ // Moonlight Design - rich data (6 items)
    {
      id: 'S001',
      name: 'Logo Design',
      description: 'Professional logo design with unlimited revisions',
      pricing: {
        rate: 350.00,
        unit: 'fixed',
        currency: 'USD',
      },
      organizationId: '1',
      isActive: true,
      taxable: true,
      tags: ['design', 'branding', 'logo', 'creative'],
      createdAt: new Date('2023-01-15'),
      updatedAt: new Date('2023-01-15'),
    },
    {
      id: 'S002',
      name: 'Website Design',
      description: 'Custom website design with responsive layouts',
      pricing: {
        rate: 85.00,
        unit: 'hour',
        currency: 'USD',
      },
      organizationId: '1',
      isActive: true,
      taxable: true,
      tags: ['web', 'design', 'responsive', 'ui/ux'],
      createdAt: new Date('2023-02-10'),
      updatedAt: new Date('2023-02-10'),
    },
    {
      id: 'S003',
      name: 'Brand Identity Package',
      description: 'Complete brand identity including logo, colors, and style guide',
      pricing: {
        rate: 1200.00,
        unit: 'project',
        currency: 'USD',
      },
      organizationId: '1',
      isActive: true,
      taxable: true,
      tags: ['branding', 'identity', 'package', 'premium'],
      createdAt: new Date('2023-03-22'),
      updatedAt: new Date('2023-03-22'),
    },
    {
      id: 'S004',
      name: 'Social Media Graphics',
      description: 'Customized graphics package for social media platforms',
      pricing: {
        rate: 120.00,
        unit: 'day',
        currency: 'EUR',
      },
      organizationId: '1',
      isActive: false,
      taxable: true,
      tags: ['social-media', 'graphics', 'marketing'],
      createdAt: new Date('2023-04-05'),
      updatedAt: new Date('2023-04-05'),
    },
    {
      id: 'S005',
      name: 'UI/UX Design',
      description: 'User interface and experience design for web and mobile apps',
      pricing: {
        rate: 95.00,
        unit: 'hour',
        currency: 'USD',
      },
      organizationId: '1',
      isActive: true,
      taxable: true,
      tags: ['ui', 'ux', 'mobile', 'web', 'user-experience'],
      createdAt: new Date('2023-04-18'),
      updatedAt: new Date('2023-04-18'),
    },
    {
      id: 'S006',
      name: 'Print Design',
      description: 'Design for business cards, brochures, flyers, and other print materials',
      pricing: {
        rate: 75.00,
        unit: 'hour',
        currency: 'GBP',
      },
      organizationId: '1',
      isActive: false,
      taxable: true,
      tags: ['print', 'design', 'marketing-materials'],
      createdAt: new Date('2023-05-12'),
      updatedAt: new Date('2023-05-12'),
    }
  ],
  '2': [ // Stellar Web Services - moderate data (3 items)
    {
      id: 'S001',
      name: 'Web Development',
      description: 'Full-stack web development services',
      pricing: {
        rate: 95.00,
        unit: 'hour',
        currency: 'USD',
      },
      organizationId: '2',
      isActive: true,
      taxable: true,
      tags: ['development', 'full-stack', 'web', 'programming'],
      createdAt: new Date('2023-01-20'),
      updatedAt: new Date('2023-01-20'),
    },
    {
      id: 'S002',
      name: 'SEO Optimization',
      description: 'Search engine optimization for existing websites',
      pricing: {
        rate: 450.00,
        unit: 'day',
        currency: 'CAD',
      },
      organizationId: '2',
      isActive: true,
      taxable: true,
      tags: ['seo', 'optimization', 'marketing', 'analytics'],
      createdAt: new Date('2023-02-15'),
      updatedAt: new Date('2023-02-15'),
    },
    {
      id: 'S003',
      name: 'Website Maintenance',
      description: 'Monthly website maintenance and updates',
      pricing: {
        rate: 250.00,
        unit: 'month',
        currency: 'USD',
      },
      organizationId: '2',
      isActive: true,
      taxable: true,
      tags: ['maintenance', 'support', 'updates', 'hosting'],
      createdAt: new Date('2023-04-05'),
      updatedAt: new Date('2023-04-05'),
    },
  ],
  '3': [] // Green Tech Solutions - empty data (0 items)
};

// Mock clients data per organization - using proper Client type
export const clientsData: Record<string, Client[]> = {
  '1': [ // Moonlight Design - rich data (6 items)
    {
      id: '1',
      name: 'Acme Inc.',
      company: 'Acme Corporation',
      contact: {
        email: '<EMAIL>',
        phone: '(*************',
      },
      address: {
        fullAddress: '123 Main St, Anytown, USA',
      },
      organizationId: '1',
      isActive: true,
      defaultTaxExempt: false,
      createdAt: new Date('2023-01-15'),
      updatedAt: new Date('2023-01-15'),
    },
    {
      id: '2',
      name: 'Globex Corporation',
      company: 'Globex Corp',
      contact: {
        email: '<EMAIL>',
        phone: '(*************',
      },
      address: {
        fullAddress: '456 Business Ave, Commerce City, USA',
      },
      organizationId: '1',
      isActive: true,
      defaultTaxExempt: false,
      createdAt: new Date('2023-02-22'),
      updatedAt: new Date('2023-02-22'),
    },
    {
      id: '3',
      name: 'TechCorp',
      company: 'TechCorp Ltd',
      contact: {
        email: '<EMAIL>',
        phone: '(*************',
      },
      address: {
        fullAddress: '789 Innovation Dr, Silicon Valley, USA',
      },
      organizationId: '1',
      isActive: true,
      defaultTaxExempt: false,
      createdAt: new Date('2023-03-10'),
      updatedAt: new Date('2023-03-10'),
    },
    {
      id: '4',
      name: 'Initech',
      company: 'Initech Solutions',
      contact: {
        email: '<EMAIL>',
        phone: '(*************',
      },
      address: {
        fullAddress: '321 Corporate Blvd, Metro City, USA',
      },
      organizationId: '1',
      isActive: true,
      defaultTaxExempt: false,
      createdAt: new Date('2023-04-05'),
      updatedAt: new Date('2023-04-05'),
    },
    {
      id: '5',
      name: 'Massive Dynamic',
      company: 'Massive Dynamic Inc',
      contact: {
        email: '<EMAIL>',
        phone: '(*************',
      },
      address: {
        fullAddress: '555 Science Parkway, New York, USA',
      },
      organizationId: '1',
      isActive: true,
      defaultTaxExempt: false,
      createdAt: new Date('2023-04-28'),
      updatedAt: new Date('2023-04-28'),
    },
    {
      id: '6',
      name: 'DevCorp',
      company: 'DevCorp Technologies',
      contact: {
        email: '<EMAIL>',
        phone: '(*************',
      },
      address: {
        fullAddress: '987 Developer Lane, Tech City, USA',
      },
      organizationId: '1',
      isActive: true,
      defaultTaxExempt: false,
      createdAt: new Date('2023-05-15'),
      updatedAt: new Date('2023-05-15'),
    },
  ],
  '2': [ // Stellar Web Services - moderate data (2 items)
    {
      id: '7',
      name: 'Stark Industries',
      company: 'Stark Industries Inc',
      contact: {
        email: '<EMAIL>',
        phone: '(*************',
      },
      address: {
        fullAddress: '789 Tech Blvd, Innovation Valley, USA',
      },
      organizationId: '2',
      isActive: true,
      defaultTaxExempt: false,
      createdAt: new Date('2023-03-10'),
      updatedAt: new Date('2023-03-10'),
    },
    {
      id: '8',
      name: 'Wayne Enterprises',
      company: 'Wayne Enterprises LLC',
      contact: {
        email: '<EMAIL>',
        phone: '(*************',
      },
      address: {
        fullAddress: '101 Gotham Rd, Gotham City, USA',
      },
      organizationId: '2',
      isActive: true,
      defaultTaxExempt: false,
      createdAt: new Date('2023-04-05'),
      updatedAt: new Date('2023-04-05'),
    },
  ],
  '3': [] // Green Tech Solutions - empty data (0 items)
};

// Mock invoices data per organization - using proper InvoiceListItem type
export const invoicesData: Record<string, InvoiceListItem[]> = {
  '1': [ // Moonlight Design - rich data (6 items)
    {
      id: 'INV-001',
      invoiceNumber: 'INV-MOON-001',
      clientName: 'Acme Inc.',
      clientId: '1',
      amount: '$1,250.00',
      status: 'paid',
      issueDate: new Date('2023-05-15'),
      dueDate: new Date('2023-06-15'),
      total: 1250.00,
    },
    {
      id: 'INV-002',
      invoiceNumber: 'INV-MOON-002',
      clientName: 'Globex Corporation',
      clientId: '2',
      amount: '$850.50',
      status: 'pending',
      issueDate: new Date('2023-06-01'),
      dueDate: new Date('2023-06-18'),
      total: 850.50,
    },
    {
      id: 'INV-003',
      invoiceNumber: 'INV-MOON-003',
      clientName: 'TechCorp',
      clientId: '3',
      amount: '$3,200.00',
      status: 'paid',
      issueDate: new Date('2023-05-05'),
      dueDate: new Date('2023-06-05'),
      total: 3200.00,
    },
    {
      id: 'INV-004',
      invoiceNumber: 'INV-MOON-004',
      clientName: 'DevCorp',
      clientId: '6',
      amount: '$2,240.00',
      status: 'paid',
      issueDate: new Date('2023-05-12'),
      dueDate: new Date('2023-06-12'),
      total: 2240.00,
    },
    {
      id: 'INV-005',
      invoiceNumber: 'INV-MOON-005',
      clientName: 'Initech',
      clientId: '4',
      amount: '$1,500.00',
      status: 'pending',
      issueDate: new Date('2023-06-05'),
      dueDate: new Date('2023-07-05'),
      total: 1500.00,
    },
    {
      id: 'INV-006',
      invoiceNumber: 'INV-MOON-006',
      clientName: 'Massive Dynamic',
      clientId: '5',
      amount: '$4,800.00',
      status: 'draft',
      issueDate: new Date('2023-06-10'),
      dueDate: new Date('2023-07-10'),
      total: 4800.00,
    },
  ],
  '2': [ // Stellar Web Services - moderate data (2 items)
    {
      id: 'INV-021',
      invoiceNumber: 'INV-SWS-001',
      clientName: 'Wayne Enterprises',
      clientId: '8',
      amount: '$1,800.25',
      status: 'overdue',
      issueDate: new Date('2023-04-10'),
      dueDate: new Date('2023-05-10'),
      total: 1800.25,
    },
    {
      id: 'INV-022',
      invoiceNumber: 'INV-SWS-002',
      clientName: 'Stark Industries',
      clientId: '7',
      amount: '$3,200.75',
      status: 'draft',
      issueDate: new Date('2023-06-02'),
      dueDate: new Date('2023-06-20'),
      total: 3200.75,
    },
  ],
  '3': [] // Green Tech Solutions - empty data (0 items)
};

// Tax options data - using proper TaxOption type
export const taxOptions: TaxOption[] = [
  {
    id: 'no-tax',
    name: 'No Tax',
    rate: 0,
    description: 'No tax applied',
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
  },
  {
    id: 'sales-tax-5',
    name: 'Sales Tax',
    rate: 5.0,
    description: '5% Sales Tax',
    region: 'State',
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
  },
  {
    id: 'sales-tax-7.5',
    name: 'Sales Tax',
    rate: 7.5,
    description: '7.5% Sales Tax',
    region: 'State',
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
  },
  {
    id: 'sales-tax-10',
    name: 'Sales Tax',
    rate: 10.0,
    description: '10% Sales Tax',
    region: 'State',
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
  },
  {
    id: 'vat-15',
    name: 'VAT',
    rate: 15.0,
    description: '15% VAT',
    region: 'Federal',
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
  },
  {
    id: 'vat-20',
    name: 'VAT',
    rate: 20.0,
    description: '20% VAT',
    region: 'Federal',
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
  },
  {
    id: 'gst-12',
    name: 'GST',
    rate: 12.0,
    description: '12% GST',
    region: 'Federal',
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
  },
  {
    id: 'gst-18',
    name: 'GST',
    rate: 18.0,
    description: '18% GST',
    region: 'Federal',
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
  },
];

// Dashboard data
export const dashboardData = {
  user: {
    name: 'Alex',
    companies: [
      {
        id: '1',
        name: 'Moonlight Design',
        nickname: 'MOON',
        logo: null,
        isDefault: true,
      },
      {
        id: '2',
        name: 'Stellar Web Services',
        nickname: 'SWS',
        logo: null,
        isDefault: false,
      },
      {
        id: '3',
        name: 'Green Tech Solutions',
        nickname: 'GTS',
        logo: null,
        isDefault: false,
      }
    ],
    activeCompanyId: '1', // Default to the first company
  },
  companyData: companyData
}; 