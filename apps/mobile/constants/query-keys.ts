// Re-export query keys from shared package
export { QUERY_KEYS } from '@repo/constants';

// Import for local use
import { QUERY_KEYS } from '@repo/constants';

// Legacy query key constants for backward compatibility
export const ORGANIZATIONS_KEY = QUERY_KEYS.ORGANIZATIONS;
export const ORGANIZATION_KEY = (id: string) => QUERY_KEYS.ORGANIZATION(id);

export const INVOICES_KEY = (organizationId: string) => QUERY_KEYS.INVOICES(organizationId);
export const INVOICE_KEY = (id: string, organizationId: string) => QUERY_KEYS.INVOICE(organizationId, id);

export const CLIENTS_KEY = (organizationId: string) => QUERY_KEYS.CLIENTS(organizationId);
export const CLIENT_KEY = (id: string, organizationId: string) => QUERY_KEYS.CLIENT(organizationId, id);

export const SERVICES_KEY = (organizationId: string) => QUERY_KEYS.SERVICES(organizationId);
export const SERVICE_KEY = (id: string, organizationId: string) => QUERY_KEYS.SERVICE(organizationId, id);

export const TAX_OPTIONS_KEY = ['tax-options'];
export const TAX_OPTION_KEY = (id: string) => ['tax-option', { id }];