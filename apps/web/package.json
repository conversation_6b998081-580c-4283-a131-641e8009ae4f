{"name": "web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@repo/constants": "workspace:*", "@repo/dtos": "workspace:*", "@repo/eslint-config": "workspace:*", "@repo/queries": "workspace:*", "@repo/schemas": "workspace:*", "@repo/shared": "workspace:*", "@repo/stores": "workspace:*", "@repo/typescript-config": "workspace:*", "@repo/ui": "workspace:*", "@repo/ui-interfaces": "workspace:*", "@repo/utils": "workspace:*", "@tanstack/react-query": "^5.79.0", "@tanstack/react-query-devtools": "^5.80.6", "react": "catalog:", "react-dom": "catalog:", "react-icons": "^5.5.0", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/eslint": "catalog:", "@types/node": "catalog:", "@types/react": "catalog:", "@types/react-dom": "catalog:", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10", "eslint": "catalog:", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8", "tailwindcss": "catalog:", "typescript": "catalog:", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}