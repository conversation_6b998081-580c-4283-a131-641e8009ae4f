/**
 * Web app color definitions - matching mobile app Colors.ts
 * Adapted for CSS variables and Tailwind usage
 */

// App theme color
const tintColorLight = '#3490f3';

// App theme colors (matching mobile)
export const colors = {
  // Primary colors - Blue theme matching mobile
  primary: '#3490f3',
  primaryLight: '#4da3f5',
  primaryLighter: '#66b6f7',
  primaryVeryLight: '#e6f3ff',
  
  // Status colors
  success: '#4CAF50',
  error: '#F44336',
  warning: '#FFC107',
  disabled: '#C7C7CC',
  
  // Background colors
  background: '#F7F8FA',
  cardBackground: '#FFFFFF',
  
  // Text colors
  text: {
    primary: '#222222',
    secondary: '#8E8E93',
    tertiary: '#C7C7CC',
    white: '#FFFFFF',
    dark: '#333333',
  },
  
  // Status backgrounds - matching mobile with blue theme
  statusBackground: {
    pending: '#e6f3ff',       // Very light blue for pending
    pendingIcon: '#cce7ff',   // Slightly darker blue for pending icon
    paid: '#EEFBF0',          // Green for paid status
    paidIcon: '#D5F2DD',      // Green for paid icon
    overdue: '#FEEEEE',       // Red for overdue status
    overdueIcon: '#FADADA',   // Red for overdue icon
  },
  
  // UI elements
  divider: '#F0F0F0',
  shadow: '#000000',
  transparentWhite: 'rgba(255,255,255,0.18)',
  buttonBackground: 'rgba(255,255,255,0.25)',
  avatarPlaceholder: '#F2F2F7',
  
  // Avatar backgrounds - matching mobile with blue theme
  avatarBackground: {
    primary: '#cce7ff',       // Light blue for primary avatars
    success: '#D5F2DD',       // Green for success
    error: '#FADADA',         // Red for error
    default: '#e6f3ff',       // Light blue for default
  },
};

// CSS custom properties for dynamic theming
export const cssVariables = {
  '--color-primary': colors.primary,
  '--color-primary-light': colors.primaryLight,
  '--color-primary-lighter': colors.primaryLighter,
  '--color-primary-very-light': colors.primaryVeryLight,
  '--color-success': colors.success,
  '--color-error': colors.error,
  '--color-warning': colors.warning,
  '--color-background': colors.background,
  '--color-card-background': colors.cardBackground,
  '--color-text-primary': colors.text.primary,
  '--color-text-secondary': colors.text.secondary,
  '--color-divider': colors.divider,
};

// Tailwind color utilities
export const tailwindColors = {
  'app-primary': colors.primary,
  'app-primary-light': colors.primaryLight,
  'app-primary-lighter': colors.primaryLighter,
  'app-primary-very-light': colors.primaryVeryLight,
  'app-success': colors.success,
  'app-error': colors.error,
  'app-warning': colors.warning,
  'app-background': colors.background,
  'app-card': colors.cardBackground,
  'app-text-primary': colors.text.primary,
  'app-text-secondary': colors.text.secondary,
  'app-text-tertiary': colors.text.tertiary,
  'app-divider': colors.divider,
  'app-pending': colors.statusBackground.pending,
  'app-paid': colors.statusBackground.paid,
  'app-overdue': colors.statusBackground.overdue,
};

// Status badge colors
export const statusColors = {
  pending: {
    bg: colors.statusBackground.pending,
    text: colors.primary,
    border: colors.statusBackground.pendingIcon,
  },
  paid: {
    bg: colors.statusBackground.paid,
    text: colors.success,
    border: colors.statusBackground.paidIcon,
  },
  overdue: {
    bg: colors.statusBackground.overdue,
    text: colors.error,
    border: colors.statusBackground.overdueIcon,
  },
}; 