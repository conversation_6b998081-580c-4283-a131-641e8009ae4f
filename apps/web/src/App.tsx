import { useState } from 'react'
import { DashboardLayout } from './components/layout/DashboardLayout'
import { DashboardOverview } from './components/dashboard/DashboardOverview'
import { InvoicesList } from './components/invoices/InvoicesList'
import { ClientsList } from './components/clients/ClientsList'

function App() {
  const [activeTab, setActiveTab] = useState('dashboard')

  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return <DashboardOverview />
      case 'invoices':
        return <InvoicesList />
      case 'clients':
        return <ClientsList />
      case 'services':
        return (
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Services</h2>
            <p className="text-muted-foreground">Coming soon - Service catalog management</p>
          </div>
        )
      case 'organizations':
        return (
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Organizations</h2>
            <p className="text-muted-foreground">Coming soon - Organization management</p>
          </div>
        )
      default:
        return <DashboardOverview />
    }
  }

  return (
    <DashboardLayout 
      activeTab={activeTab} 
      onTabChange={setActiveTab}
    >
      {renderContent()}
    </DashboardLayout>
  )
}

export default App
