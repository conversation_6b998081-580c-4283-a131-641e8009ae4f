import React, { useState } from 'react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle,
  Badge,
  Button,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Input,
  Avatar,
  AvatarFallback,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@repo/ui';
import { colors } from '../../constants/colors';

interface ClientsListProps {}

export const ClientsList: React.FC<ClientsListProps> = () => {
  const [searchTerm, setSearchTerm] = useState('');

  // Mock data - in real app this would come from shared stores/queries
  const clients = [
    {
      id: '1',
      name: 'Acme Corp',
      email: '<EMAIL>',
      phone: '+****************',
      totalInvoices: 5,
      totalRevenue: 12500.00,
      lastInvoice: '2024-01-15',
      status: 'active' as const,
    },
    {
      id: '2',
      name: 'TechStart LLC',
      email: '<EMAIL>',
      phone: '+****************',
      totalInvoices: 3,
      totalRevenue: 8400.00,
      lastInvoice: '2024-01-10',
      status: 'active' as const,
    },
    {
      id: '3',
      name: 'Creative Co.',
      email: '<EMAIL>',
      phone: '+****************',
      totalInvoices: 2,
      totalRevenue: 2850.00,
      lastInvoice: '2023-12-20',
      status: 'inactive' as const,
    },
    {
      id: '4',
      name: 'Digital Agency',
      email: '<EMAIL>',
      phone: '+****************',
      totalInvoices: 4,
      totalRevenue: 15200.00,
      lastInvoice: '2024-01-08',
      status: 'active' as const,
    },
    {
      id: '5',
      name: 'StartupCo',
      email: '<EMAIL>',
      phone: '+****************',
      totalInvoices: 1,
      totalRevenue: 1200.00,
      lastInvoice: '2023-12-28',
      status: 'active' as const,
    },
    {
      id: '6',
      name: 'E-commerce Ltd',
      email: '<EMAIL>',
      phone: '+****************',
      totalInvoices: 2,
      totalRevenue: 7500.00,
      lastInvoice: '2024-01-10',
      status: 'active' as const,
    },
  ];

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const getStatusBadge = (status: 'active' | 'inactive') => {
    return (
      <Badge 
        variant="outline"
        style={{
          backgroundColor: status === 'active' ? colors.statusBackground.paid : colors.statusBackground.pending,
          color: status === 'active' ? colors.success : colors.primary,
          borderColor: status === 'active' ? colors.statusBackground.paidIcon : colors.statusBackground.pendingIcon,
        }}
      >
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const filteredClients = clients.filter(client => 
    client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    client.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getClientStats = () => {
    const activeClients = clients.filter(c => c.status === 'active').length;
    const totalRevenue = clients.reduce((sum, c) => sum + c.totalRevenue, 0);
    const avgRevenue = totalRevenue / clients.length;
    
    return {
      total: clients.length,
      active: activeClients,
      totalRevenue,
      avgRevenue,
    };
  };

  const stats = getClientStats();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900">
            Clients
          </h1>
          <p className="text-muted-foreground">
            Manage your client relationships
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button 
            variant="outline"
          >
            📤 Export
          </Button>
          <Button 
            style={{ backgroundColor: colors.primary }}
            className="text-white hover:opacity-90"
          >
            👥 Add Client
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Clients</CardTitle>
            <span className="text-lg">👥</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Clients</CardTitle>
            <span className="text-lg">✅</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold" style={{ color: colors.success }}>
              {stats.active}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <span className="text-lg">💰</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold" style={{ color: colors.primary }}>
              {formatCurrency(stats.totalRevenue)}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Revenue</CardTitle>
            <span className="text-lg">📊</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold" style={{ color: colors.warning }}>
              {formatCurrency(stats.avgRevenue)}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Client List */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Client Directory</CardTitle>
              <CardDescription>
                {filteredClients.length} of {clients.length} clients
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <Input
                placeholder="Search clients..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-80"
              />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Client</TableHead>
                <TableHead>Contact</TableHead>
                <TableHead>Invoices</TableHead>
                <TableHead>Revenue</TableHead>
                <TableHead>Last Invoice</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredClients.map((client) => (
                <TableRow key={client.id}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <Avatar className="h-10 w-10">
                        <AvatarFallback style={{ backgroundColor: colors.avatarBackground.primary }}>
                          {getInitials(client.name)}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">{client.name}</div>
                        <div className="text-sm text-gray-500">ID: {client.id}</div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="text-sm">{client.email}</div>
                      <div className="text-sm text-gray-500">{client.phone}</div>
                    </div>
                  </TableCell>
                  <TableCell className="font-medium">
                    {client.totalInvoices}
                  </TableCell>
                  <TableCell className="font-medium">
                    {formatCurrency(client.totalRevenue)}
                  </TableCell>
                  <TableCell className="text-gray-600">
                    {new Date(client.lastInvoice).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(client.status)}
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          ⋮
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>👁️ View Details</DropdownMenuItem>
                        <DropdownMenuItem>✏️ Edit Client</DropdownMenuItem>
                        <DropdownMenuItem>📄 Create Invoice</DropdownMenuItem>
                        <DropdownMenuItem>📧 Send Email</DropdownMenuItem>
                        <DropdownMenuItem className="text-red-600">
                          🗑️ Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}; 