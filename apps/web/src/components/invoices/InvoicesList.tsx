import React, { useState } from 'react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle,
  Badge,
  Button,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@repo/ui';
import { colors, statusColors } from '../../constants/colors';

interface InvoicesListProps {}

export const InvoicesList: React.FC<InvoicesListProps> = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  // Mock data - in real app this would come from shared stores/queries
  const invoices = [
    {
      id: 'INV-001',
      client: 'Acme Corp',
      amount: 2500.00,
      status: 'paid' as const,
      dueDate: '2024-01-15',
      createdDate: '2024-01-01',
      description: 'Website Development Services'
    },
    {
      id: 'INV-002',
      client: 'TechStart LLC',
      amount: 1800.00,
      status: 'pending' as const,
      dueDate: '2024-01-20',
      createdDate: '2024-01-05',
      description: 'Mobile App Consultation'
    },
    {
      id: 'INV-003',
      client: 'Creative Co.',
      amount: 950.00,
      status: 'overdue' as const,
      dueDate: '2024-01-10',
      createdDate: '2023-12-20',
      description: 'Logo Design Package'
    },
    {
      id: 'INV-004',
      client: 'Digital Agency',
      amount: 3200.00,
      status: 'pending' as const,
      dueDate: '2024-01-25',
      createdDate: '2024-01-08',
      description: 'SEO Optimization Campaign'
    },
    {
      id: 'INV-005',
      client: 'StartupCo',
      amount: 1200.00,
      status: 'paid' as const,
      dueDate: '2024-01-12',
      createdDate: '2023-12-28',
      description: 'Brand Strategy Consultation'
    },
    {
      id: 'INV-006',
      client: 'E-commerce Ltd',
      amount: 4500.00,
      status: 'pending' as const,
      dueDate: '2024-01-30',
      createdDate: '2024-01-10',
      description: 'E-commerce Platform Development'
    },
  ];

  const getStatusBadge = (status: 'paid' | 'pending' | 'overdue') => {
    const statusStyle = statusColors[status];
    return (
      <Badge 
        variant="outline"
        style={{
          backgroundColor: statusStyle.bg,
          color: statusStyle.text,
          borderColor: statusStyle.border,
        }}
      >
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const filteredInvoices = invoices.filter(invoice => {
    const matchesSearch = 
      invoice.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.client.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || invoice.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const getStatusStats = () => {
    const stats = {
      all: invoices.length,
      pending: invoices.filter(i => i.status === 'pending').length,
      paid: invoices.filter(i => i.status === 'paid').length,
      overdue: invoices.filter(i => i.status === 'overdue').length,
    };
    return stats;
  };

  const stats = getStatusStats();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900">
            Invoices
          </h1>
          <p className="text-muted-foreground">
            Manage and track all your invoices
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button 
            variant="outline"
          >
            📤 Export
          </Button>
          <Button 
            style={{ backgroundColor: colors.primary }}
            className="text-white hover:opacity-90"
          >
            📄 Create Invoice
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Invoices</CardTitle>
            <span className="text-lg">📄</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.all}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
            <span className="text-lg">⏳</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold" style={{ color: colors.primary }}>
              {stats.pending}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Paid</CardTitle>
            <span className="text-lg">✅</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold" style={{ color: colors.success }}>
              {stats.paid}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Overdue</CardTitle>
            <span className="text-lg">⚠️</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold" style={{ color: colors.error }}>
              {stats.overdue}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Invoice List</CardTitle>
              <CardDescription>
                {filteredInvoices.length} of {invoices.length} invoices
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <Input
                placeholder="Search invoices..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-80"
              />
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="paid">Paid</SelectItem>
                  <SelectItem value="overdue">Overdue</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Invoice ID</TableHead>
                <TableHead>Client</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Amount</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Due Date</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredInvoices.map((invoice) => (
                <TableRow key={invoice.id}>
                  <TableCell className="font-medium">
                    {invoice.id}
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{invoice.client}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="max-w-xs truncate" title={invoice.description}>
                      {invoice.description}
                    </div>
                  </TableCell>
                  <TableCell className="font-medium">
                    {formatCurrency(invoice.amount)}
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(invoice.status)}
                  </TableCell>
                  <TableCell className={
                    invoice.status === 'overdue' 
                      ? 'text-red-600 font-medium' 
                      : 'text-gray-600'
                  }>
                    {new Date(invoice.dueDate).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          ⋮
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>👁️ View Details</DropdownMenuItem>
                        <DropdownMenuItem>✏️ Edit Invoice</DropdownMenuItem>
                        <DropdownMenuItem>📧 Send Reminder</DropdownMenuItem>
                        <DropdownMenuItem>📄 Duplicate</DropdownMenuItem>
                        <DropdownMenuItem className="text-red-600">
                          🗑️ Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}; 