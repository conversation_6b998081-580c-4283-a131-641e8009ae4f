import React from 'react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle,
  Badge,
  Button,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@repo/ui';
import { colors, statusColors } from '../../constants/colors';

interface DashboardOverviewProps {}

export const DashboardOverview: React.FC<DashboardOverviewProps> = () => {
  // Mock data - in real app this would come from shared stores/queries
  const stats = [
    {
      title: 'Total Revenue',
      value: '$45,231.89',
      change: '+20.1% from last month',
      changeType: 'positive' as const,
      icon: '💰',
    },
    {
      title: 'Active Invoices',
      value: '12',
      change: '+2 from last week',
      changeType: 'positive' as const,
      icon: '📄',
    },
    {
      title: 'Pending Payments',
      value: '$12,234.00',
      change: '+4 invoices pending',
      changeType: 'neutral' as const,
      icon: '⏳',
    },
    {
      title: 'Total Clients',
      value: '8',
      change: '+1 new client',
      changeType: 'positive' as const,
      icon: '👥',
    },
  ];

  const recentInvoices = [
    {
      id: 'INV-001',
      client: 'Acme Corp',
      amount: '$2,500.00',
      status: 'paid' as const,
      dueDate: '2024-01-15',
    },
    {
      id: 'INV-002',
      client: 'TechStart LLC',
      amount: '$1,800.00',
      status: 'pending' as const,
      dueDate: '2024-01-20',
    },
    {
      id: 'INV-003',
      client: 'Creative Co.',
      amount: '$950.00',
      status: 'overdue' as const,
      dueDate: '2024-01-10',
    },
    {
      id: 'INV-004',
      client: 'Digital Agency',
      amount: '$3,200.00',
      status: 'pending' as const,
      dueDate: '2024-01-25',
    },
  ];

  const getStatusBadge = (status: 'paid' | 'pending' | 'overdue') => {
    const statusStyle = statusColors[status];
    return (
      <Badge 
        variant="outline"
        style={{
          backgroundColor: statusStyle.bg,
          color: statusStyle.text,
          borderColor: statusStyle.border,
        }}
      >
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900">
            Dashboard
          </h1>
          <p className="text-muted-foreground">
            Welcome back! Here's what's happening with your business.
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button 
            style={{ backgroundColor: colors.primary }}
            className="text-white hover:opacity-90"
          >
            📄 Create Invoice
          </Button>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <span className="text-lg">{stat.icon}</span>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p 
                className={`text-xs ${
                  stat.changeType === 'positive' 
                    ? 'text-green-600' 
                    : stat.changeType === 'neutral'
                    ? 'text-gray-600'
                    : 'text-red-600'
                }`}
              >
                {stat.change}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Recent Activity */}
      <div className="grid gap-4 md:grid-cols-7">
        {/* Recent Invoices */}
        <Card className="col-span-4">
          <CardHeader>
            <CardTitle>Recent Invoices</CardTitle>
            <CardDescription>
              Your latest invoice activity
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Invoice</TableHead>
                  <TableHead>Client</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Due Date</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {recentInvoices.map((invoice) => (
                  <TableRow key={invoice.id}>
                    <TableCell className="font-medium">
                      {invoice.id}
                    </TableCell>
                    <TableCell>{invoice.client}</TableCell>
                    <TableCell>{invoice.amount}</TableCell>
                    <TableCell>
                      {getStatusBadge(invoice.status)}
                    </TableCell>
                    <TableCell className="text-gray-600">
                      {new Date(invoice.dueDate).toLocaleDateString()}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card className="col-span-3">
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common tasks and shortcuts
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button 
              variant="outline" 
              className="w-full justify-start text-left"
            >
              <span className="mr-2">📄</span>
              Create New Invoice
            </Button>
            <Button 
              variant="outline" 
              className="w-full justify-start text-left"
            >
              <span className="mr-2">👥</span>
              Add New Client
            </Button>
            <Button 
              variant="outline" 
              className="w-full justify-start text-left"
            >
              <span className="mr-2">🛠️</span>
              Create Service
            </Button>
            <Button 
              variant="outline" 
              className="w-full justify-start text-left"
            >
              <span className="mr-2">🏢</span>
              Switch Organization
            </Button>
            <Button 
              variant="outline" 
              className="w-full justify-start text-left"
            >
              <span className="mr-2">📊</span>
              View Reports
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Monthly Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Monthly Overview</CardTitle>
          <CardDescription>
            Your business performance this month
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold" style={{ color: colors.success }}>
                $28,450
              </div>
              <div className="text-sm text-gray-600">Revenue This Month</div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold" style={{ color: colors.primary }}>
                18
              </div>
              <div className="text-sm text-gray-600">Invoices Created</div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold" style={{ color: colors.warning }}>
                $3,200
              </div>
              <div className="text-sm text-gray-600">Outstanding</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}; 