import React from 'react';
import { 
  Card, 
  Button, 
  Avatar, 
  AvatarImage, 
  AvatarFallback,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  Badge
} from '@repo/ui';
import { colors } from '../../constants/colors';

interface DashboardLayoutProps {
  children: React.ReactNode;
  activeTab?: string;
  onTabChange?: (tab: string) => void;
}

export const DashboardLayout: React.FC<DashboardLayoutProps> = ({ 
  children, 
  activeTab = 'dashboard',
  onTabChange 
}) => {
  const navigationItems = [
    { 
      id: 'dashboard', 
      label: 'Dashboard', 
      icon: '📊',
      count: null 
    },
    { 
      id: 'invoices', 
      label: 'Invoices', 
      icon: '📄',
      count: 12 
    },
    { 
      id: 'clients', 
      label: 'Clients', 
      icon: '👥',
      count: 8 
    },
    { 
      id: 'services', 
      label: 'Services', 
      icon: '🛠️',
      count: 15 
    },
    { 
      id: 'organizations', 
      label: 'Organizations', 
      icon: '🏢',
      count: 3 
    },
  ];

  return (
    <div className="min-h-screen" style={{ backgroundColor: colors.background }}>
      {/* Header */}
      <header className="border-b bg-white shadow-sm">
        <div className="flex h-16 items-center justify-between px-6">
          <div className="flex items-center space-x-4">
            <h1 className="text-2xl font-bold" style={{ color: colors.primary }}>
              InvoiceGo
            </h1>
          </div>
          
          <div className="flex items-center space-x-4">
            <Badge 
              variant="outline" 
              className="hidden md:inline-flex"
              style={{ 
                backgroundColor: colors.statusBackground.paid,
                color: colors.success,
                borderColor: colors.statusBackground.paidIcon 
              }}
            >
              All Systems Operational
            </Badge>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src="/placeholder-avatar.jpg" alt="User" />
                    <AvatarFallback style={{ backgroundColor: colors.avatarBackground.primary }}>
                      JD
                    </AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56" align="end" forceMount>
                <DropdownMenuLabel className="font-normal">
                  <div className="flex flex-col space-y-1">
                    <p className="text-sm font-medium leading-none">John Doe</p>
                    <p className="text-xs leading-none text-muted-foreground">
                      <EMAIL>
                    </p>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem>Profile Settings</DropdownMenuItem>
                <DropdownMenuItem>Billing</DropdownMenuItem>
                <DropdownMenuItem>Team</DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem>Log out</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar */}
        <aside className="w-64 border-r bg-white h-[calc(100vh-4rem)]">
          <nav className="p-4 space-y-2">
            {navigationItems.map((item) => (
              <Button
                key={item.id}
                variant={activeTab === item.id ? "default" : "ghost"}
                className={`w-full justify-between text-left ${
                  activeTab === item.id 
                    ? 'text-white' 
                    : 'text-gray-700 hover:text-gray-900'
                }`}
                style={activeTab === item.id ? { backgroundColor: colors.primary } : {}}
                onClick={() => onTabChange?.(item.id)}
              >
                <div className="flex items-center space-x-3">
                  <span className="text-lg">{item.icon}</span>
                  <span>{item.label}</span>
                </div>
                {item.count && (
                  <Badge 
                    variant="secondary" 
                    className="ml-auto"
                    style={activeTab === item.id ? { 
                      backgroundColor: 'rgba(255,255,255,0.2)',
                      color: 'white'
                    } : {}}
                  >
                    {item.count}
                  </Badge>
                )}
              </Button>
            ))}
          </nav>
          
          {/* Organization Selector */}
          <div className="p-4 border-t mt-4">
            <div className="mb-3">
              <p className="text-sm font-medium text-gray-700">Current Organization</p>
            </div>
            <Card className="p-3">
              <div className="flex items-center space-x-3">
                <Avatar className="h-8 w-8">
                  <AvatarFallback style={{ backgroundColor: colors.avatarBackground.primary }}>
                    AC
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    Acme Corp
                  </p>
                  <p className="text-xs text-gray-500 truncate">
                    <EMAIL>
                  </p>
                </div>
              </div>
            </Card>
          </div>
        </aside>

        {/* Main Content */}
        <main className="flex-1 p-6">
          {children}
        </main>
      </div>
    </div>
  );
}; 