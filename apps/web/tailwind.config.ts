import type { Config } from "tailwindcss";
import baseConfig from "@repo/ui/tailwind.config";
import { tailwindColors } from "./src/constants/colors";

const config: Config = {
  ...baseConfig,
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
    "../../packages/ui/src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    ...baseConfig.theme,
    extend: {
      ...baseConfig.theme?.extend,
      colors: {
        ...baseConfig.theme?.extend?.colors,
        ...tailwindColors,
      },
    },
  },
};

export default config;