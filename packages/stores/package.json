{"name": "@repo/stores", "version": "0.0.0", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "exports": {".": "./src/index.ts", "./organization": "./src/organization/index.ts", "./user": "./src/user/index.ts", "./invoice": "./src/invoice/index.ts", "./client": "./src/client/index.ts", "./service": "./src/service/index.ts", "./settings": "./src/settings/index.ts"}, "scripts": {"lint": "eslint .", "type-check": "tsc --noEmit"}, "dependencies": {"@repo/schemas": "workspace:*", "@repo/constants": "workspace:*", "@repo/utils": "workspace:*", "zustand": "^5.0.2"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "eslint": "catalog:", "typescript": "catalog:"}}