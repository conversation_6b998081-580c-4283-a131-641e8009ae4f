import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { 
  InvoiceStore, 
  LineItem, 
  TaxMethod, 
  TaxConfiguration, 
  Attachment,
  InvoicePaymentMethods 
} from './types';

// Default tax options - can be overridden by apps
const defaultTaxOptions = [
  { id: 'tax_0', name: 'No Tax', rate: 0 },
  { id: 'tax_7_5', name: 'GST (7.5%)', rate: 7.5 },
  { id: 'tax_10', name: 'GST (10%)', rate: 10 },
  { id: 'tax_15', name: 'VAT (15%)', rate: 15 },
  { id: 'tax_20', name: 'VAT (20%)', rate: 20 },
];

// Helper functions
const defaultDueDate = () => {
  const date = new Date();
  date.setDate(date.getDate() + 30);
  return date;
};

const createEmptyLineItem = (): LineItem => ({
  id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
  description: '',
  quantity: '1',
  price: '0',
  total: '0.00',
});

// Tax options setter - can be called by apps to set their tax options
let taxOptions = defaultTaxOptions;
export const setTaxOptions = (options: typeof defaultTaxOptions) => {
  taxOptions = options;
};

export const getTaxOptions = () => taxOptions;

// Create the actual invoice store instance
export const useInvoiceStore = create<InvoiceStore>()(
  persist(
    (set, get) => ({
      // Initial state
      invoiceNumber: 'INV-ORG-001',
      invoiceDate: new Date(),
      dueDate: defaultDueDate(),
      selectedClientId: undefined,
      notes: '',
      terms: 'Payment due within 30 days.',
      lineItems: [createEmptyLineItem()],
      taxConfig: {
        method: TaxMethod.NONE,
        rate: 0,
        taxId: taxOptions.find(t => t.rate === 7.5)?.id || taxOptions[0]?.id || 'tax_0',
        inclusive: false,
      },
      attachments: [],
      notesExpanded: false,
      termsExpanded: false,
      paymentMethods: {
        selectedGatewayId: undefined,
        selectedCustomInstructionIds: [],
        isManualSelected: false,
      },

      // Basic setters
      setInvoiceNumber: (number: string) => set({ invoiceNumber: number }),
      setInvoiceDate: (date: Date) => set({ invoiceDate: date }),
      setDueDate: (date: Date) => set({ dueDate: date }),
      setSelectedClient: (clientId: string | undefined) => set({ selectedClientId: clientId }),
      setNotes: (notes: string) => set({ notes }),
      setTerms: (terms: string) => set({ terms }),
      setNotesExpanded: (expanded: boolean) => set({ notesExpanded: expanded }),
      setTermsExpanded: (expanded: boolean) => set({ termsExpanded: expanded }),
      
      // Tax configuration
      setTaxConfiguration: (config: Partial<TaxConfiguration>) => 
        set({ taxConfig: { ...get().taxConfig, ...config } }),
      
      setItemTaxable: (itemId: string, taxable: boolean) => {
        set((state) => ({
          lineItems: state.lineItems.map(item =>
            item.id === itemId ? { ...item, taxable } : item
          )
        }));
      },

      setItemTaxRate: (itemId: string, taxRate: number) => {
        set((state) => ({
          lineItems: state.lineItems.map(item =>
            item.id === itemId ? { ...item, taxRate } : item
          )
        }));
      },

      setItemSelectedTaxId: (itemId: string, taxId: string) => {
        set((state) => ({
          lineItems: state.lineItems.map(item =>
            item.id === itemId ? { ...item, selectedTaxId: taxId } : item
          )
        }));
      },

      // Line items management
      addLineItem: () => {
        set((state) => ({
          lineItems: [...state.lineItems, createEmptyLineItem()]
        }));
      },

      removeLineItem: (id: string) => {
        set((state) => ({
          lineItems: state.lineItems.filter(item => item.id !== id)
        }));
      },

      updateLineItem: (id: string, updates: Partial<LineItem>) => {
        set((state) => ({
          lineItems: state.lineItems.map(item => {
            if (item.id === id) {
              const updatedItem = { ...item, ...updates };
              
              // Auto-calculate total when quantity or price changes
              if (updates.quantity !== undefined || updates.price !== undefined) {
                const quantity = parseFloat(updatedItem.quantity || '0');
                const price = parseFloat(updatedItem.price || '0');
                updatedItem.total = (quantity * price).toFixed(2);
              }
              
              return updatedItem;
            }
            return item;
          })
        }));
      },

      moveLineItem: (fromIndex: number, toIndex: number) => {
        set((state) => {
          const newLineItems = [...state.lineItems];
          const [movedItem] = newLineItems.splice(fromIndex, 1);
          if (movedItem) {
            newLineItems.splice(toIndex, 0, movedItem);
          }
          return { lineItems: newLineItems };
        });
      },

      addServiceAsLineItem: (service: any) => {
        const serviceRate = service.pricing?.rate || service.rate || 0;
        const serviceUnit = service.pricing?.unit || service.unit || 'fixed';
        
        const newItem: LineItem = {
          id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          description: service.name,
          itemDescription: service.description,
          quantity: '1',
          price: serviceRate.toString(),
          total: serviceRate.toFixed(2),
          serviceId: service.id,
          unit: serviceUnit as 'fixed' | 'hour' | 'month' | 'custom',
        };
        
        set((state) => ({
          lineItems: [...state.lineItems, newItem]
        }));
      },

      // Attachments management
      addAttachment: (attachment: Omit<Attachment, 'id' | 'uploadedAt'>) => {
        const newAttachment: Attachment = {
          ...attachment,
          id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          uploadedAt: new Date(),
        };
        
        set((state) => ({
          attachments: [...state.attachments, newAttachment]
        }));
      },

      removeAttachment: (id: string) => {
        set((state) => ({
          attachments: state.attachments.filter(att => att.id !== id)
        }));
      },

      // Payment methods
      setPaymentMethods: (methods: Partial<InvoicePaymentMethods>) => {
        set((state) => ({
          paymentMethods: { ...state.paymentMethods, ...methods }
        }));
      },

      // Calculations
      getSubtotal: () => {
        const state = get();
        return state.lineItems.reduce((sum, item) => sum + parseFloat(item.total || '0'), 0);
      },

      getTaxAmount: () => {
        const state = get();
        const { taxConfig } = state;
        
        if (taxConfig.method === TaxMethod.NONE) return 0;
        
        if (taxConfig.method === TaxMethod.ON_TOTAL) {
          const subtotal = get().getSubtotal();
          return subtotal * (taxConfig.rate / 100);
        }
        
        if (taxConfig.method === TaxMethod.PER_ITEM) {
          return state.lineItems.reduce((sum, item) => {
            if (item.taxable !== false) {
              const itemTotal = parseFloat(item.total || '0');
              return sum + (itemTotal * (taxConfig.rate / 100));
            }
            return sum;
          }, 0);
        }
        
        return 0;
      },

      getItemTaxAmount: (itemId: string) => {
        const state = get();
        const { taxConfig } = state;
        const item = state.lineItems.find(i => i.id === itemId);
        
        if (!item || taxConfig.method === TaxMethod.NONE || item.taxable === false) {
          return 0;
        }
        
        if (taxConfig.method === TaxMethod.PER_ITEM) {
          const itemTotal = parseFloat(item.total || '0');
          return itemTotal * (taxConfig.rate / 100);
        }
        
        return 0;
      },

      getPreTaxAmount: () => {
        const state = get();
        const { taxConfig } = state;
        
        if (taxConfig.method === TaxMethod.AS_DEDUCTION) {
          const subtotal = get().getSubtotal();
          const taxAmount = subtotal * (taxConfig.rate / 100);
          return subtotal - taxAmount;
        }
        
        return get().getSubtotal();
      },

      getTotalBeforeTax: () => {
        return get().getSubtotal();
      },

      getTotal: () => {
        const subtotal = get().getSubtotal();
        const taxAmount = get().getTaxAmount();
        
        const state = get();
        if (state.taxConfig.method === TaxMethod.AS_DEDUCTION) {
          return subtotal - taxAmount;
        }
        
        return subtotal + taxAmount;
      },

      // Validation
      isFormValid: () => {
        const state = get();
        
        // Check required fields
        if (!state.invoiceNumber.trim()) return false;
        if (!state.selectedClientId) return false;
        
        // Check line items
        const validLineItems = state.lineItems.filter(item => 
          item.description.trim() && 
          parseFloat(item.quantity || '0') > 0 && 
          parseFloat(item.price || '0') >= 0
        );
        
        return validLineItems.length > 0;
      },

      // Load existing invoice data
      loadInvoiceData: (invoice: any) => {
        // Convert invoice data to store format
        const convertedLineItems: LineItem[] = invoice.lineItems?.map((item: any) => ({
          id: item.id || `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          description: item.description || '',
          itemDescription: item.itemDescription,
          quantity: item.quantity?.toString() || '1',
          price: item.price?.toString() || '0',
          total: item.total?.toString() || '0.00',
          serviceId: item.serviceId,
          unit: item.unit,
          taxable: item.taxable,
        })) || [createEmptyLineItem()];

        // Convert tax configuration
        const taxConfig: TaxConfiguration = {
          method: invoice.taxConfig?.method || TaxMethod.NONE,
          rate: invoice.taxConfig?.rate || 0,
          taxId: invoice.taxConfig?.taxId || taxOptions[0]?.id || 'tax_0',
          inclusive: invoice.taxConfig?.inclusive || false,
        };

        // Set all the invoice data
        set({
          invoiceNumber: invoice.invoiceNumber || 'INV-ORG-001',
          invoiceDate: invoice.issueDate ? new Date(invoice.issueDate) : new Date(),
          dueDate: invoice.dueDate ? new Date(invoice.dueDate) : defaultDueDate(),
          selectedClientId: invoice.clientId,
          notes: invoice.notes || '',
          terms: invoice.terms || 'Payment due within 30 days.',
          lineItems: convertedLineItems,
          taxConfig,
          attachments: invoice.attachments?.map((att: any) => ({
            id: att.id,
            name: att.name,
            size: att.size,
            type: att.type,
            uri: att.uri,
            uploadedAt: new Date(att.uploadedAt),
          })) || [],
          notesExpanded: false,
          termsExpanded: false,
          paymentMethods: {
            selectedGatewayId: invoice.paymentMethods?.selectedGatewayId,
            selectedCustomInstructionIds: invoice.paymentMethods?.selectedCustomInstructionIds || [],
            isManualSelected: false,
          },
        });
      },

      resetForm: () => {
        set({
          invoiceNumber: 'INV-ORG-001',
          invoiceDate: new Date(),
          dueDate: defaultDueDate(),
          selectedClientId: undefined,
          notes: '',
          terms: 'Payment due within 30 days.',
          lineItems: [createEmptyLineItem()],
          taxConfig: {
            method: TaxMethod.NONE,
            rate: 0,
            taxId: taxOptions.find(t => t.rate === 7.5)?.id || taxOptions[0]?.id || 'tax_0',
            inclusive: false,
          },
          attachments: [],
          notesExpanded: false,
          termsExpanded: false,
          paymentMethods: {
            selectedGatewayId: undefined,
            selectedCustomInstructionIds: [],
            isManualSelected: false,
          },
        });
      },

      // Generate next invoice number
      generateNextInvoiceNumber: (organizationNickname: string, invoiceCount: number) => {
        const nextNumber = (invoiceCount + 1).toString().padStart(3, '0');
        const invoiceNumber = `INV-${organizationNickname}-${nextNumber}`;
        set({ invoiceNumber });
        return invoiceNumber;
      },
    }),
    {
      name: "invoice",
      partialize: (state) => ({
        invoiceNumber: state.invoiceNumber,
        invoiceDate: state.invoiceDate,
        dueDate: state.dueDate,
        selectedClientId: state.selectedClientId,
        notes: state.notes,
        terms: state.terms,
        lineItems: state.lineItems,
        taxConfig: state.taxConfig,
        attachments: state.attachments,
        paymentMethods: state.paymentMethods,
      })
    }
  )
);

// Helper function to initialize store with tax options (for apps that need custom tax options)
export const initializeInvoiceStore = (taxOptions?: typeof defaultTaxOptions) => {
  if (taxOptions) {
    setTaxOptions(taxOptions);
  }
};
