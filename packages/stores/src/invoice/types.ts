// Invoice store types

// Tax method enum for better type safety
export enum TaxMethod {
  NONE = 'none',
  ON_TOTAL = 'on_total',
  PER_ITEM = 'per_item',
  AS_DEDUCTION = 'as_deduction'
}

// Line item interface
export interface LineItem {
  id: string;
  description: string;
  itemDescription?: string;
  quantity: string;
  price: string;
  total: string;
  serviceId?: string;
  unit?: 'fixed' | 'hour' | 'month' | 'custom';
  taxable?: boolean;
  discount?: string;
  discountType?: 'percentage' | 'fixed';
  taxRate?: number;
  selectedTaxId?: string;
}

// Tax configuration
export interface TaxConfiguration {
  method: TaxMethod;
  rate: number;
  taxId: string;
  inclusive: boolean;
}

// Attachment interface
export interface Attachment {
  id: string;
  name: string;
  size: number;
  type: string;
  uri: string;
  uploadedAt: Date;
}

// Invoice payment methods selection
export interface InvoicePaymentMethods {
  selectedGatewayId?: string;
  selectedCustomInstructionIds: string[];
  isManualSelected: boolean;
}

// Invoice state interface
export interface InvoiceState {
  // Form state
  invoiceNumber: string;
  invoiceDate: Date;
  dueDate: Date;
  selectedClientId: string | undefined;
  notes: string;
  terms: string;
  lineItems: LineItem[];
  taxConfig: TaxConfiguration;
  attachments: Attachment[];
  paymentMethods: InvoicePaymentMethods;
  
  // UI state
  notesExpanded: boolean;
  termsExpanded: boolean;
}

// Invoice actions interface
export interface InvoiceActions {
  // Basic setters
  setInvoiceNumber: (number: string) => void;
  setInvoiceDate: (date: Date) => void;
  setDueDate: (date: Date) => void;
  setSelectedClient: (clientId: string | undefined) => void;
  setNotes: (notes: string) => void;
  setTerms: (terms: string) => void;
  setNotesExpanded: (expanded: boolean) => void;
  setTermsExpanded: (expanded: boolean) => void;
  
  // Tax configuration
  setTaxConfiguration: (config: Partial<TaxConfiguration>) => void;
  setItemTaxable: (itemId: string, taxable: boolean) => void;
  
  // Line items management
  addLineItem: () => void;
  removeLineItem: (id: string) => void;
  updateLineItem: (id: string, updates: Partial<LineItem>) => void;
  moveLineItem: (fromIndex: number, toIndex: number) => void;
  addServiceAsLineItem: (service: any) => void;
  
  // Attachments management
  addAttachment: (attachment: Omit<Attachment, 'id' | 'uploadedAt'>) => void;
  removeAttachment: (id: string) => void;
  
  // Payment methods
  setPaymentMethods: (methods: Partial<InvoicePaymentMethods>) => void;
  
  // Calculations
  getSubtotal: () => number;
  getTaxAmount: () => number;
  getItemTaxAmount: (itemId: string) => number;
  getPreTaxAmount: () => number;
  getTotalBeforeTax: () => number;
  getTotal: () => number;
  
  // Validation
  isFormValid: () => boolean;
  
  // Load existing invoice data
  loadInvoiceData: (invoice: any) => void;
  
  // Reset
  resetForm: () => void;
  
  // Generate next invoice number
  generateNextInvoiceNumber: (organizationNickname: string, invoiceCount: number) => string;
}

// Combined store interface
export interface InvoiceStore extends InvoiceState, InvoiceActions {}
