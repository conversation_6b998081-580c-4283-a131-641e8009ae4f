import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { User, UserStore } from './types';

// Create the actual user store instance
export const useUserStore = create<UserStore>()(
  persist(
    (set) => ({
      // Initial state
      currentUser: null,
      isAuthenticated: false,
      isLoading: false,
        
        // Actions
        setCurrentUser: (user) => {
          set({ 
            currentUser: user,
            isAuthenticated: !!user,
          });
        },
        
        updateCurrentUser: (updates) => {
          set((state) => ({
            currentUser: state.currentUser 
              ? { ...state.currentUser, ...updates }
              : null
          }));
        },
        
        setAuthenticated: (authenticated) => {
          set({ isAuthenticated: authenticated });
        },
        
        setLoading: (isLoading) => set({ isLoading }),
        
        logout: () => {
          set({ 
            currentUser: null,
            isAuthenticated: false,
          });
        },
    }),
    {
      name: "user",
      partialize: (state) => ({
        currentUser: state.currentUser,
        isAuthenticated: state.isAuthenticated,
      })
    }
  )
);

// Helper function to initialize store with user data (for apps that need initial data)
export const initializeUserStore = (initialUser?: User | null, initialAuthenticated?: boolean) => {
  const { setCurrentUser, setAuthenticated } = useUserStore.getState();

  if (initialUser !== undefined) {
    setCurrentUser(initialUser);
  }

  if (initialAuthenticated !== undefined) {
    setAuthenticated(initialAuthenticated);
  }
};
