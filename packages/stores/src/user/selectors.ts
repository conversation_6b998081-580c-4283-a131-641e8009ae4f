import { UserStore } from './types';
import { useUserStore } from './store';

// Selector factory for user store
export const createUserSelectors = (useStore: () => UserStore) => {
  // Computed selectors for clean usage
  const useCurrentUserId = () => 
    useStore()?.currentUser?.id;

  const useIsAuthenticated = () => 
    useStore()?.isAuthenticated || false;

  const useCurrentUser = () =>
    useStore()?.currentUser;

  const useIsLoading = () =>
    useStore()?.isLoading || false;

  const useUserFullName = () => {
    const user = useStore()?.currentUser;
    return user ? `${user.firstName} ${user.lastName}` : '';
  };

  const useUserInitials = () => {
    const user = useStore()?.currentUser;
    return user ? `${user.firstName[0]}${user.lastName[0]}`.toUpperCase() : '';
  };

  return {
    useCurrentUserId,
    useIsAuthenticated,
    useCurrentUser,
    useIsLoading,
    useUserFullName,
    useUserInitials,
  };
};

// 🔥 CRITICAL: Direct selector hooks for services to import
export const useCurrentUserId = () => 
  useUserStore((state) => state.currentUser?.id);

export const useIsAuthenticated = () => 
  useUserStore((state) => state.isAuthenticated);

export const useCurrentUser = () =>
  useUserStore((state) => state.currentUser);

export const useUserIsLoading = () =>
  useUserStore((state) => state.isLoading);

export const useUserFullName = () => {
  return useUserStore((state) => {
    const user = state.currentUser;
    return user ? `${user.firstName} ${user.lastName}` : '';
  });
};

export const useUserInitials = () => {
  return useUserStore((state) => {
    const user = state.currentUser;
    return user ? `${user.firstName[0]}${user.lastName[0]}`.toUpperCase() : '';
  });
};
