import { OrganizationStore } from './types';
import { useOrganizationStore } from './store';

// Selector factory for organization store
export const createOrganizationSelectors = (useStore: () => OrganizationStore) => {
  // Computed selectors for clean usage
  const useActiveOrganizationId = () => 
    useStore()?.activeOrganization?.id;

  const useHasActiveOrganization = () => 
    !!useStore()?.activeOrganization;

  const useActiveOrganization = () =>
    useStore()?.activeOrganization;

  const useOrganizations = () =>
    useStore()?.organizations || [];

  const useIsLoading = () =>
    useStore()?.isLoading || false;

  const useOrganizationById = (id: string) =>
    useStore()?.organizations?.find(org => org.id === id);

  const useDashboardData = (organizationId?: string) => {
    const store = useStore();
    const id = organizationId || store?.activeOrganization?.id;
    return id ? store?.getDashboardData(id) : null;
  };

  const useActiveDashboardData = () => {
    const store = useStore();
    const activeId = store?.activeOrganization?.id;
    return activeId ? store?.getDashboardData(activeId) : null;
  };

  return {
    useActiveOrganizationId,
    useHasActiveOrganization,
    useActiveOrganization,
    useOrganizations,
    useIsLoading,
    useOrganizationById,
    useDashboardData,
    useActiveDashboardData,
  };
};

// 🔥 CRITICAL: Direct selector hooks for services to import
// These are the actual hooks that services need
export const useActiveOrganizationId = () => 
  useOrganizationStore((state) => state.activeOrganization?.id);

export const useHasActiveOrganization = () => 
  useOrganizationStore((state) => !!state.activeOrganization);

export const useActiveOrganization = () =>
  useOrganizationStore((state) => state.activeOrganization);

export const useOrganizations = () =>
  useOrganizationStore((state) => state.organizations);

export const useOrganizationIsLoading = () =>
  useOrganizationStore((state) => state.isLoading);

export const useOrganizationById = (id: string) =>
  useOrganizationStore((state) => state.organizations.find(org => org.id === id));

export const useDashboardData = (organizationId?: string) => {
  return useOrganizationStore((state) => {
    const id = organizationId || state.activeOrganization?.id;
    return id ? state.getDashboardData(id) : null;
  });
};

export const useActiveDashboardData = () => {
  return useOrganizationStore((state) => {
    const activeId = state.activeOrganization?.id;
    return activeId ? state.getDashboardData(activeId) : null;
  });
};
