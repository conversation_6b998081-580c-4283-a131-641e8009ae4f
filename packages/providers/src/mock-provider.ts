// @ts-nocheck
// Mock provider implementation - type safety relaxed for mock data
import { 
  AppDefaults, 
  BusinessTypeTemplate, 
  SupportedCurrency, 
  UpdateAppDefaultsInput,
  CreateFeedbackInput, 
  Feedback, 
  FeedbackCategory, 
  UpdateFeedbackInput,
  CreateInvoiceTemplateInput, 
  InvoiceTemplate, 
  UpdateInvoiceTemplateInput,
  Subscription, 
  SubscriptionChangeRequest, 
  SubscriptionPlan,
  ContactFormInput, 
  ContactSubmission, 
  FAQ, 
  HelpArticle, 
  HelpCategory, 
  KnowledgeBaseSearchInput, 
  SearchResult,
  SecuritySettings, 
  UpdateSecuritySettingsInput, 
  UpdateUserProfileInput, 
  UserProfile,
  Client, 
  ClientActivity,
  Invoice, 
  InvoiceActivity,
  CreateOrganizationForm as CreateOrganizationInput, 
  Organization, 
  UpdateOrganizationForm as UpdateOrganizationInput,
  CreateCustomPaymentInstructionInput,
  CustomPaymentInstruction,
  OrganizationPaymentMethods,
  UpdateCustomPaymentInstructionInput,
  UpdateOrganizationPaymentMethodsInput,
  CreatePayment,
  Payment,
  PaymentSummary,
  Service, 
  ServiceActivity,
  TaxOption
} from '@repo/schemas';
import { MockDatabase } from '@repo/testing';
import {
  CreateClientInput,
  CreateInvoiceInput,
  CreateServiceInput,
  IApiProvider,
} from './api-provider-interface';

// Settings imports

export class MockProvider implements IApiProvider {
  connectStripeAccount(organizationId: string, authCode: string): Promise<string> {
    throw new Error('Method not implemented.');
  }
  connectPayPalAccount(organizationId: string, authCode: string): Promise<string> {
    throw new Error('Method not implemented.');
  }
  disconnectPaymentGateway(organizationId: string, gatewayId: string): Promise<void> {
    throw new Error('Method not implemented.');
  }
  createPaymentLink(invoiceId: string): Promise<string> {
    throw new Error('Method not implemented.');
  }
  // Simulate network delay
  private delay = (ms: number = 200) => new Promise(resolve => setTimeout(resolve, ms));

  // Helper method to generate unique IDs for activities
  private generateActivityId(prefix: string): string {
    return `${prefix}${Date.now()}${Math.random().toString(36).substr(2, 3).toUpperCase()}`;
  }

  // Helper method to create invoice activity
  private createInvoiceActivity(
    invoiceId: string, 
    organizationId: string, 
    type: string, 
    description: string, 
    metadata?: any
  ) {
    const activity: InvoiceActivity = {
      id: this.generateActivityId('IA'),
      invoiceId,
      organizationId,
      type: type as any,
      description,
      metadata,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    MockDatabase.invoiceActivities.push(activity);
    return activity;
  }

  // Helper method to create client activity
  private createClientActivity(
    clientId: string, 
    organizationId: string, 
    type: string, 
    description: string, 
    metadata?: any
  ) {
    const activity: ClientActivity = {
      id: this.generateActivityId('CA'),
      clientId,
      organizationId,
      type: type as any,
      description,
      metadata,
      createdAt: new Date(),
    };
    MockDatabase.clientActivities.push(activity);
    return activity;
  }

  // Helper method to create service activity
  private createServiceActivity(
    serviceId: string, 
    organizationId: string, 
    type: string, 
    description: string, 
    metadata?: any
  ) {
    const activity: ServiceActivity = {
      id: this.generateActivityId('SA'),
      serviceId,
      organizationId,
      type: type as any,
      description,
      metadata,
      createdAt: new Date(),
    };
    MockDatabase.serviceActivities.push(activity);
    return activity;
  }

  // Organizations
  async getOrganizations(): Promise<Organization[]> {
    await this.delay();
    return [...MockDatabase.organizations];
  }

  async getOrganization(id: string): Promise<Organization> {
    await this.delay();
    const organization = MockDatabase.organizations.find(org => org.id === id);
    if (!organization) {
      throw new Error(`Organization with id ${id} not found`);
    }
    return organization;
  }

  async createOrganization(organization: CreateOrganizationInput): Promise<Organization> {
    await this.delay(300);
    
    const newOrganization: Organization = {
      ...organization,
      id: `${Date.now()}`,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    MockDatabase.organizations.push(newOrganization);
    return newOrganization;
  }

  async updateOrganization(id: string, updates: UpdateOrganizationInput): Promise<Organization> {
    await this.delay(250);
    
    const index = MockDatabase.organizations.findIndex(org => org.id === id);
    
    if (index === -1) {
      throw new Error(`Organization with id ${id} not found`);
    }
    
    MockDatabase.organizations[index] = {
      ...MockDatabase.organizations[index],
      ...updates,
      updatedAt: new Date(),
    };
    
    return MockDatabase.organizations[index];
  }

  // Helper method to generate nickname from organization name
  private generateNickname(name: string): string {
    return name
      .split(' ')
      .map(word => word.charAt(0).toUpperCase())
      .join('')
      .slice(0, 4);
  }

  // Invoices
  async getInvoices(organizationId: string): Promise<Invoice[]> {
    await this.delay();
    return MockDatabase.invoices
      .filter(invoice => invoice.organizationId === organizationId)
      .sort((a, b) => new Date(b.issueDate).getTime() - new Date(a.issueDate).getTime());
  }

  async getInvoice(organizationId: string, invoiceId: string): Promise<Invoice> {
    await this.delay();
    const invoice = MockDatabase.invoices.find(
      inv => inv.id === invoiceId && inv.organizationId === organizationId
    );
    if (!invoice) {
      throw new Error(`Invoice with id ${invoiceId} not found for organization ${organizationId}`);
    }
    return invoice;
  }

  async createInvoice(organizationId: string, invoice: CreateInvoiceInput): Promise<Invoice> {
    await this.delay(300); // Slightly longer delay for create operations
    
    const newInvoice: Invoice = {
      ...invoice,
      id: `INV-${Date.now()}${Math.random().toString(36).substr(2, 3).toUpperCase()}`,
      organizationId,
      createdAt: new Date(),
      updatedAt: new Date(),
      comments: [],
      attachments: [],
    };
    
    MockDatabase.invoices.push(newInvoice);

    // Create invoice activity
    this.createInvoiceActivity(
      newInvoice.id,
      organizationId,
      'created',
      'Invoice created'
    );

    // Create client activity for invoice usage
    if (newInvoice.clientId) {
      this.createClientActivity(
        newInvoice.clientId,
        organizationId,
        'used_in_invoice',
        `Appeared in invoice ${newInvoice.invoiceNumber}`,
        {
          invoiceId: newInvoice.id,
          invoiceNumber: newInvoice.invoiceNumber,
          amount: newInvoice.totals?.total || 0,
          status: newInvoice.status,
        }
      );
    }

    // Create service activities for each line item that has a serviceId
    if (newInvoice.lineItems) {
      newInvoice.lineItems.forEach((lineItem: any) => {
        if (lineItem.serviceId) {
          // Find the service to get its name for the description
          const service = MockDatabase.services.find(s => s.id === lineItem.serviceId);
          const serviceName = service?.name || lineItem.description;
          
          this.createServiceActivity(
            lineItem.serviceId,
            organizationId,
            'used_in_invoice',
            `Used in invoice ${newInvoice.invoiceNumber} for ${newInvoice.clientName}`,
            {
              invoiceId: newInvoice.id,
              invoiceNumber: newInvoice.invoiceNumber,
              clientName: newInvoice.clientName,
              amount: lineItem.total,
            }
          );
        }
      });
    }
    
    return newInvoice;
  }

  async updateInvoice(organizationId: string, invoiceId: string, updates: Partial<Invoice>): Promise<Invoice> {
    await this.delay(250);
    
    const index = MockDatabase.invoices.findIndex(
      inv => inv.id === invoiceId && inv.organizationId === organizationId
    );
    
    if (index === -1) {
      throw new Error(`Invoice with id ${invoiceId} not found for organization ${organizationId}`);
    }
    
    const oldInvoice = { ...MockDatabase.invoices[index] };
    
    MockDatabase.invoices[index] = {
      ...MockDatabase.invoices[index],
      ...updates,
      updatedAt: new Date(),
    };

    // Create activity for status changes
    if (updates.status && updates.status !== oldInvoice.status) {
      this.createInvoiceActivity(
        invoiceId,
        organizationId,
        'status_changed',
        `Status changed from ${oldInvoice.status} to ${updates.status}`,
        {
          oldStatus: oldInvoice.status,
          newStatus: updates.status,
        }
      );
    }

    // Create activity for other updates
    if (Object.keys(updates).length > 1 || (Object.keys(updates).length === 1 && !updates.status)) {
      this.createInvoiceActivity(
        invoiceId,
        organizationId,
        'edited',
        'Invoice details updated'
      );
    }
    
    return MockDatabase.invoices[index];
  }

  async deleteInvoice(organizationId: string, invoiceId: string): Promise<void> {
    await this.delay(200);
    
    const index = MockDatabase.invoices.findIndex(
      inv => inv.id === invoiceId && inv.organizationId === organizationId
    );
    
    if (index === -1) {
      throw new Error(`Invoice with id ${invoiceId} not found for organization ${organizationId}`);
    }
    
    MockDatabase.invoices.splice(index, 1);
  }

  async getInvoiceActivities(organizationId: string, invoiceId: string): Promise<InvoiceActivity[]> {
    await this.delay(150);
    return MockDatabase.invoiceActivities
      .filter(activity => activity.invoiceId === invoiceId && activity.organizationId === organizationId)
      .sort((a, b) => {
        const aDate = a.createdAt || new Date(0);
        const bDate = b.createdAt || new Date(0);
        return bDate.getTime() - aDate.getTime();
      }); // Most recent first
  }

  // Clients
  async getClients(organizationId: string): Promise<Client[]> {
    await this.delay();
    return MockDatabase.clients
      .filter(client => client.organizationId === organizationId)
      .sort((a, b) => a.name.localeCompare(b.name));
  }

  async getClient(organizationId: string, clientId: string): Promise<Client> {
    await this.delay();
    const client = MockDatabase.clients.find(
      c => c.id === clientId && c.organizationId === organizationId
    );
    if (!client) {
      throw new Error(`Client with id ${clientId} not found for organization ${organizationId}`);
    }
    return client;
  }

  async createClient(organizationId: string, client: CreateClientInput): Promise<Client> {
    await this.delay(300);
    
    const newClient: Client = {
      ...client,
      id: `${Date.now()}`,
      organizationId,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    MockDatabase.clients.push(newClient);

    // Create client activity for creation
    this.createClientActivity(
      newClient.id,
      organizationId,
      'created',
      'Client added to organization'
    );
    
    return newClient;
  }

  async updateClient(organizationId: string, clientId: string, updates: Partial<Client>): Promise<Client> {
    await this.delay(250);
    
    const index = MockDatabase.clients.findIndex(
      c => c.id === clientId && c.organizationId === organizationId
    );
    
    if (index === -1) {
      throw new Error(`Client with id ${clientId} not found for organization ${organizationId}`);
    }
    
    const oldClient = { ...MockDatabase.clients[index] };
    
    MockDatabase.clients[index] = {
      ...MockDatabase.clients[index],
      ...updates,
      updatedAt: new Date(),
    };

    // Create specific activities for different types of updates
    if (updates.contact && JSON.stringify(updates.contact) !== JSON.stringify(oldClient.contact)) {
      this.createClientActivity(
        clientId,
        organizationId,
        'contact_updated',
        'Contact information updated',
        {
          field: 'contact',
          oldValue: oldClient.contact,
          newValue: updates.contact,
        }
      );
    } else if (updates.address && JSON.stringify(updates.address) !== JSON.stringify(oldClient.address)) {
      this.createClientActivity(
        clientId,
        organizationId,
        'address_updated',
        'Address information updated',
        {
          field: 'address',
          oldValue: oldClient.address,
          newValue: updates.address,
        }
      );
    } else if (updates.isActive !== undefined && updates.isActive !== oldClient.isActive) {
      this.createClientActivity(
        clientId,
        organizationId,
        updates.isActive ? 'activated' : 'deactivated',
        `Client ${updates.isActive ? 'activated' : 'deactivated'}`
      );
    } else {
      this.createClientActivity(
        clientId,
        organizationId,
        'updated',
        'Client information updated'
      );
    }
    
    return MockDatabase.clients[index];
  }

  async deleteClient(organizationId: string, clientId: string): Promise<void> {
    await this.delay(200);
    
    const index = MockDatabase.clients.findIndex(
      c => c.id === clientId && c.organizationId === organizationId
    );
    
    if (index === -1) {
      throw new Error(`Client with id ${clientId} not found for organization ${organizationId}`);
    }
    
    MockDatabase.clients.splice(index, 1);
  }

  async getClientActivities(organizationId: string, clientId: string): Promise<ClientActivity[]> {
    await this.delay(150);
    return MockDatabase.clientActivities
      .filter(activity => activity.clientId === clientId && activity.organizationId === organizationId)
      .sort((a, b) => {
        const aDate = a.createdAt || new Date(0);
        const bDate = b.createdAt || new Date(0);
        return bDate.getTime() - aDate.getTime();
      }); // Most recent first
  }

  // Services
  async getServices(organizationId: string): Promise<Service[]> {
    await this.delay();
    return MockDatabase.services
      .filter(service => service.organizationId === organizationId)
      .sort((a, b) => a.name.localeCompare(b.name));
  }

  async getService(organizationId: string, serviceId: string): Promise<Service> {
    await this.delay();
    const service = MockDatabase.services.find(
      s => s.id === serviceId && s.organizationId === organizationId
    );
    if (!service) {
      throw new Error(`Service with id ${serviceId} not found for organization ${organizationId}`);
    }
    return service;
  }

  async createService(organizationId: string, service: CreateServiceInput): Promise<Service> {
    await this.delay(300);
    
    const newService: Service = {
      ...service,
      id: `S${Date.now()}`,
      organizationId,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    MockDatabase.services.push(newService);

    // Create service activity for creation
    this.createServiceActivity(
      newService.id,
      organizationId,
      'created',
      'Service created'
    );
    
    return newService;
  }

  async updateService(organizationId: string, serviceId: string, updates: Partial<Service>): Promise<Service> {
    await this.delay(250);
    
    const index = MockDatabase.services.findIndex(
      s => s.id === serviceId && s.organizationId === organizationId
    );
    
    if (index === -1) {
      throw new Error(`Service with id ${serviceId} not found for organization ${organizationId}`);
    }
    
    const oldService = { ...MockDatabase.services[index] };
    
    MockDatabase.services[index] = {
      ...MockDatabase.services[index],
      ...updates,
      updatedAt: new Date(),
    };

    // Create specific activities for different types of updates
    if (updates.pricing && JSON.stringify(updates.pricing) !== JSON.stringify(oldService.pricing)) {
      this.createServiceActivity(
        serviceId,
        organizationId,
        'pricing_changed',
        `Pricing updated from ${oldService.pricing?.rate}/${oldService.pricing?.unit} to ${updates.pricing.rate}/${updates.pricing.unit}`,
        {
          field: 'pricing',
          oldValue: oldService.pricing,
          newValue: updates.pricing,
        }
      );
    } else if (updates.isActive !== undefined && updates.isActive !== oldService.isActive) {
      this.createServiceActivity(
        serviceId,
        organizationId,
        updates.isActive ? 'activated' : 'deactivated',
        `Service ${updates.isActive ? 'activated' : 'deactivated'}`
      );
    } else {
      this.createServiceActivity(
        serviceId,
        organizationId,
        'updated',
        'Service information updated'
      );
    }
    
    return MockDatabase.services[index];
  }

  async deleteService(organizationId: string, serviceId: string): Promise<void> {
    await this.delay(200);
    
    const index = MockDatabase.services.findIndex(
      s => s.id === serviceId && s.organizationId === organizationId
    );
    
    if (index === -1) {
      throw new Error(`Service with id ${serviceId} not found for organization ${organizationId}`);
    }
    
    MockDatabase.services.splice(index, 1);
  }

  async getServiceActivities(organizationId: string, serviceId: string): Promise<ServiceActivity[]> {
    await this.delay(150);
    return MockDatabase.serviceActivities
      .filter(activity => activity.serviceId === serviceId && activity.organizationId === organizationId)
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime()); // Most recent first
  }

  // Tax Options
  async getTaxOptions(): Promise<TaxOption[]> {
    await this.delay();
    return [...MockDatabase.taxOptions];
  }

  async getTaxOption(id: string): Promise<TaxOption> {
    await this.delay();
    const taxOption = MockDatabase.taxOptions.find(tax => tax.id === id);
    if (!taxOption) {
      throw new Error(`Tax option with id ${id} not found`);
    }
    return taxOption;
  }

  // Payment operations
  async getInvoicePayments(organizationId: string, invoiceId: string) {
    return MockDatabase.payments.filter(
      payment => payment.invoiceId === invoiceId && payment.organizationId === organizationId
    );
  }

  async getPaymentSummary(organizationId: string, invoiceId: string) {
    const payments = await this.getInvoicePayments(organizationId, invoiceId);
    
    const paymentTransactions = payments.filter(p => p.type === 'payment' && p.status === 'completed');
    const refundTransactions = payments.filter(p => p.type === 'refund' && p.status === 'completed');
    
    const totalPaid = paymentTransactions.reduce((sum, p) => sum + p.amount, 0);
    const totalRefunded = refundTransactions.reduce((sum, p) => sum + Math.abs(p.amount), 0);
    const netPaid = totalPaid - totalRefunded;
    
    const lastPayment = paymentTransactions.sort((a, b) => 
      new Date(b.paymentDate).getTime() - new Date(a.paymentDate).getTime()
    )[0];
    
    return {
      totalPaid,
      totalRefunded,
      netPaid,
      lastPaymentDate: lastPayment?.paymentDate,
      lastPaymentAmount: lastPayment?.amount,
      lastPaymentMethod: lastPayment?.method,
      paymentCount: paymentTransactions.length,
      refundCount: refundTransactions.length,
    };
  }

  async createPayment(payment: any) {
    await this.delay(250);
    
    const newPayment = {
      ...payment,
      id: `PAY-${Date.now()}`,
      status: 'completed' as const,
      processedAt: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    MockDatabase.payments.push(newPayment);

    // Create invoice activity for payment
    const isRefund = newPayment.type === 'refund';
    const activityType = isRefund ? 'refunded' : 'paid';
    const actionDescription = isRefund 
      ? `Refund processed - ${newPayment.method}`
      : `Payment received - ${newPayment.method}`;

    this.createInvoiceActivity(
      newPayment.invoiceId,
      newPayment.organizationId,
      activityType,
      actionDescription,
      {
        paymentMethod: newPayment.method,
        amount: newPayment.amount,
        transactionId: newPayment.transactionId,
        reference: newPayment.reference,
        paymentId: newPayment.id,
      }
    );
    
    return newPayment;
  }

  async updatePayment(organizationId: string, paymentId: string, updates: Partial<Payment>): Promise<Payment> {
    await this.delay(250);
    
    const index = MockDatabase.payments.findIndex(
      p => p.id === paymentId && p.organizationId === organizationId
    );
    
    if (index === -1) {
      throw new Error('Payment not found');
    }
    
    const updatedPayment = {
      ...MockDatabase.payments[index],
      ...updates,
      updatedAt: new Date(),
    };
    
    MockDatabase.payments[index] = updatedPayment;
    return updatedPayment;
  }

  async deletePayment(organizationId: string, paymentId: string) {
    const index = MockDatabase.payments.findIndex(
      p => p.id === paymentId && p.organizationId === organizationId
    );
    
    if (index === -1) {
      throw new Error('Payment not found');
    }
    
    MockDatabase.payments.splice(index, 1);
  }

  // Payment Methods
  async getOrganizationPaymentMethods(organizationId: string): Promise<OrganizationPaymentMethods | null> {
    await this.delay();
    return MockDatabase.organizationPaymentMethods.find(
      config => config.organizationId === organizationId
    ) || null;
  }

  async updateOrganizationPaymentMethods(input: UpdateOrganizationPaymentMethodsInput): Promise<OrganizationPaymentMethods> {
    await this.delay(250);
    
    const index = MockDatabase.organizationPaymentMethods.findIndex(
      config => config.organizationId === input.organizationId
    );
    
    if (index === -1) {
      // Create new configuration if doesn't exist
      const newConfig: OrganizationPaymentMethods = {
        organizationId: input.organizationId,
        activeGateway: input.activeGateway,
        paymentGateways: input.paymentGateways || [],
        customInstructions: input.customInstructions || [],
        allowPartialPayments: input.allowPartialPayments !== undefined ? input.allowPartialPayments : true,
        autoCreatePaymentLinks: input.autoCreatePaymentLinks !== undefined ? input.autoCreatePaymentLinks : true,
        updatedAt: new Date(),
      };
      MockDatabase.organizationPaymentMethods.push(newConfig);
      return newConfig;
    } else {
      // Update existing configuration
      MockDatabase.organizationPaymentMethods[index] = {
        ...MockDatabase.organizationPaymentMethods[index],
        ...input,
        updatedAt: new Date(),
      };
      return MockDatabase.organizationPaymentMethods[index];
    }
  }

  async getCustomPaymentInstructions(organizationId: string): Promise<CustomPaymentInstruction[]> {
    await this.delay();
    return MockDatabase.customPaymentInstructions
      .filter(instruction => instruction.organizationId === organizationId && instruction.isActive)
      .sort((a, b) => a.sortOrder - b.sortOrder);
  }

  async createCustomPaymentInstruction(input: CreateCustomPaymentInstructionInput): Promise<CustomPaymentInstruction> {
    await this.delay(300);
    
    const newInstruction: CustomPaymentInstruction = {
      ...input,
      id: `cpi_${Date.now()}${Math.random().toString(36).substr(2, 3)}`,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    MockDatabase.customPaymentInstructions.push(newInstruction);
    return newInstruction;
  }

  async updateCustomPaymentInstruction(input: UpdateCustomPaymentInstructionInput): Promise<CustomPaymentInstruction> {
    await this.delay(250);
    
    const index = MockDatabase.customPaymentInstructions.findIndex(
      instruction => instruction.id === input.id
    );
    
    if (index === -1) {
      throw new Error(`Custom payment instruction with id ${input.id} not found`);
    }
    
    MockDatabase.customPaymentInstructions[index] = {
      ...MockDatabase.customPaymentInstructions[index],
      ...input,
      updatedAt: new Date(),
    };
    
    return MockDatabase.customPaymentInstructions[index];
  }

  async deleteCustomPaymentInstruction(instructionId: string): Promise<void> {
    await this.delay(200);
    
    const index = MockDatabase.customPaymentInstructions.findIndex(
      instruction => instruction.id === instructionId
    );
    
    if (index === -1) {
      throw new Error(`Custom payment instruction with id ${instructionId} not found`);
    }
    
    // Remove from database
    MockDatabase.customPaymentInstructions.splice(index, 1);
    
    // Also remove from any organization payment methods that reference it
    MockDatabase.organizationPaymentMethods.forEach(config => {
      config.customInstructions = config.customInstructions.filter(id => id !== instructionId);
    });
  }

  // ============================================
  // SETTINGS METHODS - Phase 1 Implementation
  // ============================================

  // User Profile Management
  async getUserProfile(userId: string): Promise<UserProfile | null> {
    await this.delay();
    return MockDatabase.userProfiles.find(profile => profile.id === userId) || null;
  }

  async updateUserProfile(userId: string, updates: UpdateUserProfileInput): Promise<UserProfile> {
    await this.delay(250);
    
    const index = MockDatabase.userProfiles.findIndex(profile => profile.id === userId);
    if (index === -1) {
      throw new Error('User profile not found');
    }
    
    MockDatabase.userProfiles[index] = {
      ...MockDatabase.userProfiles[index],
      ...updates,
      updatedAt: new Date(),
    };
    
    return MockDatabase.userProfiles[index];
  }

  async uploadAvatar(userId: string, avatarFile: { uri: string; type: string; name: string; size: number }): Promise<string> {
    await this.delay(1000); // Simulate upload time
    
    // Mock upload to cloud storage - in real app would upload to S3/CloudFlare/etc
    const avatarUrl = `https://api.invoiceplus.com/avatars/${userId}/${Date.now()}.jpg`;
    
    // Update user profile with new avatar URL
    await this.updateUserProfile(userId, { avatar: avatarUrl });
    
    return avatarUrl;
  }

  // Security Settings
  async getSecuritySettings(userId: string): Promise<SecuritySettings | null> {
    await this.delay();
    return MockDatabase.securitySettings.find(settings => settings.userId === userId) || null;
  }

  async updateSecuritySettings(userId: string, updates: UpdateSecuritySettingsInput): Promise<SecuritySettings> {
    await this.delay(250);
    
    const index = MockDatabase.securitySettings.findIndex(settings => settings.userId === userId);
    if (index === -1) {
      throw new Error('Security settings not found');
    }
    
    MockDatabase.securitySettings[index] = {
      ...MockDatabase.securitySettings[index],
      ...updates,
      updatedAt: new Date(),
    };
    
    return MockDatabase.securitySettings[index];
  }

  // User Security (alias methods for getUserSecurity services)
  async getUserSecurity(userId: string): Promise<SecuritySettings | null> {
    return this.getSecuritySettings(userId);
  }

  async updateUserSecurity(userId: string, updates: UpdateSecuritySettingsInput): Promise<SecuritySettings> {
    return this.updateSecuritySettings(userId, updates);
  }

  async enableTwoFactor(userId: string): Promise<{ secret: string; qrCode: string }> {
    await this.delay(300);
    
    // Mock two-factor setup
    const secret = `JBSWY3DPEHPK3PXP${Date.now()}`;
    const qrCode = `data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==`;
    
    return { secret, qrCode };
  }

  async verifyTwoFactor(userId: string, code: string): Promise<boolean> {
    await this.delay(200);
    
    // Mock verification - accept codes starting with '123'
    const isValid = code.startsWith('123');
    
    if (isValid) {
      // Update user security settings to enable 2FA
      const index = MockDatabase.securitySettings.findIndex(settings => settings.userId === userId);
      if (index !== -1) {
        MockDatabase.securitySettings[index] = {
          ...MockDatabase.securitySettings[index],
          twoFactorEnabled: true,
          updatedAt: new Date(),
        };
      }
    }
    
    return isValid;
  }

  async disableTwoFactor(userId: string): Promise<void> {
    await this.delay(200);
    
    const index = MockDatabase.securitySettings.findIndex(settings => settings.userId === userId);
    if (index !== -1) {
      MockDatabase.securitySettings[index] = {
        ...MockDatabase.securitySettings[index],
        twoFactorEnabled: false,
        updatedAt: new Date(),
      };
    }
  }

  // Subscription Management
  async getSubscriptionPlans(): Promise<SubscriptionPlan[]> {
    await this.delay();
    return MockDatabase.subscriptionPlans.filter(plan => plan.isActive);
  }

  async getUserSubscription(userId: string): Promise<Subscription | null> {
    await this.delay();
    let subscription = MockDatabase.subscriptions.find(sub => sub.userId === userId);
    
    // If no subscription exists, create a default starter subscription
    if (!subscription) {
      subscription = {
        id: `sub_${Date.now()}`,
        userId,
        planId: 'plan_starter', // Default to starter plan
        status: 'active',
        currentPeriodStart: new Date(),
        currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
        cancelAtPeriodEnd: false,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      MockDatabase.subscriptions.push(subscription);
    }
    
    return subscription;
  }

  async changeSubscription(userId: string, changeRequest: SubscriptionChangeRequest): Promise<Subscription> {
    await this.delay(500);
    
    let subscription = MockDatabase.subscriptions.find(sub => sub.userId === userId);
    
    // If no subscription exists, create a starter (free) one
    if (!subscription) {
      subscription = {
        id: `sub_${Date.now()}`,
        userId,
        planId: 'plan_starter', // Default to starter (free) plan
        status: 'active',
        currentPeriodStart: new Date(),
        currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
        cancelAtPeriodEnd: false,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      MockDatabase.subscriptions.push(subscription);
    }
    
    const newPlan = MockDatabase.subscriptionPlans.find(plan => plan.id === changeRequest.newPlanId);
    if (!newPlan) {
      throw new Error(`Invalid subscription plan: ${changeRequest.newPlanId}`);
    }
    
    // Update subscription
    subscription.planId = changeRequest.newPlanId;
    subscription.updatedAt = new Date();
    
    if (changeRequest.effectiveDate === 'immediately') {
      subscription.currentPeriodStart = new Date();
      subscription.currentPeriodEnd = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days
    }
    
    return subscription;
  }

  async cancelSubscription(userId: string, cancelAtPeriodEnd: boolean = true): Promise<Subscription> {
    await this.delay(300);
    
    const subscription = MockDatabase.subscriptions.find(sub => sub.userId === userId);
    if (!subscription) {
      throw new Error('No active subscription found');
    }
    
    subscription.cancelAtPeriodEnd = cancelAtPeriodEnd;
    if (!cancelAtPeriodEnd) {
      subscription.status = 'canceled';
      subscription.canceledAt = new Date();
    }
    subscription.updatedAt = new Date();
    
    return subscription;
  }

  // Invoice Template Management  
  async getInvoiceTemplates(organizationId?: string): Promise<InvoiceTemplate[]> {
    await this.delay();
    return MockDatabase.invoiceTemplates.filter(template => 
      template.isActive && (template.isBuiltIn || template.organizationId === organizationId)
    );
  }

  async getInvoiceTemplate(templateId: string): Promise<InvoiceTemplate | null> {
    await this.delay();
    return MockDatabase.invoiceTemplates.find(template => template.id === templateId) || null;
  }

  async createInvoiceTemplate(organizationId: string, template: CreateInvoiceTemplateInput): Promise<InvoiceTemplate> {
    await this.delay(300);
    
    const newTemplate: InvoiceTemplate = {
      ...template,
      id: `template_${Date.now()}`,
      organizationId,
      isBuiltIn: false,
      isActive: true,
      usage: {
        count: 0,
        lastUsed: undefined,
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    MockDatabase.invoiceTemplates.push(newTemplate);
    return newTemplate;
  }

  async updateInvoiceTemplate(templateId: string, updates: UpdateInvoiceTemplateInput): Promise<InvoiceTemplate> {
    await this.delay(250);
    
    const index = MockDatabase.invoiceTemplates.findIndex(template => template.id === templateId);
    if (index === -1) {
      throw new Error('Template not found');
    }
    
    MockDatabase.invoiceTemplates[index] = {
      ...MockDatabase.invoiceTemplates[index],
      ...updates,
      updatedAt: new Date(),
    };
    
    return MockDatabase.invoiceTemplates[index];
  }

  async deleteInvoiceTemplate(templateId: string): Promise<void> {
    await this.delay(200);
    
    const index = MockDatabase.invoiceTemplates.findIndex(template => template.id === templateId);
    if (index === -1) {
      throw new Error('Template not found');
    }
    
    const template = MockDatabase.invoiceTemplates[index];
    if (template.isBuiltIn) {
      throw new Error('Cannot delete built-in templates');
    }
    
    MockDatabase.invoiceTemplates.splice(index, 1);
  }

  // App Defaults Management
  async getAppDefaults(organizationId: string): Promise<AppDefaults | null> {
    await this.delay();
    return MockDatabase.appDefaults.find(defaults => defaults.organizationId === organizationId) || null;
  }

  async updateAppDefaults(organizationId: string, updates: UpdateAppDefaultsInput): Promise<AppDefaults> {
    await this.delay(250);
    
    const index = MockDatabase.appDefaults.findIndex(defaults => defaults.organizationId === organizationId);
    if (index === -1) {
      throw new Error('App defaults not found for organization');
    }
    
    MockDatabase.appDefaults[index] = {
      ...MockDatabase.appDefaults[index],
      ...updates,
      updatedAt: new Date(),
    };
    
    return MockDatabase.appDefaults[index];
  }

  async getSupportedCurrencies(): Promise<SupportedCurrency[]> {
    await this.delay();
    return MockDatabase.supportedCurrencies;
  }

  async getBusinessTypeTemplates(): Promise<BusinessTypeTemplate[]> {
    await this.delay();
    // For now return empty array - will implement business type templates later
    return [];
  }

  // Feedback Management
  async getFeedbackCategories(): Promise<FeedbackCategory[]> {
    await this.delay();
    return MockDatabase.feedbackCategories.filter(category => category.isActive);
  }

  async submitFeedback(userId: string, feedback: CreateFeedbackInput): Promise<Feedback> {
    await this.delay(300);
    
    const newFeedback: Feedback = {
      ...feedback,
      id: `feedback_${Date.now()}`,
      userId,
      status: 'open',
      followUpRequired: false,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    MockDatabase.feedback.push(newFeedback);
    return newFeedback;
  }

  async getUserFeedback(userId: string): Promise<Feedback[]> {
    await this.delay();
    return MockDatabase.feedback
      .filter(feedback => feedback.userId === userId)
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }

  async updateFeedback(feedbackId: string, updates: UpdateFeedbackInput): Promise<Feedback> {
    await this.delay(250);
    
    const index = MockDatabase.feedback.findIndex(feedback => feedback.id === feedbackId);
    if (index === -1) {
      throw new Error('Feedback not found');
    }
    
    MockDatabase.feedback[index] = {
      ...MockDatabase.feedback[index],
      ...updates,
      updatedAt: new Date(),
    };
    
    return MockDatabase.feedback[index];
  }

  // Help & Support
  async getHelpCategories(): Promise<HelpCategory[]> {
    await this.delay();
    return MockDatabase.helpCategories
      .filter(category => category.isActive)
      .sort((a, b) => a.sortOrder - b.sortOrder);
  }

  async getHelpArticles(categoryId?: string): Promise<HelpArticle[]> {
    await this.delay();
    let articles = MockDatabase.helpArticles.filter(article => article.isPublished);
    
    if (categoryId) {
      articles = articles.filter(article => article.categoryId === categoryId);
    }
    
    return articles.sort((a, b) => {
      // Featured articles first, then by views
      if (a.featured && !b.featured) return -1;
      if (!a.featured && b.featured) return 1;
      return b.views - a.views;
    });
  }

  async getHelpArticle(articleId: string): Promise<HelpArticle | null> {
    await this.delay();
    
    const article = MockDatabase.helpArticles.find(article => 
      article.id === articleId && article.isPublished
    );
    
    if (article) {
      // Increment view count
      article.views++;
    }
    
    return article || null;
  }

  async getFAQs(categoryId?: string): Promise<FAQ[]> {
    await this.delay();
    let faqs = MockDatabase.faqs.filter(faq => faq.isPublished);
    
    if (categoryId) {
      faqs = faqs.filter(faq => faq.categoryId === categoryId);
    }
    
    return faqs.sort((a, b) => a.priority - b.priority);
  }

  async searchKnowledgeBase(searchInput: KnowledgeBaseSearchInput): Promise<SearchResult[]> {
    await this.delay(400);
    
    const { query, category, limit = 10, sortBy = 'relevance' } = searchInput;
    const searchTerm = query.toLowerCase();
    
    // Search articles
    const articleResults = MockDatabase.helpArticles
      .filter(article => {
        if (!article.isPublished) return false;
        if (category && article.categoryId !== category) return false;
        
        return (
          article.title.toLowerCase().includes(searchTerm) ||
          article.content.toLowerCase().includes(searchTerm) ||
          article.tags.some(tag => tag.toLowerCase().includes(searchTerm)) ||
          article.searchKeywords.some(keyword => keyword.toLowerCase().includes(searchTerm))
        );
      })
      .map(article => ({
        id: article.id,
        title: article.title,
        excerpt: article.excerpt || article.content.substring(0, 150) + '...',
        url: `/help/articles/${article.slug}`,
        type: 'article' as const,
        category: article.categoryId,
        relevanceScore: this.calculateRelevanceScore(article.title + ' ' + article.content, searchTerm),
        lastUpdated: article.updatedAt,
        tags: article.tags,
        difficulty: article.difficulty,
      }));
    
    // Search FAQs
    const faqResults = MockDatabase.faqs
      .filter(faq => {
        if (!faq.isPublished) return false;
        if (category && faq.categoryId !== category) return false;
        
        return (
          faq.question.toLowerCase().includes(searchTerm) ||
          faq.answer.toLowerCase().includes(searchTerm) ||
          faq.tags.some(tag => tag.toLowerCase().includes(searchTerm))
        );
      })
      .map(faq => ({
        id: faq.id,
        title: faq.question,
        excerpt: faq.answer.substring(0, 150) + '...',
        url: `/help/faqs/${faq.id}`,
        type: 'faq' as const,
        category: faq.categoryId,
        relevanceScore: this.calculateRelevanceScore(faq.question + ' ' + faq.answer, searchTerm),
        lastUpdated: faq.updatedAt,
        tags: faq.tags,
        difficulty: 'beginner' as const,
      }));
    
    // Combine and sort results
    let allResults = [...articleResults, ...faqResults];
    
    if (sortBy === 'relevance') {
      allResults.sort((a, b) => b.relevanceScore - a.relevanceScore);
    } else if (sortBy === 'date') {
      allResults.sort((a, b) => new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime());
    }
    
    return allResults.slice(0, limit);
  }

  private calculateRelevanceScore(content: string, searchTerm: string): number {
    const contentLower = content.toLowerCase();
    const termLower = searchTerm.toLowerCase();
    
    // Simple relevance scoring
    let score = 0;
    
    // Exact match in title gets highest score
    if (contentLower.includes(termLower)) {
      score += 10;
    }
    
    // Count occurrences
    const occurrences = (contentLower.match(new RegExp(termLower, 'g')) || []).length;
    score += occurrences * 2;
    
    // Word proximity bonus (very simple implementation)
    const words = termLower.split(' ');
    if (words.length > 1) {
      const wordsInContent = words.filter(word => contentLower.includes(word));
      score += (wordsInContent.length / words.length) * 5;
    }
    
    return score;
  }

  async submitContactForm(contactForm: ContactFormInput): Promise<void> {
    await this.delay(500);
    
    const submission: ContactSubmission = {
      ...contactForm,
      id: `contact_${Date.now()}`,
      processed: false,
      status: 'new',
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    // In a real app, this would send to support system
    console.log('Contact form submitted:', submission);
  }

  // Rate App - Integration with App Store/Play Store
  async rateApp(rating: number, review?: string): Promise<void> {
    await this.delay(200);
    
    // In a real app, this would integrate with native store rating APIs
    console.log('App rating submitted:', { rating, review });
  }
} 