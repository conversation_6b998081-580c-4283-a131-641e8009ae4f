import { IApiProvider } from './api-provider-interface';
import { MockProvider } from './mock-provider';

let providerInstance: IApiProvider | null = null;

export function getApiProvider(): IApiProvider {
  if (!providerInstance) {
    // Always use MockProvider for now since we don't have a real API provider yet
    // In the future, this can check environment variables to decide which provider to use
    providerInstance = new MockProvider() as IApiProvider;
  }
  
  // TypeScript assertion - we know providerInstance is not null here
  return providerInstance!;
}

// For testing purposes, allow resetting the provider
export function resetProvider(): void {
  providerInstance = null;
} 