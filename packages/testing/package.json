{"name": "@repo/testing", "version": "0.0.0", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "scripts": {"lint": "eslint . --max-warnings 0", "generate:package": "tsup", "dev": "tsup --watch", "typecheck": "tsc --noEmit"}, "dependencies": {"@repo/schemas": "workspace:*"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "^20.5.2", "eslint": "^8.48.0", "tsup": "^7.2.0", "typescript": "^5.2.2"}, "exports": {".": {"types": "./src/index.ts", "default": "./src/index.ts"}}}