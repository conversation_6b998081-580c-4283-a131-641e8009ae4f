{"name": "@repo/utils", "version": "0.0.0", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "exports": {".": "./src/index.ts", "./currency": "./src/currency/index.ts", "./date": "./src/date/index.ts", "./validation": "./src/validation/index.ts", "./formatting": "./src/formatting/index.ts"}, "scripts": {"lint": "eslint .", "type-check": "tsc --noEmit"}, "dependencies": {"@repo/schemas": "workspace:*", "zod": "^3.25.42"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "eslint": "catalog:", "typescript": "catalog:"}}