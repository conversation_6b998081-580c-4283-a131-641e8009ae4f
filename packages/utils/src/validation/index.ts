import { z } from 'zod';

// Common validation utilities

export const isValidEmail = (email: string): boolean => {
  const emailSchema = z.string().email();
  return emailSchema.safeParse(email).success;
};

export const isValidUrl = (url: string): boolean => {
  const urlSchema = z.string().url();
  return urlSchema.safeParse(url).success;
};

export const isValidPhoneNumber = (phone: string): boolean => {
  // Basic phone number validation - can be enhanced
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
};

export const isValidPostalCode = (postalCode: string, country?: string): boolean => {
  // Basic postal code validation - can be enhanced per country
  if (!postalCode) return false;
  
  switch (country?.toUpperCase()) {
    case 'US':
      return /^\d{5}(-\d{4})?$/.test(postalCode);
    case 'CA':
      return /^[A-Za-z]\d[A-Za-z] \d[A-Za-z]\d$/.test(postalCode);
    case 'UK':
    case 'GB':
      return /^[A-Za-z]{1,2}\d[A-Za-z\d]?\s?\d[A-Za-z]{2}$/.test(postalCode);
    default:
      return postalCode.length >= 3 && postalCode.length <= 10;
  }
};

export const isValidTaxId = (taxId: string): boolean => {
  // Basic tax ID validation - can be enhanced per country
  return /^[A-Za-z0-9\-]{5,20}$/.test(taxId);
};

export const isValidCurrencyCode = (code: string): boolean => {
  // ISO 4217 currency code validation
  return /^[A-Z]{3}$/.test(code);
};

export const isValidPercentage = (value: number): boolean => {
  return value >= 0 && value <= 100;
};

export const isValidAmount = (amount: number): boolean => {
  return amount >= 0 && Number.isFinite(amount);
};

export const sanitizeString = (input: string): string => {
  return input.trim().replace(/\s+/g, ' ');
};

export const sanitizeHtml = (input: string): string => {
  // Basic HTML sanitization - remove script tags and dangerous attributes
  return input
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/on\w+="[^"]*"/gi, '')
    .replace(/javascript:/gi, '');
};

export const validateRequired = (value: any, fieldName: string): string | null => {
  if (value === null || value === undefined || value === '') {
    return `${fieldName} is required`;
  }
  return null;
};

export const validateMinLength = (value: string, minLength: number, fieldName: string): string | null => {
  if (value.length < minLength) {
    return `${fieldName} must be at least ${minLength} characters`;
  }
  return null;
};

export const validateMaxLength = (value: string, maxLength: number, fieldName: string): string | null => {
  if (value.length > maxLength) {
    return `${fieldName} must be no more than ${maxLength} characters`;
  }
  return null;
};

export const validateRange = (value: number, min: number, max: number, fieldName: string): string | null => {
  if (value < min || value > max) {
    return `${fieldName} must be between ${min} and ${max}`;
  }
  return null;
};

// Zod schema validation helper
export const validateWithSchema = <T>(schema: z.ZodSchema<T>, data: unknown): {
  success: boolean;
  data?: T;
  errors?: Record<string, string>;
} => {
  const result = schema.safeParse(data);
  
  if (result.success) {
    return { success: true, data: result.data };
  }
  
  const errors: Record<string, string> = {};
  result.error.errors.forEach((error) => {
    const path = error.path.join('.');
    errors[path] = error.message;
  });
  
  return { success: false, errors };
};
