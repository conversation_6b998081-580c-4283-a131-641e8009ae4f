# @repo/utils

This package contains all shared utility functions for the InvoiceGo monorepo. It provides common functionality for currency formatting, date manipulation, validation, and other business logic utilities.

## 📦 Installation

```bash
pnpm add @repo/utils
```

## 🏗️ Architecture

The utils package is organized by functionality:

```
src/
├── currency/     # Currency formatting and manipulation
├── date/         # Date utilities and formatting
├── validation/   # Common validation functions
├── formatting/   # Text and number formatting
├── business/     # Business logic utilities
└── index.ts      # Export all utilities
```

## 🔧 Usage

### Currency Utilities

```typescript
import { formatCurrency, parseCurrency, convertCurrency } from '@repo/utils/currency';

// Format amounts with currency symbols
const formatted = formatCurrency(1234.56, 'USD'); // "$1,234.56"
const euroFormatted = formatCurrency(1234.56, 'EUR'); // "€1,234.56"

// Parse currency strings back to numbers
const amount = parseCurrency("$1,234.56"); // 1234.56

// Handle different decimal places
const yen = formatCurrency(1234, 'JPY'); // "¥1,234" (0 decimal places)
```

### Date Utilities

```typescript
import { 
  formatDate, 
  formatRelativeDate, 
  addBusinessDays,
  calculateDueDate,
  isOverdue 
} from '@repo/utils/date';

// Format dates for display
const formatted = formatDate(new Date(), 'MM/dd/yyyy'); // "12/19/2024"
const relative = formatRelativeDate(new Date()); // "Today", "Yesterday", "2 days ago"

// Business date calculations
const dueDate = calculateDueDate(new Date(), { days: 30 }); // 30 days from now
const isLate = isOverdue(dueDate); // boolean

// Skip weekends for payment terms
const businessDue = addBusinessDays(new Date(), 14); // 14 business days
```

### Validation Utilities

```typescript
import { 
  validateEmail, 
  validatePhone, 
  validateURL,
  validateBusinessId,
  sanitizeInput 
} from '@repo/utils/validation';

// Email validation
const emailValid = validateEmail("<EMAIL>"); // true
const emailInvalid = validateEmail("invalid-email"); // false

// Phone number validation (international)
const phoneValid = validatePhone("******-123-4567"); // true

// URL validation
const urlValid = validateURL("https://example.com"); // true

// Business ID validation (tax ID, VAT, etc.)
const taxIdValid = validateBusinessId("123-45-6789", "US"); // true

// Input sanitization
const clean = sanitizeInput("<script>alert('xss')</script>"); // "alert('xss')"
```

### Formatting Utilities

```typescript
import { 
  formatNumber, 
  formatPercentage,
  formatBusinessId,
  capitalizeWords,
  truncateText 
} from '@repo/utils/formatting';

// Number formatting
const number = formatNumber(1234567.89); // "1,234,567.89"
const percentage = formatPercentage(0.1534); // "15.34%"

// Text formatting
const title = capitalizeWords("invoice management system"); // "Invoice Management System"
const excerpt = truncateText("Very long text...", 50); // "Very long text... (truncated)"

// Business ID formatting
const formatted = formatBusinessId("*********", "EIN"); // "12-3456789"
```

### Business Logic Utilities

```typescript
import { 
  calculateInvoiceTotal,
  calculateTax,
  generateInvoiceNumber,
  validatePaymentTerms 
} from '@repo/utils/business';

// Invoice calculations
const lineItems = [
  { quantity: 2, unitPrice: 100, description: "Service A" },
  { quantity: 1, unitPrice: 200, description: "Service B" }
];

const subtotal = calculateInvoiceTotal(lineItems); // 400
const tax = calculateTax(subtotal, 0.08); // 32
const total = subtotal + tax; // 432

// Invoice number generation
const invoiceNumber = generateInvoiceNumber({
  prefix: "INV",
  year: 2024,
  sequence: 1
}); // "INV-2024-0001"

// Payment terms validation
const termsValid = validatePaymentTerms("Net 30"); // true
```

## 🔧 Available Utilities

### Currency Functions
- `formatCurrency(amount, currencyCode)` - Format amount with currency symbol
- `parseCurrency(currencyString)` - Parse formatted currency to number
- `convertCurrency(amount, from, to, rates)` - Convert between currencies
- `getCurrencySymbol(currencyCode)` - Get symbol for currency code
- `validateCurrencyCode(code)` - Validate ISO 4217 currency code

### Date Functions
- `formatDate(date, format)` - Format date with custom format
- `formatRelativeDate(date)` - Human-readable relative dates
- `addBusinessDays(date, days)` - Add business days (skip weekends)
- `calculateDueDate(issueDate, terms)` - Calculate payment due date
- `isOverdue(dueDate)` - Check if date is overdue
- `getBusinessDaysUntil(startDate, endDate)` - Count business days

### Validation Functions
- `validateEmail(email)` - RFC-compliant email validation
- `validatePhone(phone, country?)` - International phone validation
- `validateURL(url)` - URL validation with protocol checking
- `validateBusinessId(id, type)` - Tax ID/VAT/EIN validation
- `validatePostalCode(code, country)` - Postal code validation
- `sanitizeInput(input)` - XSS protection and input cleaning

### Formatting Functions
- `formatNumber(number, options?)` - Number formatting with locale
- `formatPercentage(decimal, precision?)` - Percentage formatting
- `formatBytes(bytes)` - File size formatting (KB, MB, GB)
- `capitalizeWords(text)` - Title case conversion
- `truncateText(text, length, suffix?)` - Smart text truncation
- `slugify(text)` - URL-safe slug generation

### Business Functions
- `calculateInvoiceTotal(lineItems)` - Calculate invoice subtotal
- `calculateTax(amount, rate, inclusive?)` - Tax calculations
- `generateInvoiceNumber(options)` - Invoice number generation
- `validatePaymentTerms(terms)` - Payment terms validation
- `calculateDiscountAmount(subtotal, discount)` - Discount calculations
- `formatPaymentTerms(terms)` - Human-readable payment terms

## 🌐 Internationalization

Many utilities support internationalization:

```typescript
import { formatCurrency, formatDate, formatNumber } from '@repo/utils';

// Currency formatting respects locale
const usd = formatCurrency(1234.56, 'USD', 'en-US'); // "$1,234.56"
const eur = formatCurrency(1234.56, 'EUR', 'de-DE'); // "1.234,56 €"

// Date formatting with locale
const date = formatDate(new Date(), 'long', 'en-US'); // "December 19, 2024"
const dateFr = formatDate(new Date(), 'long', 'fr-FR'); // "19 décembre 2024"

// Number formatting with locale
const number = formatNumber(1234.56, { locale: 'de-DE' }); // "1.234,56"
```

## 🎯 Benefits

1. **Consistency** - Same formatting across all applications
2. **Reliability** - Thoroughly tested utility functions
3. **Internationalization** - Built-in locale support
4. **Performance** - Optimized and memoized functions
5. **Type Safety** - Full TypeScript support
6. **Extensibility** - Easy to add new utilities

## 🧪 Testing

All utilities include comprehensive unit tests:

```bash
pnpm test
```

### Example Test Coverage

```typescript
// Currency utilities are thoroughly tested
describe('formatCurrency', () => {
  it('formats USD correctly', () => {
    expect(formatCurrency(1234.56, 'USD')).toBe('$1,234.56');
  });

  it('handles zero decimal currencies', () => {
    expect(formatCurrency(1234, 'JPY')).toBe('¥1,234');
  });

  it('respects currency positioning', () => {
    expect(formatCurrency(1234.56, 'EUR')).toBe('€1,234.56');
  });
});
```

## 🔧 Custom Utilities

You can easily extend the utilities for your specific needs:

```typescript
import { formatCurrency, validateEmail } from '@repo/utils';

// Create domain-specific utilities
export const formatInvoiceAmount = (amount: number, currency: string) => {
  return formatCurrency(amount, currency).replace('$', 'USD ');
};

export const validateBusinessEmail = (email: string) => {
  return validateEmail(email) && !email.includes('@gmail.com');
};
```

## 📚 Related Packages

- `@repo/schemas` - Type definitions for utility function parameters
- `@repo/constants` - Shared constants used by utilities
- `@repo/stores` - Business logic that uses these utilities 