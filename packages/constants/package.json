{"name": "@repo/constants", "version": "0.0.0", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "exports": {".": "./src/index.ts", "./currencies": "./src/currencies.ts", "./countries": "./src/countries.ts", "./query-keys": "./src/query-keys.ts"}, "scripts": {"lint": "eslint .", "type-check": "tsc --noEmit"}, "dependencies": {"@repo/schemas": "workspace:*"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "eslint": "catalog:", "typescript": "catalog:"}}