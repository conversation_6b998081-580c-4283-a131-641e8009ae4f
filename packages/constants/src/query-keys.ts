// React Query key constants for consistent caching

export const QUERY_KEYS = {
  // Organization queries
  ORGANIZATIONS: ['organizations'] as const,
  ORGANIZATION: (id: string) => ['organizations', id] as const,
  ORGANIZATION_SETTINGS: (id: string) => ['organizations', id, 'settings'] as const,
  
  // Client queries
  CLIENTS: (organizationId: string) => ['clients', organizationId] as const,
  CLIENT: (organizationId: string, clientId: string) => ['clients', organizationId, clientId] as const,
  CLIENT_ACTIVITIES: (organizationId: string, clientId: string) => ['clients', organizationId, clientId, 'activities'] as const,
  
  // Invoice queries
  INVOICES: (organizationId: string) => ['invoices', organizationId] as const,
  INVOICE: (organizationId: string, invoiceId: string) => ['invoices', organizationId, invoiceId] as const,
  INVOICE_ACTIVITIES: (organizationId: string, invoiceId: string) => ['invoices', organizationId, invoiceId, 'activities'] as const,
  INVOICE_COMMENTS: (organizationId: string, invoiceId: string) => ['invoices', organizationId, invoiceId, 'comments'] as const,
  
  // Service queries
  SERVICES: (organizationId: string) => ['services', organizationId] as const,
  SERVICE: (organizationId: string, serviceId: string) => ['services', organizationId, serviceId] as const,
  SERVICE_ACTIVITIES: (organizationId: string, serviceId: string) => ['services', organizationId, serviceId, 'activities'] as const,
  
  // Dashboard queries
  DASHBOARD: (organizationId: string) => ['dashboard', organizationId] as const,
  DASHBOARD_STATS: (organizationId: string) => ['dashboard', organizationId, 'stats'] as const,
  DASHBOARD_RECENT_INVOICES: (organizationId: string) => ['dashboard', organizationId, 'recent-invoices'] as const,
  DASHBOARD_RECENT_CLIENTS: (organizationId: string) => ['dashboard', organizationId, 'recent-clients'] as const,
  
  // Settings queries
  SETTINGS: ['settings'] as const,
  SETTINGS_UI: ['settings', 'ui'] as const,
  SETTINGS_CURRENCY: ['settings', 'currency'] as const,
  
  // User queries
  USER: ['user'] as const,
  USER_PROFILE: ['user', 'profile'] as const,
  USER_PREFERENCES: ['user', 'preferences'] as const,
  USER_SECURITY: (userId: string) => ['user', userId, 'security'] as const,
  
  // Payment queries
  PAYMENTS: (organizationId: string) => ['payments', organizationId] as const,
  PAYMENT: (organizationId: string, paymentId: string) => ['payments', organizationId, paymentId] as const,
  PAYMENT_METHODS: (organizationId: string) => ['payment-methods', organizationId] as const,
  
  // Tax queries
  TAX_RATES: (organizationId: string) => ['tax-rates', organizationId] as const,
  TAX_RATE: (organizationId: string, taxRateId: string) => ['tax-rates', organizationId, taxRateId] as const,
  
  // Template queries
  TEMPLATES: (organizationId: string) => ['templates', organizationId] as const,
  TEMPLATE: (organizationId: string, templateId: string) => ['templates', organizationId, templateId] as const,
  
  // Subscription queries
  SUBSCRIPTION: ['subscription'] as const,
  SUBSCRIPTION_PLANS: ['subscription', 'plans'] as const,
  SUBSCRIPTION_USAGE: ['subscription', 'usage'] as const,
} as const;

// Mutation keys for optimistic updates
export const MUTATION_KEYS = {
  // Organization mutations
  CREATE_ORGANIZATION: 'create-organization',
  UPDATE_ORGANIZATION: 'update-organization',
  DELETE_ORGANIZATION: 'delete-organization',
  
  // Client mutations
  CREATE_CLIENT: 'create-client',
  UPDATE_CLIENT: 'update-client',
  DELETE_CLIENT: 'delete-client',
  
  // Invoice mutations
  CREATE_INVOICE: 'create-invoice',
  UPDATE_INVOICE: 'update-invoice',
  DELETE_INVOICE: 'delete-invoice',
  SEND_INVOICE: 'send-invoice',
  MARK_INVOICE_PAID: 'mark-invoice-paid',
  
  // Service mutations
  CREATE_SERVICE: 'create-service',
  UPDATE_SERVICE: 'update-service',
  DELETE_SERVICE: 'delete-service',
  
  // Payment mutations
  CREATE_PAYMENT: 'create-payment',
  UPDATE_PAYMENT: 'update-payment',
  DELETE_PAYMENT: 'delete-payment',
  
  // Settings mutations
  UPDATE_SETTINGS: 'update-settings',
  UPDATE_UI_PREFERENCES: 'update-ui-preferences',
} as const;
