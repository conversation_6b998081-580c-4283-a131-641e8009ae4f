import { Currency } from '@repo/schemas';

// Comprehensive currency list (moved from settingsStore.ts)
export const CURRENCIES: Currency[] = [
  {
    code: 'USD',
    symbol: '$',
    name: 'US Dollar',
    decimalPlaces: 2,
    symbolPosition: 'before',
  },
  {
    code: 'EUR',
    symbol: '€',
    name: 'Euro',
    decimalPlaces: 2,
    symbolPosition: 'before',
  },
  {
    code: 'GBP',
    symbol: '£',
    name: 'British Pound',
    decimalPlaces: 2,
    symbolPosition: 'before',
  },
  {
    code: 'CAD',
    symbol: 'C$',
    name: 'Canadian Dollar',
    decimalPlaces: 2,
    symbolPosition: 'before',
  },
  {
    code: 'AUD',
    symbol: 'A$',
    name: 'Australian Dollar',
    decimalPlaces: 2,
    symbolPosition: 'before',
  },
  {
    code: 'JPY',
    symbol: '¥',
    name: 'Japanese Yen',
    decimalPlaces: 0,
    symbolPosition: 'before',
  },
  {
    code: 'CNY',
    symbol: '¥',
    name: 'Chinese Yuan',
    decimalPlaces: 2,
    symbolPosition: 'before',
  },
  {
    code: 'INR',
    symbol: '₹',
    name: 'Indian Rupee',
    decimalPlaces: 2,
    symbolPosition: 'before',
  },
  {
    code: 'KRW',
    symbol: '₩',
    name: 'South Korean Won',
    decimalPlaces: 0,
    symbolPosition: 'before',
  },
  {
    code: 'BRL',
    symbol: 'R$',
    name: 'Brazilian Real',
    decimalPlaces: 2,
    symbolPosition: 'before',
  },
  {
    code: 'MXN',
    symbol: '$',
    name: 'Mexican Peso',
    decimalPlaces: 2,
    symbolPosition: 'before',
  },
  {
    code: 'CHF',
    symbol: 'CHF',
    name: 'Swiss Franc',
    decimalPlaces: 2,
    symbolPosition: 'before',
  },
  {
    code: 'SEK',
    symbol: 'kr',
    name: 'Swedish Krona',
    decimalPlaces: 2,
    symbolPosition: 'after',
  },
  {
    code: 'NOK',
    symbol: 'kr',
    name: 'Norwegian Krone',
    decimalPlaces: 2,
    symbolPosition: 'after',
  },
  {
    code: 'DKK',
    symbol: 'kr',
    name: 'Danish Krone',
    decimalPlaces: 2,
    symbolPosition: 'after',
  },
  {
    code: 'PLN',
    symbol: 'zł',
    name: 'Polish Złoty',
    decimalPlaces: 2,
    symbolPosition: 'after',
  },
  {
    code: 'CZK',
    symbol: 'Kč',
    name: 'Czech Koruna',
    decimalPlaces: 2,
    symbolPosition: 'after',
  },
  {
    code: 'HUF',
    symbol: 'Ft',
    name: 'Hungarian Forint',
    decimalPlaces: 0,
    symbolPosition: 'after',
  },
  {
    code: 'RUB',
    symbol: '₽',
    name: 'Russian Ruble',
    decimalPlaces: 2,
    symbolPosition: 'after',
  },
  {
    code: 'TRY',
    symbol: '₺',
    name: 'Turkish Lira',
    decimalPlaces: 2,
    symbolPosition: 'before',
  },
  {
    code: 'ZAR',
    symbol: 'R',
    name: 'South African Rand',
    decimalPlaces: 2,
    symbolPosition: 'before',
  },
  {
    code: 'SGD',
    symbol: 'S$',
    name: 'Singapore Dollar',
    decimalPlaces: 2,
    symbolPosition: 'before',
  },
  {
    code: 'HKD',
    symbol: 'HK$',
    name: 'Hong Kong Dollar',
    decimalPlaces: 2,
    symbolPosition: 'before',
  },
  {
    code: 'NZD',
    symbol: 'NZ$',
    name: 'New Zealand Dollar',
    decimalPlaces: 2,
    symbolPosition: 'before',
  },
  {
    code: 'THB',
    symbol: '฿',
    name: 'Thai Baht',
    decimalPlaces: 2,
    symbolPosition: 'before',
  },
];

// Popular currencies for quick access
export const POPULAR_CURRENCIES = CURRENCIES.slice(0, 6);

// Currency groups
export const CURRENCY_GROUPS = {
  MAJOR: ['USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD'],
  EUROPEAN: ['EUR', 'GBP', 'CHF', 'SEK', 'NOK', 'DKK', 'PLN', 'CZK', 'HUF'],
  ASIAN: ['JPY', 'CNY', 'INR', 'KRW', 'SGD', 'HKD', 'THB'],
  AMERICAS: ['USD', 'CAD', 'BRL', 'MXN'],
} as const;
