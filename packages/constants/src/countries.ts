// Country constants for address and localization

export interface Country {
  code: string; // ISO 3166-1 alpha-2
  name: string;
  currency: string;
  phonePrefix: string;
  postalCodeFormat?: string;
}

export const COUNTRIES: Country[] = [
  {
    code: 'US',
    name: 'United States',
    currency: 'USD',
    phonePrefix: '+1',
    postalCodeFormat: '12345 or 12345-6789',
  },
  {
    code: 'CA',
    name: 'Canada',
    currency: 'CAD',
    phonePrefix: '+1',
    postalCodeFormat: 'A1A 1A1',
  },
  {
    code: 'GB',
    name: 'United Kingdom',
    currency: 'GBP',
    phonePrefix: '+44',
    postalCodeFormat: 'SW1A 1AA',
  },
  {
    code: 'DE',
    name: 'Germany',
    currency: 'EUR',
    phonePrefix: '+49',
    postalCodeFormat: '12345',
  },
  {
    code: 'FR',
    name: 'France',
    currency: 'EUR',
    phonePrefix: '+33',
    postalCodeFormat: '12345',
  },
  {
    code: 'IT',
    name: 'Italy',
    currency: 'EUR',
    phonePrefix: '+39',
    postalCodeFormat: '12345',
  },
  {
    code: 'ES',
    name: 'Spain',
    currency: 'EUR',
    phonePrefix: '+34',
    postalCodeFormat: '12345',
  },
  {
    code: 'NL',
    name: 'Netherlands',
    currency: 'EUR',
    phonePrefix: '+31',
    postalCodeFormat: '1234 AB',
  },
  {
    code: 'AU',
    name: 'Australia',
    currency: 'AUD',
    phonePrefix: '+61',
    postalCodeFormat: '1234',
  },
  {
    code: 'JP',
    name: 'Japan',
    currency: 'JPY',
    phonePrefix: '+81',
    postalCodeFormat: '123-4567',
  },
  {
    code: 'CN',
    name: 'China',
    currency: 'CNY',
    phonePrefix: '+86',
    postalCodeFormat: '123456',
  },
  {
    code: 'IN',
    name: 'India',
    currency: 'INR',
    phonePrefix: '+91',
    postalCodeFormat: '123456',
  },
  {
    code: 'BR',
    name: 'Brazil',
    currency: 'BRL',
    phonePrefix: '+55',
    postalCodeFormat: '12345-678',
  },
  {
    code: 'MX',
    name: 'Mexico',
    currency: 'MXN',
    phonePrefix: '+52',
    postalCodeFormat: '12345',
  },
  {
    code: 'KR',
    name: 'South Korea',
    currency: 'KRW',
    phonePrefix: '+82',
    postalCodeFormat: '12345',
  },
  {
    code: 'SG',
    name: 'Singapore',
    currency: 'SGD',
    phonePrefix: '+65',
    postalCodeFormat: '123456',
  },
  {
    code: 'HK',
    name: 'Hong Kong',
    currency: 'HKD',
    phonePrefix: '+852',
  },
  {
    code: 'NZ',
    name: 'New Zealand',
    currency: 'NZD',
    phonePrefix: '+64',
    postalCodeFormat: '1234',
  },
  {
    code: 'CH',
    name: 'Switzerland',
    currency: 'CHF',
    phonePrefix: '+41',
    postalCodeFormat: '1234',
  },
  {
    code: 'SE',
    name: 'Sweden',
    currency: 'SEK',
    phonePrefix: '+46',
    postalCodeFormat: '123 45',
  },
  {
    code: 'NO',
    name: 'Norway',
    currency: 'NOK',
    phonePrefix: '+47',
    postalCodeFormat: '1234',
  },
  {
    code: 'DK',
    name: 'Denmark',
    currency: 'DKK',
    phonePrefix: '+45',
    postalCodeFormat: '1234',
  },
];

// Popular countries for quick access
export const POPULAR_COUNTRIES = COUNTRIES.slice(0, 10);

// Country groups
export const COUNTRY_GROUPS = {
  NORTH_AMERICA: ['US', 'CA', 'MX'],
  EUROPE: ['GB', 'DE', 'FR', 'IT', 'ES', 'NL', 'CH', 'SE', 'NO', 'DK'],
  ASIA_PACIFIC: ['AU', 'JP', 'CN', 'IN', 'KR', 'SG', 'HK', 'NZ'],
  SOUTH_AMERICA: ['BR'],
} as const;
