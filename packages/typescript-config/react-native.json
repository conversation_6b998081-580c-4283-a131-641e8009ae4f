{"$schema": "https://json.schemastore.org/tsconfig", "display": "React Native", "compilerOptions": {"allowJs": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "isolatedModules": true, "jsx": "react-jsx", "lib": ["es2017"], "moduleResolution": "node", "noEmit": true, "strict": true, "target": "esnext", "resolveJsonModule": true, "skipLibCheck": true, "declaration": true, "declarationMap": true}, "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js"]}