import { QUERY_KEYS } from '@repo/constants';
import { getApiProvider } from '@repo/providers';
import { queryClient } from '../query-client';
import { Invoice, InvoiceActivity, UpdateInvoiceForm } from '@repo/schemas';
import { useActiveOrganizationId } from '@repo/stores';
import { useQuery } from '@tanstack/react-query';
import { debounce } from 'lodash';

const provider = getApiProvider();

// Service functions
export const fetchInvoices = async (organizationId: string): Promise<Invoice[]> => {
  const response = await provider.getInvoices(organizationId);
  return response;
};

export const fetchInvoice = async (organizationId: string, invoiceId: string): Promise<Invoice> => {
  const response = await provider.getInvoice(organizationId, invoiceId);
  return response;
};

export const updateInvoice = async (data: UpdateInvoiceForm & { organizationId: string; id: string }): Promise<Invoice> => {
  const response = await provider.updateInvoice(data.organizationId, data.id, data);
  
  // Immediate cache updates
  queryClient.setQueryData(QUERY_KEYS.INVOICE(data.organizationId, response.id), response);
  queryClient.setQueryData(QUERY_KEYS.INVOICES(data.organizationId), (cache: Invoice[]) => {
    if (!cache) return [response];
    return cache.map((invoice) => invoice.id === response.id ? response : invoice);
  });
  
  return response;
};

export const fetchInvoiceActivities = async (organizationId: string, invoiceId: string): Promise<InvoiceActivity[]> => {
  const response = await provider.getInvoiceActivities(organizationId, invoiceId);
  return response;
};

// Debounced version for real-time editing
export const debouncedUpdateInvoice = debounce(updateInvoice, 1000);

// Clean hooks
export const useInvoices = () => {
  const organizationId = useActiveOrganizationId();
  
  const {
    error,
    isPending: loading,
    data: invoices,
  } = useQuery({
    queryKey: QUERY_KEYS.INVOICES(organizationId!),
    queryFn: () => fetchInvoices(organizationId!),
    enabled: !!organizationId,
  });

  return { invoices, loading, error };
};

export const useInvoice = (invoiceId: string) => {
  const organizationId = useActiveOrganizationId();
  
  const {
    error,
    isPending: loading,
    data: invoice,
  } = useQuery({
    queryKey: QUERY_KEYS.INVOICE(organizationId!, invoiceId),
    queryFn: () => fetchInvoice(organizationId!, invoiceId),
    enabled: !!organizationId && !!invoiceId,
  });

  return { invoice, loading, error };
};

export const useInvoiceActivities = (invoiceId: string) => {
  const organizationId = useActiveOrganizationId();
  
  const {
    error,
    isPending: loading,
    data: activities,
  } = useQuery({
    queryKey: QUERY_KEYS.INVOICE_ACTIVITIES(organizationId!, invoiceId),
    queryFn: () => fetchInvoiceActivities(organizationId!, invoiceId),
    enabled: !!organizationId && !!invoiceId,
  });

  return { activities, loading, error };
}; 