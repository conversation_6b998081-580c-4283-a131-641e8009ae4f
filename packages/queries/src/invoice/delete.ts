import { QUERY_KEYS } from '@repo/constants';
import { getApiProvider } from '@repo/providers';
import { queryClient } from '../query-client';
import { Invoice } from '@repo/schemas';
import { useActiveOrganizationId } from '@repo/stores';
import { useMutation } from '@tanstack/react-query';

const provider = getApiProvider();

export const deleteInvoice = async (organizationId: string, invoiceId: string): Promise<void> => {
  await provider.deleteInvoice(organizationId, invoiceId);
  
  // Immediate cache updates
  queryClient.removeQueries({ queryKey: QUERY_KEYS.INVOICE(organizationId, invoiceId) });
  queryClient.setQueryData(QUERY_KEYS.INVOICES(organizationId), (cache: Invoice[]) => {
    if (!cache) return [];
    return cache.filter((invoice) => invoice.id !== invoiceId);
  });
};

export const useDeleteInvoice = () => {
  const organizationId = useActiveOrganizationId();
  
  const {
    error,
    isPending: loading,
    mutateAsync: deleteInvoiceFn,
  } = useMutation({
    mutationFn: (invoiceId: string) => 
      deleteInvoice(organizationId!, invoiceId),
  });

  return { deleteInvoice: deleteInvoiceFn, loading, error };
}; 