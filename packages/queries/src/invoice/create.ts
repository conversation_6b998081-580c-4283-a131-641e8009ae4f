import { QUERY_KEYS } from '@repo/constants';
import { CreateInvoiceInput as ProviderCreateInvoiceInput, getApiProvider } from '@repo/providers';
import { queryClient } from '../query-client';
import { Invoice, CreateInvoiceForm as ZodCreateInvoiceInput } from '@repo/schemas';
import { useActiveOrganizationId } from '@repo/stores';
import { useMutation } from '@tanstack/react-query';

const provider = getApiProvider();

export const createInvoice = async (organizationId: string, data: ZodCreateInvoiceInput): Promise<Invoice> => {
  // Convert Zod input to provider input format
  const providerData: ProviderCreateInvoiceInput = {
    ...data,
    totals: {}, // Will be calculated by the provider/backend
  };
  
  const response = await provider.createInvoice(organizationId, providerData);
  
      // Immediate cache updates
    queryClient.setQueryData(QUERY_KEYS.INVOICE(organizationId, response.id), response);
    queryClient.setQueryData(QUERY_KEYS.INVOICES(organizationId), (cache: Invoice[]) => {
    if (!cache) return [response];
    return [...cache, response];
  });
  
  return response;
};

export const useCreateInvoice = () => {
  const organizationId = useActiveOrganizationId();
  
  const {
    error,
    isPending: loading,
    mutateAsync: createInvoiceFn,
  } = useMutation({
    mutationFn: (data: ZodCreateInvoiceInput) => 
      createInvoice(organizationId!, data),
    onSuccess: (data) => {
      // Additional success handling if needed
    },
  });

  return { createInvoice: createInvoiceFn, loading, error };
}; 