import { QUERY_KEYS } from '@repo/constants';
import { getApiProvider } from '@repo/providers';
import { queryClient } from '../query-client';
import { Invoice, UpdateInvoiceForm } from '@repo/schemas';
import { useActiveOrganizationId } from '@repo/stores';
import { useMutation } from '@tanstack/react-query';

const provider = getApiProvider();

export const updateInvoice = async (data: UpdateInvoiceForm & {organizationId: string, id: string}): Promise<Invoice> => {
  const response = await provider.updateInvoice(data.organizationId, data.id, data);
  
  // Immediate cache updates
  queryClient.setQueryData(QUERY_KEYS.INVOICE(data.organizationId, response.id), response);
  queryClient.setQueryData(QUERY_KEYS.INVOICES(data.organizationId), (cache: Invoice[]) => {
    if (!cache) return [response];
    return cache.map((invoice) => invoice.id === response.id ? response : invoice);
  });
  
  return response;
};

export const useUpdateInvoice = () => {
  const organizationId = useActiveOrganizationId();
  
  const {
    error,
    isPending: loading,
    mutateAsync: updateInvoiceFn,
  } = useMutation({
    mutationFn: (data: UpdateInvoiceForm & {organizationId: string, id: string}) => updateInvoice(data),
    onSuccess: (data) => {
      // Additional success handling if needed
    },
  });

  return { updateInvoice: updateInvoiceFn, loading, error };
}; 