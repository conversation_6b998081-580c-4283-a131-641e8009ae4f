import { QUERY_KEYS } from '@repo/constants';
import { getApiProvider } from '@repo/providers';
import { queryClient } from '../query-client';
import { Client } from '@repo/schemas';
import { useActiveOrganizationId } from '@repo/stores';
import { useMutation } from '@tanstack/react-query';

const provider = getApiProvider();

export const deleteClient = async (organizationId: string, clientId: string): Promise<void> => {
  await provider.deleteClient(organizationId, clientId);
  
  // Immediate cache updates
  queryClient.removeQueries({ queryKey: QUERY_KEYS.CLIENT(organizationId, clientId) });
  queryClient.setQueryData(QUERY_KEYS.CLIENTS(organizationId), (cache: Client[]) => {
    if (!cache) return [];
    return cache.filter((client) => client.id !== clientId);
  });
};

export const useDeleteClient = () => {
  const organizationId = useActiveOrganizationId();
  
  const {
    error,
    isPending: loading,
    mutateAsync: deleteClientFn,
  } = useMutation({
    mutationFn: (clientId: string) => 
      deleteClient(organizationId!, clientId),
    onMutate: async (clientId) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: QUERY_KEYS.CLIENTS(organizationId!) });
      
      // Snapshot the previous value
      const previousClients = queryClient.getQueryData<Client[]>(
        QUERY_KEYS.CLIENTS(organizationId!)
      );
      
      // Optimistically update to the new value
      queryClient.setQueryData<Client[]>(
        QUERY_KEYS.CLIENTS(organizationId!),
        (old: Client[] | undefined) => old ? old.filter((client: Client) => client.id !== clientId) : []
      );
      
      // Return a context object with the snapshotted value
      return { previousClients };
    },
    onError: (error, clientId, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousClients) {
        queryClient.setQueryData(
          QUERY_KEYS.CLIENTS(organizationId!),
          context.previousClients
        );
      }
      console.error('Failed to delete client:', error);
    },
  });

  return { deleteClient: deleteClientFn, loading, error };
}; 