import { QUERY_KEYS } from '@repo/constants';
import { getApiProvider } from '@repo/providers';
import { queryClient } from '../query-client';
import { Client } from '@repo/schemas';
import { UpdateClientDto } from '@repo/dtos';
import { useActiveOrganizationId } from '@repo/stores';
import { useMutation } from '@tanstack/react-query';

const provider = getApiProvider();

export const updateClientService = async (data: UpdateClientDto): Promise<Client> => {
  const response = await provider.updateClient(data.organizationId, data.id, data.updates);
  
  // Immediate cache updates
  queryClient.setQueryData(QUERY_KEYS.CLIENT(data.organizationId, response.id), response);
  queryClient.setQueryData(QUERY_KEYS.CLIENTS(data.organizationId), (cache: Client[]) => {
    if (!cache) return [response];
    return cache.map((client) => client.id === response.id ? response : client);
  });
  
  return response;
};

export const useUpdateClient = () => {
  const organizationId = useActiveOrganizationId();
  
  const {
    error,
    isPending: loading,
    mutateAsync: updateClientFn,
  } = useMutation({
    mutationFn: ({ clientId, updates }: { clientId: string; updates: Partial<Client> }) =>
      updateClientService({ organizationId: organizationId!, id: clientId, updates }),
    onMutate: async ({ clientId, updates }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: QUERY_KEYS.CLIENT(organizationId!, clientId) });
      
      // Snapshot the previous value
      const previousClient = queryClient.getQueryData<Client>(
        QUERY_KEYS.CLIENT(organizationId!, clientId)
      );
      
      // Optimistically update to the new value
      if (previousClient) {
        queryClient.setQueryData<Client>(
          QUERY_KEYS.CLIENT(organizationId!, clientId),
          { ...previousClient, ...updates }
        );
      }
      
      // Return a context object with the snapshotted value
      return { previousClient };
    },
    onError: (error, { clientId }, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousClient) {
        queryClient.setQueryData(
          QUERY_KEYS.CLIENT(organizationId!, clientId),
          context.previousClient
        );
      }
      console.error('Failed to update client:', error);
    },
  });

  return { updateClient: updateClientFn, loading, error };
}; 