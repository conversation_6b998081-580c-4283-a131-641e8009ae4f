import { QUERY_KEYS } from '@repo/constants';
import { CreateClientInput as ProviderCreateClientInput } from '@repo/providers';
import { getApiProvider } from '@repo/providers';
import { queryClient } from '../query-client';
import { Client, CreateClientForm as ZodCreateClientInput } from '@repo/schemas';
import { useActiveOrganizationId } from '@repo/stores';
import { useMutation } from '@tanstack/react-query';

const provider = getApiProvider();

export const createClient = async (organizationId: string, data: ZodCreateClientInput): Promise<Client> => {
  // Convert Zod input to provider input format
  const providerData: ProviderCreateClientInput = {
    name: data.name,
    company: data.company,
    contact: {
      email: data.contact.email,
      phone: data.contact.phone,
    },
    address: {
      fullAddress: data.address?.fullAddress,
    },
    photo: data.photo,
    isActive: data.isActive,
    defaultTaxExempt: data.defaultTaxExempt,
  };
  
  const response = await provider.createClient(organizationId, providerData);
  
  // Immediate cache updates
  queryClient.setQueryData(QUERY_KEYS.CLIENT(organizationId, response.id), response);
  queryClient.setQueryData(QUERY_KEYS.CLIENTS(organizationId), (cache: Client[]) => {
    if (!cache) return [response];
    return [...cache, response];
  });
  
  return response;
};

export const useCreateClient = () => {
  const organizationId = useActiveOrganizationId();
  
  const {
    error,
    isPending: loading,
    mutateAsync: createClientFn,
  } = useMutation({
    mutationFn: (data: ZodCreateClientInput) => 
      createClient(organizationId!, data),
    onSuccess: (data) => {
      // Additional success handling if needed
    },
  });

  return { createClient: createClientFn, loading, error };
}; 