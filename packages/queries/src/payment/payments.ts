import { getApiProvider } from '@repo/providers';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useMemo } from 'react';
import { CreatePayment, Payment, PaymentSummary, PaymentMethod } from '@repo/schemas';
import { useActiveOrganizationId } from '@repo/stores';

const provider = getApiProvider();

// Hook to fetch payments for a specific invoice
export function useInvoicePayments(invoiceId: string) {
  const organizationId = useActiveOrganizationId();

  return useQuery({
    queryKey: ['payments', 'invoice', organizationId, invoiceId],
    queryFn: () => provider.getInvoicePayments(organizationId!, invoiceId),
    enabled: !!organizationId && !!invoiceId,
  });
}

// Hook to get payment summary for an invoice
export function usePaymentSummary(invoiceId: string) {
  const organizationId = useActiveOrganizationId();

  return useQuery({
    queryKey: ['payment-summary', organizationId, invoiceId],
    queryFn: () => provider.getPaymentSummary(organizationId!, invoiceId),
    enabled: !!organizationId && !!invoiceId,
  });
}

// Hook to create a payment record with React Query
export function useCreatePayment() {
  const queryClient = useQueryClient();
  const organizationId = useActiveOrganizationId();

  return useMutation({
    mutationFn: async (paymentData: {
      invoiceId: string;
      amount: number;
      method: PaymentMethod;
      paymentDate: Date;
      reference: string;
      notes: string;
      type: 'payment' | 'refund';
    }) => {
      if (!organizationId) {
        throw new Error('No organization selected');
      }

      const createPaymentData: CreatePayment = {
        invoiceId: paymentData.invoiceId,
        organizationId,
        amount: paymentData.amount,
        method: paymentData.method,
        type: paymentData.type,
        paymentDate: paymentData.paymentDate,
        reference: paymentData.reference,
        notes: paymentData.notes,
        clientNotified: true,
        receiptSent: true,
        attachments: [],
      };

      return provider.createPayment(createPaymentData);
    },
    onSuccess: (newPayment) => {
      // Invalidate and refetch payment-related queries
      queryClient.invalidateQueries({ 
        queryKey: ['payments', 'invoice', organizationId, newPayment.invoiceId] 
      });
      queryClient.invalidateQueries({ 
        queryKey: ['payment-summary', organizationId, newPayment.invoiceId] 
      });
      // Also invalidate invoice queries in case totals changed
      queryClient.invalidateQueries({ 
        queryKey: ['invoice', organizationId, newPayment.invoiceId] 
      });
    },
  });
}

// Hook to calculate payment status from payments
export function usePaymentStatus(payments: Payment[] = [], invoiceTotal: number = 0) {
  return useMemo(() => {
    const paymentTransactions = payments.filter(p => p.type === 'payment' && p.status === 'completed');
    const refundTransactions = payments.filter(p => p.type === 'refund' && p.status === 'completed');
    const feeTransactions = payments.filter(p => p.type === 'fee' && p.status === 'completed');
    
    const totalPaid = paymentTransactions.reduce((sum, p) => sum + p.amount, 0);
    const totalRefunded = refundTransactions.reduce((sum, p) => sum + Math.abs(p.amount), 0);
    const totalFees = feeTransactions.reduce((sum, p) => sum + p.amount, 0);
    
    const netPaid = totalPaid - totalRefunded;
    const adjustedTotal = invoiceTotal + totalFees;
    const remainingBalance = adjustedTotal - netPaid;
    
    // Determine payment status
    let status: 'unpaid' | 'partially_paid' | 'paid' | 'overpaid' | 'refunded' = 'unpaid';
    
    // Special case: if invoice total is 0 or negative, don't consider it "paid"
    // This handles draft invoices or invoices with no line items
    if (adjustedTotal <= 0) {
      status = 'unpaid';
    } else if (netPaid === 0 && totalRefunded > 0) {
      status = 'refunded';
    } else if (netPaid >= adjustedTotal) {
      status = netPaid > adjustedTotal ? 'overpaid' : 'paid';
    } else if (netPaid > 0) {
      status = 'partially_paid';
    }
    
    // Calculate payment progress percentage
    const progressPercentage = adjustedTotal > 0 ? Math.min((netPaid / adjustedTotal) * 100, 100) : 0;
    
    return {
      totalPaid,
      totalRefunded,
      totalFees,
      netPaid,
      remainingBalance: Math.max(remainingBalance, 0),
      adjustedTotal,
      status,
      progressPercentage,
      paymentCount: paymentTransactions.length,
      refundCount: refundTransactions.length,
      lastPayment: paymentTransactions.sort((a, b) => 
        new Date(b.paymentDate).getTime() - new Date(a.paymentDate).getTime()
      )[0],
    };
  }, [payments, invoiceTotal]);
}

// Function to create a payment record (deprecated - use useCreatePayment hook)
export async function createPayment(payment: CreatePayment): Promise<Payment> {
  const provider = getApiProvider();
  return provider.createPayment(payment);
}

// Function to format payment method for display
export function formatPaymentMethod(method: string): string {
  const methodMap: Record<string, string> = {
    'bank_transfer': 'Bank Transfer',
    'credit_card': 'Credit Card', 
    'debit_card': 'Debit Card',
    'cash': 'Cash',
    'check': 'Check',
    'paypal': 'PayPal',
    'stripe': 'Stripe',
    'wire_transfer': 'Wire Transfer',
    'online_banking': 'Online Banking',
    'mobile_payment': 'Mobile Payment',
    'cryptocurrency': 'Cryptocurrency',
    'other': 'Other',
  };
  
  return methodMap[method] || method;
}

// Function to get payment method icon
export function getPaymentMethodIcon(method: string): string {
  const iconMap: Record<string, string> = {
    'bank_transfer': 'business-outline',
    'credit_card': 'card-outline',
    'debit_card': 'card-outline', 
    'cash': 'cash-outline',
    'check': 'receipt-outline',
    'paypal': 'logo-paypal',
    'stripe': 'card-outline',
    'wire_transfer': 'swap-horizontal-outline',
    'online_banking': 'globe-outline',
    'mobile_payment': 'phone-portrait-outline',
    'cryptocurrency': 'logo-bitcoin',
    'other': 'ellipsis-horizontal-outline',
  };
  
  return iconMap[method] || 'card-outline';
} 