import { QUERY_KEYS } from '@repo/constants';
import { getApiProvider } from '@repo/providers';
import { SecuritySettings, UpdateSecuritySettingsInput } from '@repo/schemas';
import { useCurrentUserId } from '@repo/stores';
import { queryClient } from '../query-client';
import { useMutation, useQuery } from '@tanstack/react-query';

const provider = getApiProvider();

export const fetchUserSecurity = async (userId: string): Promise<SecuritySettings | null> => {
  return await provider.getUserSecurity(userId);
};

export const updateUserSecurity = async (userId: string, updates: UpdateSecuritySettingsInput): Promise<SecuritySettings> => {
  const response = await provider.updateUserSecurity(userId, updates);
  
  // Update cache
  queryClient.setQueryData(QUERY_KEYS.USER_SECURITY(userId), response);
  
  return response;
};

export const enableTwoFactor = async (userId: string): Promise<{ secret: string; qrCode: string }> => {
  return await provider.enableTwoFactor(userId);
};

export const verifyTwoFactor = async (userId: string, code: string): Promise<boolean> => {
  const result = await provider.verifyTwoFactor(userId, code);
  
  if (result) {
    // Invalidate security settings to refresh
    queryClient.invalidateQueries({ queryKey: QUERY_KEYS.USER_SECURITY(userId) });
  }
  
  return result;
};

export const disableTwoFactor = async (userId: string): Promise<void> => {
  await provider.disableTwoFactor(userId);
  
  // Invalidate security settings to refresh
  queryClient.invalidateQueries({ queryKey: QUERY_KEYS.USER_SECURITY(userId) });
};

export const useUserSecurity = (userId?: string) => {
  const currentUserId = useCurrentUserId();
  const targetUserId = userId || currentUserId;
  
  const {
    error,
    isPending: loading,
    data: security,
  } = useQuery({
    queryKey: QUERY_KEYS.USER_SECURITY(targetUserId!),
    queryFn: () => fetchUserSecurity(targetUserId!),
    enabled: !!targetUserId,
  });

  return { security, loading, error };
};

export const useUpdateUserSecurity = () => {
  return useMutation({
    mutationFn: ({ userId, updates }: { userId: string; updates: UpdateSecuritySettingsInput }) =>
      updateUserSecurity(userId, updates),
  });
}; 