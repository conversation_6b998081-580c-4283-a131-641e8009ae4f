import { getApi<PERSON>rovider } from '@repo/providers';
import { ContactFormInput, FAQ, HelpArticle, HelpCategory, KnowledgeBaseSearchInput, SearchResult } from '@repo/schemas';
import { useMutation, useQuery } from '@tanstack/react-query';

const provider = getApiProvider();

export const fetchHelpCategories = async (): Promise<HelpCategory[]> => {
  return await provider.getHelpCategories();
};

export const fetchHelpArticles = async (categoryId?: string): Promise<HelpArticle[]> => {
  return await provider.getHelpArticles(categoryId);
};

export const fetchHelpArticle = async (articleId: string): Promise<HelpArticle | null> => {
  return await provider.getHelpArticle(articleId);
};

export const fetchFAQs = async (categoryId?: string): Promise<FAQ[]> => {
  return await provider.getFAQs(categoryId);
};

export const searchKnowledgeBase = async (searchInput: KnowledgeBaseSearchInput): Promise<SearchResult[]> => {
  return await provider.searchKnowledgeBase(searchInput);
};

export const submitContactForm = async (contactForm: ContactFormInput): Promise<void> => {
  return await provider.submitContactForm(contactForm);
};

export const rateApp = async (rating: number, review?: string): Promise<void> => {
  return await provider.rateApp(rating, review);
};

export const useHelpCategories = () => {
  const {
    error,
    isPending: loading,
    data: categories,
  } = useQuery({
    queryKey: ['help-categories'],
    queryFn: fetchHelpCategories,
    staleTime: 30 * 60 * 1000, // 30 minutes
    gcTime: 60 * 60 * 1000, // 1 hour
  });

  return { categories, loading, error };
};

export const useHelpArticles = (categoryId?: string) => {
  const {
    error,
    isPending: loading,
    data: articles,
  } = useQuery({
    queryKey: ['help-articles', { categoryId }],
    queryFn: () => fetchHelpArticles(categoryId),
    staleTime: 15 * 60 * 1000, // 15 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  });

  return { articles, loading, error };
};

export const useHelpArticle = (articleId: string) => {
  const {
    error,
    isPending: loading,
    data: article,
  } = useQuery({
    queryKey: ['help-article', { articleId }],
    queryFn: () => fetchHelpArticle(articleId),
    enabled: !!articleId,
    staleTime: 15 * 60 * 1000, // 15 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  });

  return { article, loading, error };
};

export const useFAQs = (categoryId?: string) => {
  const {
    error,
    isPending: loading,
    data: faqs,
  } = useQuery({
    queryKey: ['faqs', { categoryId }],
    queryFn: () => fetchFAQs(categoryId),
    staleTime: 30 * 60 * 1000, // 30 minutes
    gcTime: 60 * 60 * 1000, // 1 hour
  });

  return { faqs, loading, error };
};

export const useKnowledgeBaseSearch = () => {
  const {
    error,
    isPending: loading,
    mutateAsync: searchFn,
    data: searchResults,
  } = useMutation({
    mutationFn: (searchInput: KnowledgeBaseSearchInput) =>
      searchKnowledgeBase(searchInput),
    onError: (error) => {
      console.error('Failed to search knowledge base:', error);
    },
  });

  return { search: searchFn, searchResults, loading, error };
};

export const useSubmitContactForm = () => {
  const {
    error,
    isPending: loading,
    mutateAsync: submitContactFn,
  } = useMutation({
    mutationFn: (contactForm: ContactFormInput) =>
      submitContactForm(contactForm),
    onSuccess: () => {
      // Could show success message here
      console.log('Contact form submitted successfully');
    },
    onError: (error) => {
      console.error('Failed to submit contact form:', error);
    },
  });

  return { submitContact: submitContactFn, loading, error };
};

export const useRateApp = () => {
  const {
    error,
    isPending: loading,
    mutateAsync: rateAppFn,
  } = useMutation({
    mutationFn: ({ rating, review }: { rating: number; review?: string }) =>
      rateApp(rating, review),
    onSuccess: () => {
      console.log('App rating submitted successfully');
    },
    onError: (error) => {
      console.error('Failed to submit app rating:', error);
    },
  });

  return { rateApp: rateAppFn, loading, error };
}; 