import { getApiProvider } from '@repo/providers';
import { AppDefaults, BusinessTypeTemplate, SupportedCurrency, UpdateAppDefaultsInput } from '@repo/schemas';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

const provider = getApiProvider();

export const fetchAppDefaults = async (organizationId: string): Promise<AppDefaults | null> => {
  return await provider.getAppDefaults(organizationId);
};

export const updateAppDefaults = async (data: UpdateAppDefaultsInput & { organizationId: string }): Promise<AppDefaults> => {
  const { organizationId, ...updates } = data;
  const response = await provider.updateAppDefaults(organizationId, updates);
  return response;
};

export const fetchSupportedCurrencies = async (): Promise<SupportedCurrency[]> => {
  return await provider.getSupportedCurrencies();
};

export const fetchBusinessTypeTemplates = async (): Promise<BusinessTypeTemplate[]> => {
  return await provider.getBusinessTypeTemplates();
};

export const useAppDefaults = (organizationId: string) => {
  const {
    error,
    isPending: loading,
    data: appDefaults,
  } = useQuery({
    queryKey: ['app-defaults', { organizationId }],
    queryFn: () => fetchAppDefaults(organizationId),
    enabled: !!organizationId,
    staleTime: 10 * 60 * 1000, // 10 minutes - defaults don't change frequently
    gcTime: 30 * 60 * 1000, // 30 minutes
  });

  return { appDefaults, loading, error };
};

export const useSupportedCurrencies = () => {
  const {
    error,
    isPending: loading,
    data: currencies,
  } = useQuery({
    queryKey: ['supported-currencies'],
    queryFn: fetchSupportedCurrencies,
    staleTime: 60 * 60 * 1000, // 1 hour - currencies rarely change
    gcTime: 2 * 60 * 60 * 1000, // 2 hours
  });

  return { currencies, loading, error };
};

export const useBusinessTypeTemplates = () => {
  const {
    error,
    isPending: loading,
    data: templates,
  } = useQuery({
    queryKey: ['business-type-templates'],
    queryFn: fetchBusinessTypeTemplates,
    staleTime: 60 * 60 * 1000, // 1 hour - templates rarely change
    gcTime: 2 * 60 * 60 * 1000, // 2 hours
  });

  return { templates, loading, error };
};

export const useUpdateAppDefaults = () => {
  const queryClient = useQueryClient();
  
  const {
    error,
    isPending: loading,
    mutateAsync: updateDefaultsFn,
  } = useMutation({
    mutationFn: (data: UpdateAppDefaultsInput & { organizationId: string }) =>
      updateAppDefaults(data),
    onSuccess: (updatedDefaults) => {
      // Update the app defaults cache
      queryClient.setQueryData(['app-defaults', { organizationId: updatedDefaults.organizationId }], updatedDefaults);
      
      // Invalidate related queries that might depend on these defaults
      queryClient.invalidateQueries({
        queryKey: ['app-defaults']
      });
      
      // Invalidate invoice queries since defaults affect invoice creation
      queryClient.invalidateQueries({
        queryKey: ['invoices']
      });
    },
    onError: (error) => {
      console.error('Failed to update app defaults:', error);
    },
  });

  return { updateDefaults: updateDefaultsFn, loading, error };
}; 