import { QUERY_KEYS } from '@repo/constants';
import { CreateServiceInput as ProviderCreateServiceInput } from '@repo/providers';
import { getApiProvider } from '@repo/providers';
import { queryClient } from '../query-client';
import { Service, CreateServiceForm as ZodCreateServiceInput } from '@repo/schemas';
import { useActiveOrganizationId } from '@repo/stores';
import { useMutation } from '@tanstack/react-query';

const provider = getApiProvider();

export const createService = async (organizationId: string, data: ZodCreateServiceInput): Promise<Service> => {
  // Convert Zod input to provider input format
  // Handle unit type conversion - provider only accepts specific units
  let providerUnit: 'fixed' | 'hour' | 'day' | 'month' | 'project';
  
  if (typeof data.pricing.unit === 'string' && 
      ['hour', 'day', 'month', 'project', 'fixed'].includes(data.pricing.unit)) {
    providerUnit = data.pricing.unit as 'fixed' | 'hour' | 'day' | 'month' | 'project';
  } else {
    // For any custom unit or unsupported unit, default to 'fixed'
    providerUnit = 'fixed';
  }
  
  const providerData: ProviderCreateServiceInput = {
    name: data.name,
    description: data.description || '', // Provider requires description, provide default
    pricing: {
      rate: data.pricing.rate,
      unit: providerUnit,
      currency: data.pricing.currency,
    },
    isActive: data.isActive,
    taxable: data.taxable,
    tags: data.tags,
  };
  
  const response = await provider.createService(organizationId, providerData);
  
  // Immediate cache updates
  queryClient.setQueryData(QUERY_KEYS.SERVICE(organizationId, response.id), response);
  queryClient.setQueryData(QUERY_KEYS.SERVICES(organizationId), (cache: Service[]) => {
    if (!cache) return [response];
    return [...cache, response];
  });
  
  return response;
};

export const useCreateService = () => {
  const organizationId = useActiveOrganizationId();
  
  const {
    error,
    isPending: loading,
    mutateAsync: createServiceFn,
  } = useMutation({
    mutationFn: (data: ZodCreateServiceInput) => 
      createService(organizationId!, data),
    onSuccess: (data) => {
      // Additional success handling if needed
    },
  });

  return { createService: createServiceFn, loading, error };
}; 