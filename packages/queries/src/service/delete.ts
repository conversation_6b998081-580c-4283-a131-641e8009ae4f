import { QUERY_KEYS } from '@repo/constants';
import { getApiProvider } from '@repo/providers';
import { queryClient } from '../query-client';
import { Service } from '@repo/schemas';
import { useActiveOrganizationId } from '@repo/stores';
import { useMutation } from '@tanstack/react-query';

const provider = getApiProvider();

export const deleteService = async (organizationId: string, serviceId: string): Promise<void> => {
  await provider.deleteService(organizationId, serviceId);
  
  // Immediate cache updates
  queryClient.removeQueries({ queryKey: QUERY_KEYS.SERVICE(organizationId, serviceId) });
  queryClient.setQueryData(QUERY_KEYS.SERVICES(organizationId), (cache: Service[]) => {
    if (!cache) return [];
    return cache.filter((service) => service.id !== serviceId);
  });
};

export const useDeleteService = () => {
  const organizationId = useActiveOrganizationId();
  
  const {
    error,
    isPending: loading,
    mutateAsync: deleteServiceFn,
  } = useMutation({
    mutationFn: (serviceId: string) => 
      deleteService(organizationId!, serviceId),
    onMutate: async (serviceId) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: QUERY_KEYS.SERVICES(organizationId!) });
      
      // Snapshot the previous value
      const previousServices = queryClient.getQueryData<Service[]>(
        QUERY_KEYS.SERVICES(organizationId!)
      );
      
      // Optimistically update to the new value
      queryClient.setQueryData<Service[]>(
        QUERY_KEYS.SERVICES(organizationId!),
        (old: Service[] | undefined) => old ? old.filter((service: Service) => service.id !== serviceId) : []
      );
      
      // Return a context object with the snapshotted value
      return { previousServices };
    },
    onError: (error, serviceId, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousServices) {
        queryClient.setQueryData(
          QUERY_KEYS.SERVICES(organizationId!),
          context.previousServices
        );
      }
      console.error('Failed to delete service:', error);
    },
  });

  return { deleteService: deleteServiceFn, loading, error };
}; 