import { QUERY_KEYS } from '@repo/constants';
import { getApiProvider } from '@repo/providers';
import { queryClient } from '../query-client';
import { Service } from '@repo/schemas';
import { UpdateServiceDto } from '@repo/dtos';
import { useActiveOrganizationId } from '@repo/stores';
import { useMutation } from '@tanstack/react-query';

const provider = getApiProvider();

export const updateServiceService = async (data: UpdateServiceDto): Promise<Service> => {
  const response = await provider.updateService(data.organizationId, data.id, data.updates);
  
  // Immediate cache updates
  queryClient.setQueryData(QUERY_KEYS.SERVICE(data.organizationId, response.id), response);
  queryClient.setQueryData(QUERY_KEYS.SERVICES(data.organizationId), (cache: Service[]) => {
    if (!cache) return [response];
    return cache.map((service) => service.id === response.id ? response : service);
  });
  
  return response;
};

export const useUpdateService = () => {
  const organizationId = useActiveOrganizationId();
  
  const {
    error,
    isPending: loading,
    mutateAsync: updateServiceFn,
  } = useMutation({
    mutationFn: ({ serviceId, updates }: { serviceId: string; updates: Partial<Service> }) =>
      updateServiceService({ organizationId: organizationId!, id: serviceId, updates }),
    onMutate: async ({ serviceId, updates }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: QUERY_KEYS.SERVICE(organizationId!, serviceId) });
      
      // Snapshot the previous value
      const previousService = queryClient.getQueryData<Service>(
        QUERY_KEYS.SERVICE(organizationId!, serviceId)
      );
      
      // Optimistically update to the new value
      if (previousService) {
        queryClient.setQueryData<Service>(
          QUERY_KEYS.SERVICE(organizationId!, serviceId),
          { ...previousService, ...updates }
        );
      }
      
      // Return a context object with the snapshotted value
      return { previousService };
    },
    onSuccess: (updatedService, { serviceId }) => {
      // Invalidate service activities cache to show new activity records
      queryClient.invalidateQueries({ 
        queryKey: ['service-activities', organizationId, serviceId] 
      });
    },
    onError: (error, { serviceId }, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousService) {
        queryClient.setQueryData(
          QUERY_KEYS.SERVICE(organizationId!, serviceId),
          context.previousService
        );
      }
      console.error('Failed to update service:', error);
    },
  });

  return { updateService: updateServiceFn, loading, error };
}; 