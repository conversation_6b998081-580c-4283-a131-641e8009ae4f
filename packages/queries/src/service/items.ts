import { QUERY_KEYS } from '@repo/constants';
import { getApiProvider } from '@repo/providers';
import { queryClient } from '../query-client';
import { Service } from '@repo/schemas';
import { UpdateServiceDto } from '@repo/dtos';
import { useActiveOrganizationId } from '@repo/stores';
import { useQuery } from '@tanstack/react-query';
import { debounce } from 'lodash';

const itemProvider = getApiProvider();

// Item functions (renamed from service functions)
export const fetchItems = async (organizationId: string): Promise<Service[]> => {
  const response = await itemProvider.getServices(organizationId);
  return response;
};

export const fetchItem = async (organizationId: string, itemId: string): Promise<Service> => {
  const response = await itemProvider.getService(organizationId, itemId);
  return response;
};

export const updateItem = async (data: UpdateServiceDto): Promise<Service> => {
  const response = await itemProvider.updateService(data.organizationId, data.id, data);
  
  // Immediate cache updates
  queryClient.setQueryData(QUERY_KEYS.SERVICE(data.organizationId, response.id), response);
  queryClient.setQueryData(QUERY_KEYS.SERVICES(data.organizationId), (cache: Service[]) => {
    if (!cache) return [response];
    return cache.map((item) => item.id === response.id ? response : item);
  });

  // Invalidate item activities cache to show new activity records
  queryClient.invalidateQueries({ 
    queryKey: ['service-activities', data.organizationId, data.id] 
  });
  
  return response;
};

// Debounced version for real-time editing
export const debouncedUpdateItem = debounce(updateItem, 1000);

// Clean hooks - renamed from service hooks to item hooks
export const useItems = () => {
  const organizationId = useActiveOrganizationId();
  
  const {
    error,
    isPending: loading,
    data: items,
  } = useQuery({
    queryKey: QUERY_KEYS.SERVICES(organizationId!),
    queryFn: () => fetchItems(organizationId!),
    enabled: !!organizationId,
  });

  return { items, loading, error };
};

export const useItem = (itemId: string) => {
  const organizationId = useActiveOrganizationId();
  
  const {
    error,
    isPending: loading,
    data: item,
  } = useQuery({
    queryKey: QUERY_KEYS.SERVICE(organizationId!, itemId),
    queryFn: () => fetchItem(organizationId!, itemId),
    enabled: !!organizationId && !!itemId,
  });

  return { item, loading, error };
}; 

// Hook to get item activities (renamed from service activities)
export function useItemActivities(itemId: string) {
  const organizationId = useActiveOrganizationId();
  
  return useQuery({
    queryKey: ['service-activities', organizationId, itemId],
    queryFn: async () => {
      if (!organizationId || !itemId) return [];
      return await itemProvider.getServiceActivities(organizationId, itemId);
    },
    enabled: !!organizationId && !!itemId,
  });
} 

// Legacy exports for backward compatibility (keeping original names for now)
export const fetchServices = fetchItems;
export const fetchService = fetchItem;
export const updateService = updateItem;
export const debouncedUpdateService = debouncedUpdateItem;
export const useServices = useItems;
export const useService = useItem;
export const useServiceActivities = useItemActivities; 