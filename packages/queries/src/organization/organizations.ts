import { QUERY_KEYS } from '@repo/constants';
import { getApiProvider } from '@repo/providers';
import { Organization } from '@repo/schemas';
import { useQuery } from '@tanstack/react-query';

const provider = getApiProvider();

// Service functions
export const fetchOrganizations = async (): Promise<Organization[]> => {
  const response = await provider.getOrganizations();
  return response;
};

export const fetchOrganization = async (organizationId: string): Promise<Organization> => {
  const response = await provider.getOrganization(organizationId);
  return response;
};

// Clean hooks
export const useOrganizations = () => {
  const {
    error,
    isPending: loading,
    data: organizations,
  } = useQuery({
    queryKey: QUERY_KEYS.ORGANIZATIONS,
    queryFn: () => fetchOrganizations(),
    staleTime: 5 * 60 * 1000, // 5 minutes - organizations change rarely
  });

  return { organizations, loading, error };
};

export const useOrganization = (organizationId: string) => {
  const {
    error,
    isPending: loading,
    data: organization,
  } = useQuery({
    queryKey: QUERY_KEYS.ORGANIZATION(organizationId),
    queryFn: () => fetchOrganization(organizationId),
    enabled: !!organizationId,
    staleTime: 5 * 60 * 1000, // 5 minutes - organizations change rarely
  });

  return { organization, loading, error };
}; 