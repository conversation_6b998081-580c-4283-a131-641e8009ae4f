// Create organization is not yet supported by the API provider
// This service will be implemented when the provider interface is updated

import { QUERY_KEYS } from '@repo/constants';
import { getApiProvider } from '@repo/providers';
import { queryClient } from '../query-client';
import { CreateOrganizationForm as CreateOrganizationInput, Organization } from '@repo/schemas';
import { useMutation } from '@tanstack/react-query';

const provider = getApiProvider();

export const createOrganization = async (data: CreateOrganizationInput): Promise<Organization> => {
  const response = await provider.createOrganization(data);
  
  // Update cache
  queryClient.setQueryData(QUERY_KEYS.ORGANIZATIONS, (cache: Organization[]) => {
    if (!cache) return [response];
    return [...cache, response];
  });
  
  return response;
};

export const useCreateOrganization = () => {
  return useMutation({
    mutationFn: createOrganization,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.ORGANIZATIONS });
    },
  });
}; 