// Update organization is not yet supported by the API provider
// This service will be implemented when the provider interface is updated

import { QUERY_KEYS } from '@repo/constants';
import { getApiProvider } from '@repo/providers';
import { queryClient } from '../query-client';
import { Organization } from '@repo/schemas';
import { UpdateOrganizationForm as UpdateOrganizationInput } from '@repo/schemas';
import { useMutation } from '@tanstack/react-query';

const provider = getApiProvider();

export const updateOrganization = async (organizationId: string, updates: UpdateOrganizationInput): Promise<Organization> => {
  const response = await provider.updateOrganization(organizationId, updates);
  
  // Update cache
  queryClient.setQueryData(QUERY_KEYS.ORGANIZATION(response.id), response);
  queryClient.setQueryData(QUERY_KEYS.ORGANIZATIONS, (cache: Organization[]) => {
    if (!cache) return [response];
    return cache.map((org) => org.id === response.id ? response : org);
  });
  
  return response;
};

export const useUpdateOrganization = () => {
  return useMutation({
    mutationFn: ({ organizationId, updates }: { organizationId: string; updates: UpdateOrganizationInput }) => 
      updateOrganization(organizationId, updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.ORGANIZATIONS });
    },
  });
}; 