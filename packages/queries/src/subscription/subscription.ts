import { getApi<PERSON>rovider } from '@repo/providers';
import { Subscription, SubscriptionChangeRequest, SubscriptionPlan } from '@repo/schemas';
import { useCurrentUserId } from '@repo/stores';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

const provider = getApiProvider();

export const fetchUserSubscription = async (userId: string): Promise<Subscription | null> => {
  return await provider.getUserSubscription(userId);
};

export const fetchSubscriptionPlans = async (): Promise<SubscriptionPlan[]> => {
  return await provider.getSubscriptionPlans();
};

export const changeUserSubscription = async (data: SubscriptionChangeRequest & { userId: string }): Promise<Subscription> => {
  const { userId, ...changeRequest } = data;
  const response = await provider.changeSubscription(userId, changeRequest);
  return response;
};

export const cancelUserSubscription = async (data: { userId: string; cancelAtPeriodEnd?: boolean }): Promise<Subscription> => {
  const { userId, cancelAtPeriodEnd = true } = data;
  const response = await provider.cancelSubscription(userId, cancelAtPeriodEnd);
  return response;
};

export const useUserSubscription = (userId?: string) => {
  const currentUserId = useCurrentUserId();
  const targetUserId = userId || currentUserId;
  
  const {
    error,
    isPending: loading,
    data: subscription,
  } = useQuery({
    queryKey: ['user-subscription', { userId: targetUserId }],
    queryFn: () => fetchUserSubscription(targetUserId!),
    enabled: !!targetUserId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes - subscription data changes less frequently
  });

  return { subscription, loading, error };
};

export const useSubscriptionPlans = () => {
  const {
    error,
    isPending: loading,
    data: plans,
  } = useQuery({
    queryKey: ['subscription-plans'],
    queryFn: () => fetchSubscriptionPlans(),
    staleTime: 30 * 60 * 1000, // 30 minutes - plans change infrequently
    gcTime: 60 * 60 * 1000, // 1 hour
  });

  return { plans: plans || [], loading, error };
};

export const useChangeSubscription = () => {
  const queryClient = useQueryClient();
  const currentUserId = useCurrentUserId();
  
  const {
    error,
    isPending: loading,
    mutateAsync: changeSubscriptionFn,
  } = useMutation({
    mutationFn: (changeRequest: SubscriptionChangeRequest) => 
      changeUserSubscription({ ...changeRequest, userId: currentUserId! }),
    onSuccess: (updatedSubscription) => {
      // Update subscription cache
      queryClient.setQueryData(['user-subscription', { userId: currentUserId }], updatedSubscription);
      
      // Invalidate related queries
      queryClient.invalidateQueries({
        queryKey: ['user-subscription']
      });
      
      // Also invalidate any usage or feature queries that depend on subscription
      queryClient.invalidateQueries({
        queryKey: ['subscription-limits']
      });
    },
    onError: (error) => {
      console.error('Failed to change subscription:', error);
    },
  });

  return { changeSubscription: changeSubscriptionFn, loading, error };
};

export const useCancelSubscription = () => {
  const queryClient = useQueryClient();
  const currentUserId = useCurrentUserId();
  
  const {
    error,
    isPending: loading,
    mutateAsync: cancelSubscriptionFn,
  } = useMutation({
    mutationFn: (options: { cancelAtPeriodEnd?: boolean } = {}) =>
      cancelUserSubscription({ userId: currentUserId!, ...options }),
    onSuccess: (updatedSubscription) => {
      // Update subscription cache
      queryClient.setQueryData(['user-subscription', { userId: currentUserId }], updatedSubscription);
      
      // Invalidate related queries
      queryClient.invalidateQueries({
        queryKey: ['user-subscription']
      });
      
      // Invalidate subscription-dependent queries
      queryClient.invalidateQueries({
        queryKey: ['subscription-limits']
      });
    },
    onError: (error) => {
      console.error('Failed to cancel subscription:', error);
    },
  });

  return { cancelSubscription: cancelSubscriptionFn, loading, error };
}; 