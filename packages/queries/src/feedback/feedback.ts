import { getApi<PERSON>rovider } from '@repo/providers';
import { CreateFeedbackInput, Feedback, FeedbackCategory, UpdateFeedbackInput } from '@repo/schemas';
import { useCurrentUserId } from '@repo/stores';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

const provider = getApiProvider();

export const fetchFeedbackCategories = async (): Promise<FeedbackCategory[]> => {
  return await provider.getFeedbackCategories();
};

export const submitFeedback = async (userId: string, feedbackData: Omit<CreateFeedbackInput, 'userId'>): Promise<Feedback> => {
  const response = await provider.submitFeedback(userId, feedbackData as CreateFeedbackInput);
  return response;
};

export const fetchUserFeedback = async (userId: string): Promise<Feedback[]> => {
  return await provider.getUserFeedback(userId);
};

export const updateFeedback = async (data: UpdateFeedbackInput & { feedbackId: string }): Promise<Feedback> => {
  const { feedbackId, ...updates } = data;
  const response = await provider.updateFeedback(feedbackId, updates);
  return response;
};



export const useFeedbackCategories = () => {
  const {
    error,
    isPending: loading,
    data: categories,
  } = useQuery({
    queryKey: ['feedback-categories'],
    queryFn: fetchFeedbackCategories,
    staleTime: 30 * 60 * 1000, // 30 minutes - categories rarely change
    gcTime: 60 * 60 * 1000, // 1 hour
  });

  return { categories, loading, error };
};

export const useUserFeedback = (userId?: string) => {
  const currentUserId = useCurrentUserId();
  const targetUserId = userId || currentUserId;
  
  const {
    error,
    isPending: loading,
    data: feedback,
  } = useQuery({
    queryKey: ['user-feedback', { userId: targetUserId }],
    queryFn: () => fetchUserFeedback(targetUserId!),
    enabled: !!targetUserId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
  });

  return { feedback, loading, error };
};

export const useSubmitFeedback = () => {
  const queryClient = useQueryClient();
  const currentUserId = useCurrentUserId();
  
  const {
    error,
    isPending: loading,
    mutateAsync: submitFeedbackFn,
  } = useMutation({
    mutationFn: (feedbackData: Omit<CreateFeedbackInput, 'userId'>) =>
      submitFeedback(currentUserId!, feedbackData),
    onSuccess: (newFeedback) => {
      // Invalidate user feedback queries
      queryClient.invalidateQueries({
        queryKey: ['user-feedback', { userId: currentUserId }]
      });
      
      // Could also add optimistic update here
      queryClient.setQueryData(['user-feedback', { userId: currentUserId }], (oldData: Feedback[] | undefined) => {
        if (oldData) {
          return [newFeedback, ...oldData];
        }
        return [newFeedback];
      });
    },
    onError: (error) => {
      console.error('Failed to submit feedback:', error);
    },
  });

  return { submitFeedback: submitFeedbackFn, loading, error };
};

export const useUpdateFeedback = () => {
  const queryClient = useQueryClient();
  const currentUserId = useCurrentUserId();
  
  const {
    error,
    isPending: loading,
    mutateAsync: updateFeedbackFn,
  } = useMutation({
    mutationFn: (data: UpdateFeedbackInput & { feedbackId: string }) =>
      updateFeedback(data),
    onSuccess: (updatedFeedback) => {
      // Update the user feedback list
      queryClient.setQueryData(['user-feedback', { userId: currentUserId }], (oldData: Feedback[] | undefined) => {
        if (oldData) {
          return oldData.map(feedback => 
            feedback.id === updatedFeedback.id ? updatedFeedback : feedback
          );
        }
        return [updatedFeedback];
      });
      
      // Invalidate to ensure fresh data
      queryClient.invalidateQueries({
        queryKey: ['user-feedback']
      });
    },
    onError: (error) => {
      console.error('Failed to update feedback:', error);
    },
  });

  return { updateFeedback: updateFeedbackFn, loading, error };
}; 