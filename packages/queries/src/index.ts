// Main exports for @repo/queries package

// Configuration and keys handled by shared packages

// Client services
export * from './client/clients';
export * from './client/create';
export * from './client/update';
export * from './client/delete';

// Invoice services
export * from './invoice/invoices';
export * from './invoice/create';
export { useUpdateInvoice } from './invoice/update';
export * from './invoice/delete';

// User services
export * from './user/profile';
export * from './user/update';
export * from './user/security';
export * from './feedback/feedback';

// Payment services
export * from './payment/payments';
export * from './payment-method/payment-methods';

// Subscription services
export * from './subscription/subscription';

// Support services
export * from './support/support';

// Template services
export * from './templates/templates';

// Item services (formerly called services)
export * from './service/items';
export * from './service/create';
export * from './service/update';
export * from './service/delete';

// Organization services
export * from './organization/organizations';
export * from './organization/create';
export * from './organization/update';

// Query client for internal use
export * from './query-client';

// Legacy exports for backward compatibility
export { 
  useServices,
  useService,
  useServiceActivities,
  fetchServices,
  fetchService,
  updateService,
  debouncedUpdateService
} from './service/items';

// Re-export with new names
export {
  useItems,
  useItem,
  useItemActivities,
  fetchItems,
  fetchItem,
  updateItem,
  debouncedUpdateItem
} from './service/items';

// Explicitly export specific functions needed by mobile app
export { useDeleteService } from './service/delete';
export { useCreateService } from './service/create';
export { useUpdateService } from './service/update';
