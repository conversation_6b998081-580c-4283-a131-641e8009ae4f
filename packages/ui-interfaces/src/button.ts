import { ReactNode } from 'react';

/**
 * Unified button variant system that works across web and mobile
 * Maps to both shadcn/ui variants and mobile variants
 */
export type ButtonVariant = 
  | 'primary'     // Maps to: web="default", mobile="primary"
  | 'secondary'   // Maps to: web="secondary", mobile="secondary"  
  | 'outline'     // Maps to: web="outline", mobile="outline"
  | 'destructive' // Maps to: web="destructive", mobile="danger"
  | 'ghost'       // Maps to: web="ghost", mobile="icon"
  | 'link';       // Maps to: web="link", mobile="icon"

export type ButtonSize = 'small' | 'medium' | 'large';

/**
 * Unified button props interface
 * Platform-specific implementations will extend this
 */
export interface BaseButtonProps {
  onPress?: () => void;
  onClick?: () => void;
  title?: string;
  variant?: ButtonVariant;
  size?: ButtonSize;
  disabled?: boolean;
  children?: ReactNode;
  loading?: boolean;
  icon?: string | ReactNode;
  iconRight?: string | ReactNode;
}

/**
 * Platform-specific button props
 */
export interface WebButtonProps extends BaseButtonProps {
  onClick: () => void;
  className?: string;
  type?: 'button' | 'submit' | 'reset';
  asChild?: boolean;
}

export interface MobileButtonProps extends BaseButtonProps {
  onPress: () => void;
  style?: any; // React Native ViewStyle
  textStyle?: any; // React Native TextStyle
}

/**
 * Variant mapping utilities
 */
export const BUTTON_VARIANT_MAP = {
  web: {
    primary: 'default',
    secondary: 'secondary',
    outline: 'outline',
    destructive: 'destructive',
    ghost: 'ghost',
    link: 'link',
  },
  mobile: {
    primary: 'primary',
    secondary: 'secondary',
    outline: 'outline',
    destructive: 'danger',
    ghost: 'icon',
    link: 'icon',
  },
} as const;

/**
 * Size mapping utilities
 */
export const BUTTON_SIZE_MAP = {
  web: {
    small: 'sm',
    medium: 'default',
    large: 'lg',
  },
  mobile: {
    small: 'small',
    medium: 'medium',
    large: 'large',
  },
} as const;
