import { ReactNode } from 'react';

/**
 * Unified form input types
 */
export type InputType = 
  | 'text'
  | 'email'
  | 'password'
  | 'number'
  | 'tel'
  | 'url'
  | 'search'
  | 'textarea';

/**
 * Input size variants
 */
export type InputSize = 'small' | 'medium' | 'large';

/**
 * Input state variants
 */
export type InputState = 'default' | 'error' | 'success' | 'disabled';

/**
 * Base input props interface
 */
export interface BaseInputProps {
  value?: string;
  defaultValue?: string;
  placeholder?: string;
  type?: InputType;
  size?: InputSize;
  state?: InputState;
  disabled?: boolean;
  required?: boolean;
  readOnly?: boolean;
  autoFocus?: boolean;
  maxLength?: number;
  multiline?: boolean;
  numberOfLines?: number;
  label?: string;
  helperText?: string;
  errorText?: string;
  leftIcon?: string | ReactNode;
  rightIcon?: string | ReactNode;
  onChangeText?: (text: string) => void;
  onChange?: (event: any) => void;
  onFocus?: () => void;
  onBlur?: () => void;
  onSubmit?: () => void;
}

/**
 * Platform-specific input props
 */
export interface WebInputProps extends BaseInputProps {
  className?: string;
  name?: string;
  id?: string;
  autoComplete?: string;
}

export interface MobileInputProps extends BaseInputProps {
  style?: any; // React Native ViewStyle
  textStyle?: any; // React Native TextStyle
  keyboardType?: 'default' | 'numeric' | 'email-address' | 'phone-pad' | 'url';
  returnKeyType?: 'done' | 'go' | 'next' | 'search' | 'send';
  secureTextEntry?: boolean;
  autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters';
  autoCorrect?: boolean;
}

/**
 * Form field wrapper props
 */
export interface BaseFormFieldProps {
  children: ReactNode;
  label?: string;
  helperText?: string;
  errorText?: string;
  required?: boolean;
  disabled?: boolean;
}

export interface WebFormFieldProps extends BaseFormFieldProps {
  className?: string;
  htmlFor?: string;
}

export interface MobileFormFieldProps extends BaseFormFieldProps {
  style?: any; // React Native ViewStyle
}

/**
 * Select/Dropdown props
 */
export interface BaseSelectProps {
  value?: string | string[];
  defaultValue?: string | string[];
  placeholder?: string;
  multiple?: boolean;
  disabled?: boolean;
  required?: boolean;
  options: Array<{
    label: string;
    value: string;
    disabled?: boolean;
  }>;
  onValueChange?: (value: string | string[]) => void;
  onChange?: (event: any) => void;
}

export interface WebSelectProps extends BaseSelectProps {
  className?: string;
  name?: string;
  id?: string;
}

export interface MobileSelectProps extends BaseSelectProps {
  style?: any; // React Native ViewStyle
}

/**
 * Switch/Toggle props
 */
export interface BaseSwitchProps {
  checked?: boolean;
  defaultChecked?: boolean;
  disabled?: boolean;
  required?: boolean;
  label?: string;
  onCheckedChange?: (checked: boolean) => void;
  onChange?: (event: any) => void;
}

export interface WebSwitchProps extends BaseSwitchProps {
  className?: string;
  name?: string;
  id?: string;
}

export interface MobileSwitchProps extends BaseSwitchProps {
  style?: any; // React Native ViewStyle
}
