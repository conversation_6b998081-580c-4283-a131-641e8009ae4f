import { ReactNode } from 'react';

/**
 * Unified typography variant system
 * Works across web and mobile platforms
 */
export type TypographyVariant = 
  | 'h1'        // Large heading
  | 'h2'        // Medium heading  
  | 'h3'        // Small heading
  | 'h4'        // Extra small heading
  | 'body'      // Regular body text
  | 'bodySmall' // Small body text
  | 'label'     // Form labels, emphasized text
  | 'caption'   // Small captions, metadata
  | 'code';     // Code/monospace text

/**
 * Unified color system
 */
export type TypographyColor = 
  | 'primary'     // Main text color
  | 'secondary'   // Muted text color
  | 'tertiary'    // Very muted text color
  | 'white'       // White text (for dark backgrounds)
  | 'success'     // Success/positive color
  | 'error'       // Error/negative color
  | 'warning'     // Warning color
  | 'info';       // Info/neutral color

/**
 * Typography weight options
 */
export type TypographyWeight = 
  | 'normal'    // 400
  | 'medium'    // 500
  | 'semibold'  // 600
  | 'bold';     // 700

/**
 * Base typography props interface
 */
export interface BaseTypographyProps {
  children: ReactNode;
  variant?: TypographyVariant;
  color?: TypographyColor;
  weight?: TypographyWeight;
  center?: boolean;
  numberOfLines?: number;
  ellipsizeMode?: 'head' | 'middle' | 'tail' | 'clip';
}

/**
 * Platform-specific typography props
 */
export interface WebTypographyProps extends BaseTypographyProps {
  className?: string;
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'p' | 'span' | 'div';
}

export interface MobileTypographyProps extends BaseTypographyProps {
  style?: any; // React Native TextStyle
}

/**
 * Typography size and weight mapping
 */
export const TYPOGRAPHY_STYLES = {
  h1: {
    fontSize: { web: '2rem', mobile: 28 },      // 32px / 28px
    lineHeight: { web: '2.5rem', mobile: 34 },  // 40px / 34px
    fontWeight: 'bold' as const,
  },
  h2: {
    fontSize: { web: '1.5rem', mobile: 24 },    // 24px / 24px
    lineHeight: { web: '2rem', mobile: 32 },    // 32px / 32px
    fontWeight: 'bold' as const,
  },
  h3: {
    fontSize: { web: '1.25rem', mobile: 20 },   // 20px / 20px
    lineHeight: { web: '1.75rem', mobile: 28 }, // 28px / 28px
    fontWeight: 'semibold' as const,
  },
  h4: {
    fontSize: { web: '1.125rem', mobile: 18 },  // 18px / 18px
    lineHeight: { web: '1.5rem', mobile: 24 },  // 24px / 24px
    fontWeight: 'semibold' as const,
  },
  body: {
    fontSize: { web: '1rem', mobile: 16 },      // 16px / 16px
    lineHeight: { web: '1.5rem', mobile: 22 },  // 24px / 22px
    fontWeight: 'normal' as const,
  },
  bodySmall: {
    fontSize: { web: '0.875rem', mobile: 14 },  // 14px / 14px
    lineHeight: { web: '1.25rem', mobile: 20 }, // 20px / 20px
    fontWeight: 'normal' as const,
  },
  label: {
    fontSize: { web: '0.875rem', mobile: 14 },  // 14px / 14px
    lineHeight: { web: '1.25rem', mobile: 20 }, // 20px / 20px
    fontWeight: 'medium' as const,
  },
  caption: {
    fontSize: { web: '0.75rem', mobile: 12 },   // 12px / 12px
    lineHeight: { web: '1rem', mobile: 16 },    // 16px / 16px
    fontWeight: 'normal' as const,
  },
  code: {
    fontSize: { web: '0.875rem', mobile: 14 },  // 14px / 14px
    lineHeight: { web: '1.25rem', mobile: 20 }, // 20px / 20px
    fontWeight: 'normal' as const,
    fontFamily: { web: 'monospace', mobile: 'Courier' },
  },
} as const;
