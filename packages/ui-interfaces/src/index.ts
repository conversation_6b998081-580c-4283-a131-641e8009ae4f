// Main exports for @repo/ui-interfaces package

// Button interfaces and utilities
export * from './button';

// Typography interfaces and utilities  
export * from './typography';

// Form component interfaces
export * from './form';

// Layout component interfaces
export * from './layout';

/**
 * Common color system used across platforms
 */
export const UI_COLORS = {
  // Primary brand colors
  primary: {
    50: '#eff6ff',
    100: '#dbeafe', 
    500: '#3b82f6',
    600: '#2563eb',
    900: '#1e3a8a',
  },
  
  // Semantic colors
  success: {
    50: '#f0fdf4',
    100: '#dcfce7',
    500: '#22c55e',
    600: '#16a34a',
    900: '#14532d',
  },
  
  error: {
    50: '#fef2f2',
    100: '#fee2e2',
    500: '#ef4444',
    600: '#dc2626',
    900: '#7f1d1d',
  },
  
  warning: {
    50: '#fffbeb',
    100: '#fef3c7',
    500: '#f59e0b',
    600: '#d97706',
    900: '#78350f',
  },
  
  // Neutral colors
  gray: {
    50: '#f9fafb',
    100: '#f3f4f6',
    200: '#e5e7eb',
    300: '#d1d5db',
    400: '#9ca3af',
    500: '#6b7280',
    600: '#4b5563',
    700: '#374151',
    800: '#1f2937',
    900: '#111827',
  },
} as const;

/**
 * Platform-specific color mappings
 */
export const PLATFORM_COLORS = {
  web: {
    primary: 'hsl(var(--primary))',
    secondary: 'hsl(var(--secondary))',
    background: 'hsl(var(--background))',
    foreground: 'hsl(var(--foreground))',
    muted: 'hsl(var(--muted))',
    accent: 'hsl(var(--accent))',
    destructive: 'hsl(var(--destructive))',
    border: 'hsl(var(--border))',
  },
  mobile: {
    primary: '#3b82f6',
    secondary: '#6b7280',
    background: '#ffffff',
    foreground: '#111827',
    muted: '#f3f4f6',
    accent: '#f59e0b',
    destructive: '#ef4444',
    border: '#e5e7eb',
  },
} as const;
