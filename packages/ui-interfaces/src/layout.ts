import { ReactNode } from 'react';

/**
 * Spacing system - consistent across platforms
 */
export type Spacing = 
  | 'none'    // 0
  | 'xs'      // 4px
  | 'sm'      // 8px  
  | 'md'      // 16px
  | 'lg'      // 24px
  | 'xl'      // 32px
  | '2xl'     // 48px
  | '3xl';    // 64px

/**
 * Border radius system
 */
export type BorderRadius = 
  | 'none'    // 0
  | 'sm'      // 4px
  | 'md'      // 8px
  | 'lg'      // 12px
  | 'xl'      // 16px
  | 'full';   // 9999px

/**
 * Shadow/elevation system
 */
export type Shadow = 
  | 'none'
  | 'sm'
  | 'md'
  | 'lg'
  | 'xl';

/**
 * Flex direction
 */
export type FlexDirection = 'row' | 'column' | 'row-reverse' | 'column-reverse';

/**
 * Flex alignment
 */
export type FlexAlign = 'start' | 'center' | 'end' | 'stretch';
export type FlexJustify = 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly';

/**
 * Base container props
 */
export interface BaseContainerProps {
  children: ReactNode;
  padding?: Spacing;
  paddingX?: Spacing;
  paddingY?: Spacing;
  margin?: Spacing;
  marginX?: Spacing;
  marginY?: Spacing;
  borderRadius?: BorderRadius;
  shadow?: Shadow;
  backgroundColor?: string;
  borderColor?: string;
  borderWidth?: number;
}

/**
 * Platform-specific container props
 */
export interface WebContainerProps extends BaseContainerProps {
  className?: string;
  as?: 'div' | 'section' | 'article' | 'main' | 'aside' | 'header' | 'footer';
}

export interface MobileContainerProps extends BaseContainerProps {
  style?: any; // React Native ViewStyle
}

/**
 * Flex container props
 */
export interface BaseFlexProps extends BaseContainerProps {
  direction?: FlexDirection;
  align?: FlexAlign;
  justify?: FlexJustify;
  wrap?: boolean;
  gap?: Spacing;
  flex?: number;
}

export interface WebFlexProps extends BaseFlexProps {
  className?: string;
  as?: 'div' | 'section' | 'article' | 'main' | 'aside' | 'header' | 'footer';
}

export interface MobileFlexProps extends BaseFlexProps {
  style?: any; // React Native ViewStyle
}

/**
 * Grid props (web-specific, mobile uses flex)
 */
export interface WebGridProps extends BaseContainerProps {
  className?: string;
  columns?: number | string;
  rows?: number | string;
  gap?: Spacing;
  columnGap?: Spacing;
  rowGap?: Spacing;
}

/**
 * Card props
 */
export interface BaseCardProps extends BaseContainerProps {
  header?: ReactNode;
  footer?: ReactNode;
  hoverable?: boolean;
  clickable?: boolean;
  onPress?: () => void;
  onClick?: () => void;
}

export interface WebCardProps extends BaseCardProps {
  className?: string;
}

export interface MobileCardProps extends BaseCardProps {
  style?: any; // React Native ViewStyle
}

/**
 * Spacing value mapping
 */
export const SPACING_VALUES = {
  web: {
    none: '0',
    xs: '0.25rem',   // 4px
    sm: '0.5rem',    // 8px
    md: '1rem',      // 16px
    lg: '1.5rem',    // 24px
    xl: '2rem',      // 32px
    '2xl': '3rem',   // 48px
    '3xl': '4rem',   // 64px
  },
  mobile: {
    none: 0,
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    '2xl': 48,
    '3xl': 64,
  },
} as const;

/**
 * Border radius value mapping
 */
export const BORDER_RADIUS_VALUES = {
  web: {
    none: '0',
    sm: '0.25rem',   // 4px
    md: '0.5rem',    // 8px
    lg: '0.75rem',   // 12px
    xl: '1rem',      // 16px
    full: '9999px',
  },
  mobile: {
    none: 0,
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
    full: 9999,
  },
} as const;
