{"name": "@repo/ui-interfaces", "version": "0.0.0", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "exports": {".": "./src/index.ts", "./button": "./src/button.ts", "./typography": "./src/typography.ts", "./form": "./src/form.ts", "./layout": "./src/layout.ts"}, "scripts": {"lint": "eslint .", "type-check": "tsc --noEmit"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "eslint": "catalog:", "typescript": "catalog:", "@types/react": "catalog:"}, "dependencies": {"react": "catalog:"}}