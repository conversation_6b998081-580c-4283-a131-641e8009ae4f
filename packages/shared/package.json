{"name": "@repo/shared", "version": "0.0.0", "private": true, "main": "src/index.ts", "types": "src/index.ts", "scripts": {"lint": "eslint ."}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "catalog:", "eslint": "catalog:", "typescript": "catalog:"}, "exports": {".": "./src/index.ts", "./lib/*": "./src/lib/*.ts", "./utils/*": "./src/utils/*.ts"}}