import { z } from 'zod';
import { BaseEntitySchema, EntityIdSchema } from '../common';

// Payment method enum
export const PaymentMethodSchema = z.enum([
  'bank_transfer',
  'credit_card',
  'debit_card',
  'cash',
  'check',
  'paypal',
  'stripe',
  'wire_transfer',
  'online_banking',
  'mobile_payment',
  'cryptocurrency',
  'other',
]);

export type PaymentMethod = z.infer<typeof PaymentMethodSchema>;

// Payment status enum
export const PaymentStatusSchema = z.enum([
  'pending',      // Payment initiated but not confirmed
  'completed',    // Payment successfully processed
  'failed',       // Payment failed
  'cancelled',    // Payment cancelled
  'refunded',     // Payment refunded (partial or full)
  'disputed',     // Payment disputed
]);

export type PaymentStatus = z.infer<typeof PaymentStatusSchema>;

// Payment type enum
export const PaymentTypeSchema = z.enum([
  'payment',      // Regular payment toward invoice
  'refund',       // Refund of previous payment
  'adjustment',   // Manual adjustment
  'fee',          // Late fee or processing fee
  'discount',     // Applied discount
]);

export type PaymentType = z.infer<typeof PaymentTypeSchema>;

// Payment record schema
export const PaymentSchema = BaseEntitySchema.extend({
  // Basic information
  invoiceId: EntityIdSchema,
  organizationId: EntityIdSchema,
  
  // Payment details
  amount: z.number().min(0, 'Payment amount must be positive'),
  method: PaymentMethodSchema,
  type: PaymentTypeSchema.default('payment'),
  status: PaymentStatusSchema.default('completed'),
  
  // Dates
  paymentDate: z.date(),
  processedAt: z.date().optional(),
  
  // Transaction details
  transactionId: z.string().optional(),
  reference: z.string().optional(),
  notes: z.string().optional(),
  
  // Refund information (if applicable)
  refundedPaymentId: EntityIdSchema.optional(), // If this is a refund, reference to original payment
  refundReason: z.string().optional(),
  
  // Fee information
  processingFee: z.number().min(0).optional(),
  
  // External references
  externalTransactionId: z.string().optional(), // Stripe charge ID, PayPal transaction ID, etc.
  gatewayResponse: z.record(z.any()).optional(), // Raw gateway response for debugging
  
  // Metadata
  clientNotified: z.boolean().default(false), // Whether client was notified of this payment
  receiptSent: z.boolean().default(false),     // Whether receipt was sent
  attachments: z.array(z.object({
    id: EntityIdSchema,
    name: z.string(),
    type: z.string(),
    uri: z.string(),
    size: z.number(),
    uploadedAt: z.date(),
  })).default([]), // Receipt attachments, bank confirmations, etc.
});

export type Payment = z.infer<typeof PaymentSchema>;

// Payment creation input
export const CreatePaymentSchema = PaymentSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  status: true, // Will be set based on processing
  processedAt: true,
});

export type CreatePayment = z.infer<typeof CreatePaymentSchema>;

// Payment summary for invoice display
export const PaymentSummarySchema = z.object({
  totalPaid: z.number(),
  totalRefunded: z.number(),
  netPaid: z.number(), // totalPaid - totalRefunded
  lastPaymentDate: z.date().optional(),
  lastPaymentAmount: z.number().optional(),
  lastPaymentMethod: PaymentMethodSchema.optional(),
  paymentCount: z.number(),
  refundCount: z.number(),
});

export type PaymentSummary = z.infer<typeof PaymentSummarySchema>;

// Invoice payment status (enhanced)
export const InvoicePaymentStatusSchema = z.enum([
  'unpaid',           // No payments received
  'partially_paid',   // Some payments received, balance remaining
  'paid',            // Fully paid
  'overpaid',        // Payments exceed invoice total
  'refunded',        // Fully refunded
  'partially_refunded', // Some payments refunded
]);

export type InvoicePaymentStatus = z.infer<typeof InvoicePaymentStatusSchema>; 