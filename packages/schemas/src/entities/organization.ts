import { z } from 'zod';
import { BaseEntitySchema, BaseContactSchema } from '../common';

// Organization contact (extends base contact with address)
export const OrganizationContactSchema = BaseContactSchema.extend({
  address: z.string().optional(),
});
export type OrganizationContact = z.infer<typeof OrganizationContactSchema>;

// Organization branding
export const OrganizationBrandingSchema = z.object({
  logo: z.string().optional(), // URL or base64
  primaryColor: z.string().optional(),
  secondaryColor: z.string().optional(),
  font: z.string().optional(),
});
export type OrganizationBranding = z.infer<typeof OrganizationBrandingSchema>;

// Organization settings
export const OrganizationSettingsSchema = z.object({
  currency: z.string().default('USD'),
  dateFormat: z.string().default('MM/DD/YYYY'),
  timezone: z.string().default('UTC'),
  fiscalYearStart: z.string().default('01/01'), // MM/DD format
  invoicePrefix: z.string().default('INV'),
  defaultPaymentTerms: z.string().default('Net 30'),
});
export type OrganizationSettings = z.infer<typeof OrganizationSettingsSchema>;

// Organization entity
export const OrganizationSchema = BaseEntitySchema.extend({
  name: z.string().min(1, 'Organization name is required'),
  nickname: z.string().min(1, 'Organization nickname is required'),
  description: z.string().optional(),
  contact: OrganizationContactSchema,
  branding: OrganizationBrandingSchema.optional(),
  logo: z.string().nullable().optional(), // URL or base64 for organization logo
  settings: OrganizationSettingsSchema,
  isActive: z.boolean().default(true),
  // Business registration
  businessNumber: z.string().optional(),
  taxNumber: z.string().optional(),
  registrationNumber: z.string().optional(),
});
export type Organization = z.infer<typeof OrganizationSchema>;
