{"name": "@repo/schemas", "version": "0.0.0", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "exports": {".": "./src/index.ts", "./entities": "./src/entities/index.ts", "./common": "./src/common/index.ts", "./forms": "./src/forms/index.ts"}, "scripts": {"lint": "eslint .", "type-check": "tsc --noEmit"}, "dependencies": {"zod": "^3.25.42"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "eslint": "catalog:", "typescript": "catalog:"}}