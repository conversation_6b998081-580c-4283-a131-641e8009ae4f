# @repo/schemas

This package contains all shared Zod schemas and TypeScript types for the InvoiceGo monorepo. It provides type-safe validation and consistent data structures across all applications.

## 📦 Installation

```bash
pnpm add @repo/schemas
```

## 🏗️ Architecture

The schemas package is organized into logical modules:

```
src/
├── entities/     # Business entity schemas (User, Client, Invoice, etc.)
├── common/       # Shared types and utilities (Currency, BaseEntity, etc.)
├── forms/        # Form input validation schemas
└── index.ts      # Main export file
```

## 🔧 Usage

### Entity Schemas

```typescript
import { ClientSchema, InvoiceSchema, OrganizationSchema } from '@repo/schemas';

// Validate client data
const clientData = {
  name: "ACME Corp",
  email: "<EMAIL>",
  // ... other fields
};

const validatedClient = ClientSchema.parse(clientData);
```

### Common Types

```typescript
import { Currency, BaseContact, BaseAddress } from '@repo/schemas';

// Use common types across applications
const currency: Currency = {
  code: 'USD',
  symbol: '$',
  name: 'US Dollar',
  decimalPlaces: 2,
  symbolPosition: 'before'
};
```

### Form Validation

```typescript
import { CreateClientFormSchema, CreateInvoiceFormSchema } from '@repo/schemas';

// Validate form inputs
const formData = CreateClientFormSchema.parse({
  name: "New Client",
  email: "<EMAIL>"
});
```

## 📋 Available Schemas

### Core Entities
- `ClientSchema` - Client/customer data
- `InvoiceSchema` - Invoice data with line items
- `ServiceSchema` - Service offerings
- `OrganizationSchema` - Company/organization data
- `UserSchema` - User account data

### Common Types
- `Currency` - Currency configuration
- `BaseContact` - Contact information (email, phone, website)
- `BaseAddress` - Address information
- `BaseEntity` - Common entity fields (id, createdAt, updatedAt)

### Form Schemas
- `CreateClientFormSchema` - Client creation form
- `CreateInvoiceFormSchema` - Invoice creation form
- `CreateServiceFormSchema` - Service creation form
- `UpdateClientFormSchema` - Client update form
- `UpdateInvoiceFormSchema` - Invoice update form

## 🎯 Benefits

1. **Type Safety** - Runtime validation with TypeScript types
2. **Consistency** - Shared schemas across all applications
3. **Validation** - Built-in Zod validation with error messages
4. **Maintainability** - Single source of truth for data structures
5. **Extensibility** - Easy to extend schemas for new applications

## 🔄 Schema Evolution

When updating schemas:

1. Add new optional fields first
2. Use `.extend()` for backwards compatibility
3. Update all consuming applications
4. Remove deprecated fields in separate release

```typescript
// Example of schema evolution
const NewClientSchema = ClientSchema.extend({
  newField: z.string().optional(), // Add as optional first
});
```

## 🧪 Testing

All schemas include comprehensive validation tests. Run tests with:

```bash
pnpm test
```

## 📚 Related Packages

- `@repo/dtos` - API request/response types
- `@repo/utils` - Utility functions for working with schemas
- `@repo/constants` - Shared constants and enums 