{"name": "@repo/ui", "version": "0.0.0", "private": true, "scripts": {"ui": "pnpm dlx shadcn@latest", "lint": "eslint ."}, "peerDependencies": {"react": "catalog:"}, "peerDependenciesMeta": {"react-native": {"optional": true}}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "catalog:", "@types/react": "catalog:", "autoprefixer": "^10", "postcss": "^8", "postcss-load-config": "^6", "tailwindcss": "catalog:", "typescript": "catalog:"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "lucide-react": "catalog:", "react-hook-form": "^7.57.0", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.42"}, "exports": {".": "./src/index.ts", "./globals.css": "./src/globals.css", "./postcss.config": "./postcss.config.mjs", "./tailwind.config": "./tailwind.config.ts", "./lib/*": "./src/lib/*.ts", "./hooks/*": ["./src/hooks/*.ts", "./src/hooks/*.tsx"], "./components/*": "./src/components/*.tsx"}}