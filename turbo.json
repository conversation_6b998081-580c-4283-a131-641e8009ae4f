{"$schema": "https://turbo.build/schema.json", "ui": "tui", "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": [".next/**", "!.next/cache/**", "dist/**"]}, "lint": {"dependsOn": ["^lint"]}, "dev": {"cache": false, "persistent": true}, "mobile:dev": {"cache": false, "persistent": true}, "mobile:android": {"cache": false, "persistent": true}, "mobile:ios": {"cache": false, "persistent": true}}}