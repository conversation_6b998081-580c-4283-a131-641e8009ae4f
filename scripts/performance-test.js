#!/usr/bin/env node

/**
 * Performance testing script for monorepo packages
 * Tests build times, bundle sizes, and runtime performance
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Starting Performance Tests for InvoiceGo Monorepo\n');

const packages = [
  '@repo/schemas',
  '@repo/stores', 
  '@repo/queries',
  '@repo/utils',
  '@repo/constants',
  '@repo/dtos',
  '@repo/ui-interfaces'
];

const apps = [
  'mobile'
];

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function measureBuildTime(packageName, buildCommand = 'pnpm build') {
  console.log(`📦 Testing ${packageName}...`);
  
  const startTime = Date.now();
  
  try {
    execSync(`pnpm --filter ${packageName} ${buildCommand}`, { 
      stdio: 'pipe',
      cwd: process.cwd()
    });
    
    const endTime = Date.now();
    const buildTime = endTime - startTime;
    
    console.log(`  ✅ Build time: ${buildTime}ms`);
    return { success: true, buildTime };
  } catch (error) {
    console.log(`  ❌ Build failed: ${error.message}`);
    return { success: false, buildTime: -1 };
  }
}

function analyzeBundleSize(packagePath) {
  const distPath = path.join(packagePath, 'dist');
  const srcPath = path.join(packagePath, 'src');
  
  let distSize = 0;
  let srcSize = 0;
  
  if (fs.existsSync(distPath)) {
    distSize = getDirSize(distPath);
  }
  
  if (fs.existsSync(srcPath)) {
    srcSize = getDirSize(srcPath);
  }
  
  return { distSize, srcSize };
}

function getDirSize(dirPath) {
  let size = 0;
  
  function calculateSize(currentPath) {
    const stats = fs.statSync(currentPath);
    
    if (stats.isFile()) {
      size += stats.size;
    } else if (stats.isDirectory()) {
      const files = fs.readdirSync(currentPath);
      files.forEach(file => {
        calculateSize(path.join(currentPath, file));
      });
    }
  }
  
  try {
    calculateSize(dirPath);
  } catch (error) {
    // Directory doesn't exist or permission error
  }
  
  return size;
}

async function runPerformanceTests() {
  console.log('📊 Package Performance Analysis\n');
  console.log('='.repeat(50));
  
  const results = {
    packages: {},
    apps: {},
    summary: {
      totalBuildTime: 0,
      successfulBuilds: 0,
      failedBuilds: 0,
      totalBundleSize: 0
    }
  };
  
  // Test packages
  console.log('\n📦 Testing Packages...\n');
  
  for (const packageName of packages) {
    const packagePath = path.join('packages', packageName.replace('@repo/', ''));
    
    // Build performance
    const buildResult = measureBuildTime(packageName, 'tsc --noEmit');
    
    // Bundle size analysis
    const bundleAnalysis = analyzeBundleSize(packagePath);
    
    // Type checking performance
    const typeCheckStart = Date.now();
    let typeCheckTime = 0;
    
    try {
      execSync(`pnpm --filter ${packageName} tsc --noEmit`, { 
        stdio: 'pipe',
        cwd: process.cwd()
      });
      typeCheckTime = Date.now() - typeCheckStart;
      console.log(`  ✅ Type check: ${typeCheckTime}ms`);
    } catch (error) {
      console.log(`  ❌ Type check failed`);
    }
    
    console.log(`  📊 Source size: ${formatBytes(bundleAnalysis.srcSize)}`);
    console.log(`  📦 Bundle size: ${formatBytes(bundleAnalysis.distSize)}\n`);
    
    results.packages[packageName] = {
      buildTime: buildResult.buildTime,
      typeCheckTime,
      srcSize: bundleAnalysis.srcSize,
      distSize: bundleAnalysis.distSize,
      success: buildResult.success
    };
    
    if (buildResult.success) {
      results.summary.successfulBuilds++;
      results.summary.totalBuildTime += buildResult.buildTime;
    } else {
      results.summary.failedBuilds++;
    }
    
    results.summary.totalBundleSize += bundleAnalysis.distSize;
  }
  
  // Test apps
  console.log('\n📱 Testing Applications...\n');
  
  for (const appName of apps) {
    console.log(`📱 Testing ${appName} app...`);
    
    const appPath = path.join('apps', appName);
    
    // Type checking performance
    const typeCheckStart = Date.now();
    let typeCheckTime = 0;
    
    try {
      execSync(`pnpm --filter ${appName} tsc --noEmit --skipLibCheck`, { 
        stdio: 'pipe',
        cwd: process.cwd()
      });
      typeCheckTime = Date.now() - typeCheckStart;
      console.log(`  ✅ Type check: ${typeCheckTime}ms`);
    } catch (error) {
      console.log(`  ❌ Type check failed`);
    }
    
    // Bundle analysis
    const bundleAnalysis = analyzeBundleSize(appPath);
    console.log(`  📊 Source size: ${formatBytes(bundleAnalysis.srcSize)}`);
    
    results.apps[appName] = {
      typeCheckTime,
      srcSize: bundleAnalysis.srcSize,
    };
    
    console.log('');
  }
  
  // Performance summary
  console.log('📊 Performance Summary\n');
  console.log('='.repeat(50));
  console.log(`✅ Successful builds: ${results.summary.successfulBuilds}/${packages.length}`);
  console.log(`❌ Failed builds: ${results.summary.failedBuilds}/${packages.length}`);
  console.log(`⏱️  Total build time: ${results.summary.totalBuildTime}ms`);
  console.log(`📦 Total bundle size: ${formatBytes(results.summary.totalBundleSize)}`);
  console.log(`⚡ Average build time: ${Math.round(results.summary.totalBuildTime / results.summary.successfulBuilds)}ms`);
  
  // Performance benchmarks
  console.log('\n🎯 Performance Benchmarks\n');
  console.log('='.repeat(50));
  
  const benchmarks = {
    buildTime: { good: 2000, acceptable: 5000 }, // ms
    bundleSize: { good: 100 * 1024, acceptable: 500 * 1024 }, // bytes
    typeCheckTime: { good: 1000, acceptable: 3000 } // ms
  };
  
  let allGood = true;
  
  // Check build times
  const avgBuildTime = results.summary.totalBuildTime / results.summary.successfulBuilds;
  if (avgBuildTime <= benchmarks.buildTime.good) {
    console.log('✅ Build time: EXCELLENT');
  } else if (avgBuildTime <= benchmarks.buildTime.acceptable) {
    console.log('⚠️  Build time: ACCEPTABLE');
  } else {
    console.log('❌ Build time: NEEDS IMPROVEMENT');
    allGood = false;
  }
  
  // Check bundle sizes
  const avgBundleSize = results.summary.totalBundleSize / packages.length;
  if (avgBundleSize <= benchmarks.bundleSize.good) {
    console.log('✅ Bundle size: EXCELLENT');
  } else if (avgBundleSize <= benchmarks.bundleSize.acceptable) {
    console.log('⚠️  Bundle size: ACCEPTABLE');
  } else {
    console.log('❌ Bundle size: NEEDS IMPROVEMENT');
    allGood = false;
  }
  
  // Overall assessment
  console.log('\n🏆 Overall Performance Assessment\n');
  console.log('='.repeat(50));
  
  if (allGood && results.summary.failedBuilds === 0) {
    console.log('🎉 EXCELLENT: All packages perform within benchmarks!');
  } else if (results.summary.failedBuilds === 0) {
    console.log('✅ GOOD: All packages build successfully with acceptable performance');
  } else {
    console.log('⚠️  WARNING: Some packages have build or performance issues');
  }
  
  // Save results
  fs.writeFileSync(
    'performance-results.json', 
    JSON.stringify(results, null, 2)
  );
  
  console.log('\n📄 Detailed results saved to performance-results.json');
  
  return results;
}

// Run the tests
runPerformanceTests().catch(console.error); 