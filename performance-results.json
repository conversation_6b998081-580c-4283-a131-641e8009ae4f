{"packages": {"@repo/schemas": {"buildTime": 268, "typeCheckTime": 267, "srcSize": 15351, "distSize": 0, "success": true}, "@repo/stores": {"buildTime": 274, "typeCheckTime": 271, "srcSize": 31411, "distSize": 0, "success": true}, "@repo/queries": {"buildTime": 266, "typeCheckTime": 268, "srcSize": 18110, "distSize": 0, "success": true}, "@repo/utils": {"buildTime": 269, "typeCheckTime": 268, "srcSize": 12896, "distSize": 0, "success": true}, "@repo/constants": {"buildTime": 269, "typeCheckTime": 269, "srcSize": 11223, "distSize": 0, "success": true}, "@repo/dtos": {"buildTime": 265, "typeCheckTime": 269, "srcSize": 3357, "distSize": 0, "success": true}, "@repo/ui-interfaces": {"buildTime": 269, "typeCheckTime": 265, "srcSize": 13800, "distSize": 0, "success": true}}, "apps": {"mobile": {"typeCheckTime": 269, "srcSize": 0}}, "summary": {"totalBuildTime": 1880, "successfulBuilds": 7, "failedBuilds": 0, "totalBundleSize": 0}}